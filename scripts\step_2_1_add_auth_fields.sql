-- ===== 🔐 الخطوة 2.1: إضافة حقول المصادقة المطلوبة =====
-- تاريخ التشغيل: 2025-07-20
-- الهدف: تحويل public.users ليصبح جدول مصادقة كامل

-- 1. إ<PERSON><PERSON><PERSON>ة حقول المصادقة المفقودة
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS password_hash TEXT,
ADD COLUMN IF NOT EXISTS password_salt TEXT,
ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS locked_until TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS email_verification_token TEXT,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS password_reset_token TEXT,
ADD COLUMN IF NOT EXISTS password_reset_expires TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS two_factor_secret TEXT,
ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT false;

-- 2. إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_national_id ON public.users(national_id);
CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON public.users(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON public.users(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_login_attempts ON public.users(login_attempts);

-- 3. إنشاء جدول جلسات المصادقة
CREATE TABLE IF NOT EXISTS public.auth_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    refresh_token TEXT UNIQUE,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    is_active BOOLEAN DEFAULT true
);

-- فهارس جدول الجلسات
CREATE INDEX IF NOT EXISTS idx_auth_sessions_user_id ON public.auth_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_session_token ON public.auth_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_expires_at ON public.auth_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_is_active ON public.auth_sessions(is_active);

-- 4. إنشاء جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS public.auth_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT,
    national_id TEXT,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT false,
    failure_reason TEXT,
    attempted_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس جدول محاولات تسجيل الدخول
CREATE INDEX IF NOT EXISTS idx_auth_login_attempts_email ON public.auth_login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_auth_login_attempts_national_id ON public.auth_login_attempts(national_id);
CREATE INDEX IF NOT EXISTS idx_auth_login_attempts_ip_address ON public.auth_login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_auth_login_attempts_attempted_at ON public.auth_login_attempts(attempted_at);

-- 5. إنشاء دالة تشفير كلمة المرور
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT AS $$
DECLARE
    salt TEXT;
    hashed TEXT;
BEGIN
    -- إنشاء salt عشوائي
    salt := encode(gen_random_bytes(32), 'base64');
    
    -- تشفير كلمة المرور مع salt
    hashed := encode(digest(password || salt, 'sha256'), 'base64');
    
    -- إرجاع salt:hash
    RETURN salt || ':' || hashed;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. إنشاء دالة التحقق من كلمة المرور
CREATE OR REPLACE FUNCTION verify_password(password TEXT, stored_hash TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    salt TEXT;
    hash TEXT;
    computed_hash TEXT;
BEGIN
    -- استخراج salt و hash
    salt := split_part(stored_hash, ':', 1);
    hash := split_part(stored_hash, ':', 2);
    
    -- حساب hash للكلمة المدخلة
    computed_hash := encode(digest(password || salt, 'sha256'), 'base64');
    
    -- مقارنة النتيجة
    RETURN computed_hash = hash;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. إنشاء دالة إنشاء جلسة جديدة
CREATE OR REPLACE FUNCTION create_auth_session(
    p_user_id UUID,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    session_token TEXT;
    refresh_token TEXT;
    expires_at TIMESTAMPTZ;
    session_id UUID;
    result JSON;
BEGIN
    -- إنشاء tokens
    session_token := encode(gen_random_bytes(32), 'base64');
    refresh_token := encode(gen_random_bytes(32), 'base64');
    expires_at := NOW() + INTERVAL '24 hours';
    
    -- إدراج الجلسة
    INSERT INTO public.auth_sessions (
        user_id,
        session_token,
        refresh_token,
        expires_at,
        ip_address,
        user_agent,
        device_info
    ) VALUES (
        p_user_id,
        session_token,
        refresh_token,
        expires_at,
        p_ip_address,
        p_user_agent,
        p_device_info
    ) RETURNING id INTO session_id;
    
    -- تحديث آخر تسجيل دخول للمستخدم
    UPDATE public.users 
    SET 
        last_sign_in_at = NOW(),
        last_seen = NOW(),
        is_online = true,
        current_session_id = session_id,
        total_sessions = total_sessions + 1,
        login_attempts = 0,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    result := json_build_object(
        'success', true,
        'session_id', session_id,
        'session_token', session_token,
        'refresh_token', refresh_token,
        'expires_at', expires_at,
        'message', 'تم إنشاء الجلسة بنجاح'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في إنشاء الجلسة'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. إنشاء دالة تسجيل الدخول
CREATE OR REPLACE FUNCTION authenticate_user(
    p_identifier TEXT, -- يمكن أن يكون email أو national_id
    p_password TEXT,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_device_info JSONB DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    user_record RECORD;
    session_result JSON;
    result JSON;
BEGIN
    -- البحث عن المستخدم
    SELECT * INTO user_record
    FROM public.users 
    WHERE (email = p_identifier OR national_id = p_identifier)
      AND is_active = true;
    
    -- تسجيل محاولة تسجيل الدخول
    INSERT INTO public.auth_login_attempts (
        email, national_id, ip_address, user_agent, success
    ) VALUES (
        CASE WHEN p_identifier LIKE '%@%' THEN p_identifier ELSE NULL END,
        CASE WHEN p_identifier NOT LIKE '%@%' THEN p_identifier ELSE NULL END,
        p_ip_address,
        p_user_agent,
        false -- سيتم تحديثها لاحقاً
    );
    
    -- فحص وجود المستخدم
    IF user_record.id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'user_not_found',
            'message', 'المستخدم غير موجود أو غير نشط'
        );
    END IF;
    
    -- فحص قفل الحساب
    IF user_record.locked_until IS NOT NULL AND user_record.locked_until > NOW() THEN
        RETURN json_build_object(
            'success', false,
            'error', 'account_locked',
            'message', 'الحساب مقفل مؤقتاً',
            'locked_until', user_record.locked_until
        );
    END IF;
    
    -- فحص كلمة المرور (إذا كانت موجودة)
    IF user_record.password_hash IS NOT NULL THEN
        IF NOT verify_password(p_password, user_record.password_hash) THEN
            -- زيادة محاولات تسجيل الدخول الفاشلة
            UPDATE public.users 
            SET 
                login_attempts = login_attempts + 1,
                locked_until = CASE 
                    WHEN login_attempts + 1 >= 5 THEN NOW() + INTERVAL '30 minutes'
                    ELSE NULL 
                END
            WHERE id = user_record.id;
            
            RETURN json_build_object(
                'success', false,
                'error', 'invalid_password',
                'message', 'كلمة المرور غير صحيحة'
            );
        END IF;
    ELSE
        -- إذا لم تكن كلمة المرور محفوظة، استخدم national_id ككلمة مرور افتراضية
        IF p_password != user_record.national_id THEN
            RETURN json_build_object(
                'success', false,
                'error', 'invalid_password',
                'message', 'كلمة المرور غير صحيحة (استخدم الرقم الوطني)'
            );
        END IF;
    END IF;
    
    -- إنشاء جلسة جديدة
    session_result := create_auth_session(
        user_record.id,
        p_ip_address,
        p_user_agent,
        p_device_info
    );
    
    -- تحديث محاولة تسجيل الدخول لتكون ناجحة
    UPDATE public.auth_login_attempts 
    SET success = true 
    WHERE email = p_identifier OR national_id = p_identifier
    ORDER BY attempted_at DESC 
    LIMIT 1;
    
    result := json_build_object(
        'success', true,
        'user_id', user_record.id,
        'user_data', row_to_json(user_record),
        'session', session_result,
        'message', 'تم تسجيل الدخول بنجاح'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في تسجيل الدخول'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. التحقق من إضافة الحقول
SELECT 
    '=== فحص الحقول الجديدة ===' as check_fields,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'users'
  AND column_name IN ('password_hash', 'password_salt', 'login_attempts', 'email_verified')
ORDER BY column_name;
