[{"table_schema": "public", "table_name": "devices", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "devices", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "locations", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "photos", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_activity_log", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "user_sessions", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "users", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "public", "table_name": "videos", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "buckets", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRUNCATE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRIGGER", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "REFERENCES", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "DELETE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "UPDATE", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "INSERT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "REFERENCES", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRUNCATE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "INSERT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "UPDATE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "DELETE", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRIGGER", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "objects", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "INSERT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "SELECT", "grantee": "anon"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "SELECT", "grantee": "authenticated"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "TRIGGER", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "REFERENCES", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "TRUNCATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "DELETE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "UPDATE", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "SELECT", "grantee": "service_role"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "privilege_type": "INSERT", "grantee": "service_role"}]