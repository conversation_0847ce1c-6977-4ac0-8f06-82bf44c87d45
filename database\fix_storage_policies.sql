-- ===== إصلاح Storage Policies لحل مشاكل الرفع =====

-- 1. حذف جميع الـ policies القديمة
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload videos" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update own videos" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete own videos" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update own files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete own files" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload photos" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update own photos" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete own photos" ON storage.objects;

-- 2. إنشاء policies مبسطة وفعالة

-- Policy للقراءة العامة (للجميع)
CREATE POLICY "Public read access" ON storage.objects
FOR SELECT TO public
USING (bucket_id IN ('photos', 'videos'));

-- Policy للرفع (للمستخدمين المصادق عليهم)
CREATE POLICY "Authenticated upload" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id IN ('photos', 'videos'));

-- Policy للتحديث (للمستخدمين المصادق عليهم)
CREATE POLICY "Authenticated update" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id IN ('photos', 'videos'));

-- Policy للحذف (للمستخدمين المصادق عليهم)
CREATE POLICY "Authenticated delete" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id IN ('photos', 'videos'));

-- 3. التأكد من إعدادات الـ buckets
UPDATE storage.buckets 
SET public = true, 
    file_size_limit = 104857600, -- 100MB
    allowed_mime_types = ARRAY[
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'
    ]
WHERE id = 'photos';

UPDATE storage.buckets 
SET public = true, 
    file_size_limit = 104857600, -- 100MB
    allowed_mime_types = ARRAY[
        'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm',
        'image/jpeg', 'image/png' -- fallback للصور
    ]
WHERE id = 'videos';

-- 4. إنشاء videos bucket إذا لم يكن موجود
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'videos',
    'videos',
    true,
    104857600, -- 100MB
    ARRAY['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 5. فحص النتائج
SELECT 'تم إصلاح Storage Policies بنجاح!' as result;

-- عرض الـ buckets
SELECT id, name, public, file_size_limit, allowed_mime_types 
FROM storage.buckets 
WHERE id IN ('photos', 'videos');

-- عرض الـ policies
SELECT schemaname, tablename, policyname, cmd, roles
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;

-- 6. اختبار الرفع (للتأكد من عمل الـ policies)
-- هذا سيظهر إذا كانت الـ policies تعمل
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE schemaname = 'storage' 
            AND tablename = 'objects' 
            AND cmd = 'INSERT'
        ) THEN 'Policies موجودة ✅'
        ELSE 'Policies مفقودة ❌'
    END as policy_status;
