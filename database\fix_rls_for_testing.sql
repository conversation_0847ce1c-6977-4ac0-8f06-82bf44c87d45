-- 🔧 إصلاح مؤقت لـ Row Level Security للاختبار
-- Temporary RLS Fix for Testing
-- Date: 2025-01-17
-- ⚠️ هذا للاختبار فقط - يجب إعادة تفعيل RLS في الإنتاج

-- ===== إيقاف RLS مؤقتاً للاختبار =====
-- Temporarily disable <PERSON><PERSON> for testing

-- إيقاف RLS على الجداول الرئيسية
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;

-- إيقاف RLS على الجداول الثانوية (إذا كانت موجودة)
DO $$
BEGIN
    -- التحقق من وجود الجداول قبل إيقاف RLS
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'devices') THEN
        ALTER TABLE public.devices DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_sessions') THEN
        ALTER TABLE public.user_sessions DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_logs') THEN
        ALTER TABLE public.admin_logs DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_stats') THEN
        ALTER TABLE public.system_stats DISABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- ===== إنشاء سياسات مؤقتة للوصول العام =====
-- Create temporary public access policies

-- حذف السياسات الموجودة
DROP POLICY IF EXISTS users_policy ON public.users;
DROP POLICY IF EXISTS users_simple_policy ON public.users;
DROP POLICY IF EXISTS users_user_policy ON public.users;

DROP POLICY IF EXISTS photos_policy ON public.photos;
DROP POLICY IF EXISTS photos_simple_policy ON public.photos;
DROP POLICY IF EXISTS photos_user_policy ON public.photos;

DROP POLICY IF EXISTS videos_policy ON public.videos;
DROP POLICY IF EXISTS videos_simple_policy ON public.videos;
DROP POLICY IF EXISTS videos_user_policy ON public.videos;

DROP POLICY IF EXISTS locations_policy ON public.locations;

-- إنشاء سياسات مؤقتة للوصول العام (للاختبار فقط)
-- ⚠️ هذا غير آمن - للاختبار فقط!

-- إعادة تفعيل RLS مع سياسات مفتوحة
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
CREATE POLICY users_public_access ON public.users FOR ALL USING (true);

ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
CREATE POLICY photos_public_access ON public.photos FOR ALL USING (true);

ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
CREATE POLICY videos_public_access ON public.videos FOR ALL USING (true);

ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
CREATE POLICY locations_public_access ON public.locations FOR ALL USING (true);

-- ===== رسائل التأكيد =====
SELECT '✅ تم إيقاف RLS مؤقتاً للاختبار' as status;
SELECT '⚠️ تذكر: يجب إعادة تفعيل RLS الآمن في الإنتاج!' as warning;
SELECT '📝 استخدم ملف restore_rls_security.sql لإعادة الأمان' as note;

-- ===== اختبار سريع =====
-- Quick test
SELECT 'اختبار الوصول للجداول:' as test_title;
SELECT COUNT(*) as users_count FROM public.users;
SELECT COUNT(*) as photos_count FROM public.photos;
SELECT COUNT(*) as videos_count FROM public.videos;
SELECT COUNT(*) as locations_count FROM public.locations;
