// ===== 🔐 خدمة المصادقة المستقلة =====
// Independent Authentication Service for Camera App
// تاريخ الإنشاء: 2025-07-20
// الهدف: نظام مصادقة مستقل بدون Supabase Auth

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'dart:async';

class UnifiedAuthService {
  static final UnifiedAuthService _instance = UnifiedAuthService._internal();
  factory UnifiedAuthService() => _instance;
  UnifiedAuthService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Logger _logger = Logger();

  String? _currentUserId;
  String? _currentSessionId;
  String? _currentSessionToken;
  Map<String, dynamic>? _currentUser;

  /// تسجيل الدخول باستخدام النظام المستقل الجديد
  Future<Map<String, dynamic>> signIn({
    required String nationalId,
    required String password,
    double? locationLat,
    double? locationLng,
    String? locationName,
    String? userAgent,
    String? ipAddress,
  }) async {
    try {
      _logger.i('🔐 محاولة تسجيل الدخول: $nationalId');

      // استخدام دالة المصادقة الجديدة من قاعدة البيانات
      final authResponse = await _supabase.rpc('authenticate_user', params: {
        'p_identifier': nationalId,
        'p_password': password,
        'p_ip_address': ipAddress,
        'p_user_agent': userAgent,
        'p_device_info': {
          'location_lat': locationLat,
          'location_lng': locationLng,
          'location_name': locationName,
          'platform': 'mobile',
          'app_version': '1.0.0',
        },
      });

      _logger.i('🔐 استجابة المصادقة: $authResponse');

      if (authResponse['success'] != true) {
        return {
          'success': false,
          'error': authResponse['error'] ?? 'authentication_failed',
          'message': authResponse['message'] ?? 'فشل في تسجيل الدخول',
        };
      }

      // استخراج بيانات المستخدم والجلسة
      final userData = authResponse['user_data'];
      final sessionData = authResponse['session'];

      _currentUserId = userData['id'];
      _currentSessionId = sessionData['session_id'];
      _currentSessionToken = sessionData['session_token'];
      _currentUser = userData;

      _logger.i('🔐 تم تسجيل الدخول بنجاح: $_currentUserId');

      // بدء heartbeat للحفاظ على الجلسة
      _startHeartbeat();

      return {
        'success': true,
        'user_id': _currentUserId,
        'user_data': userData,
        'session_id': _currentSessionId,
        'session_token': _currentSessionToken,
        'expires_at': sessionData['expires_at'],
        'message': 'تم تسجيل الدخول بنجاح',
      };

    } catch (e) {
      _logger.e('❌ خطأ في تسجيل الدخول: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في تسجيل الدخول',
      };
    }
  }

  /// تحديث حالة تسجيل الدخول
  Future<void> _updateLoginStatus({
    required String userId,
    required bool isOnline,
    String? sessionId,
    double? locationLat,
    double? locationLng,
    String? locationName,
  }) async {
    try {
      await _supabase.rpc('update_user_login_status', params: {
        'p_user_id': userId,
        'p_is_online': isOnline,
        'p_session_id': sessionId,
        'p_location_lat': locationLat,
        'p_location_lng': locationLng,
        'p_location_name': locationName,
      });

      _logger.i('🔄 تم تحديث حالة تسجيل الدخول: $isOnline');
    } catch (e) {
      _logger.e('❌ خطأ في تحديث حالة تسجيل الدخول: $e');
    }
  }

  /// بدء heartbeat للحفاظ على الجلسة
  void _startHeartbeat() {
    // إيقاف heartbeat السابق إن وجد
    _stopHeartbeat();
    
    // بدء heartbeat جديد كل 30 ثانية
    _heartbeatTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _updateHeartbeat();
    });
  }

  Timer? _heartbeatTimer;

  /// تحديث heartbeat
  Future<void> _updateHeartbeat() async {
    if (_currentUserId != null && _currentSessionToken != null) {
      try {
        // تحديث آخر نشاط للمستخدم
        await _supabase
            .from('users')
            .update({
              'last_seen': DateTime.now().toIso8601String(),
              'is_online': true,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', _currentUserId!);

        // تحديث الجلسة
        await _supabase
            .from('auth_sessions')
            .update({
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('session_token', _currentSessionToken!)
            .eq('is_active', true);

        _logger.d('💓 تم تحديث heartbeat');
      } catch (e) {
        _logger.e('❌ خطأ في heartbeat: $e');
        // إذا فشل heartbeat، قد تكون الجلسة منتهية
        if (e.toString().contains('session') || e.toString().contains('expired')) {
          await signOut();
        }
      }
    }
  }

  /// إيقاف heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      if (_currentUserId != null && _currentSessionToken != null) {
        // تحديث حالة المستخدم
        await _supabase
            .from('users')
            .update({
              'is_online': false,
              'current_session_id': null,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', _currentUserId!);

        // إلغاء تفعيل الجلسة
        await _supabase
            .from('auth_sessions')
            .update({
              'is_active': false,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('session_token', _currentSessionToken!);

        _logger.i('👋 تم تسجيل الخروج: $_currentUserId');
      }

      // إيقاف heartbeat
      _stopHeartbeat();

      // مسح البيانات المحلية
      _currentUserId = null;
      _currentSessionId = null;
      _currentSessionToken = null;
      _currentUser = null;

    } catch (e) {
      _logger.e('❌ خطأ في تسجيل الخروج: $e');
      // حتى لو فشل التحديث، امسح البيانات المحلية
      _currentUserId = null;
      _currentSessionId = null;
      _currentSessionToken = null;
      _currentUser = null;
    }
  }

  /// الحصول على بيانات المستخدم الحالي
  Future<Map<String, dynamic>?> getCurrentUser() async {
    if (_currentUserId == null) return _currentUser;

    try {
      final response = await _supabase
          .from('users')
          .select('*')
          .eq('id', _currentUserId!)
          .single();

      _currentUser = response;
      return response;
    } catch (e) {
      _logger.e('❌ خطأ في الحصول على بيانات المستخدم: $e');
      return _currentUser;
    }
  }

  /// فحص حالة الجلسة
  Future<bool> isSessionValid() async {
    if (_currentUserId == null || _currentSessionId == null) {
      return false;
    }

    try {
      final response = await _supabase
          .from('users_unified')
          .select('current_session_id, is_active, is_online')
          .eq('id', _currentUserId!)
          .single();

      final isValid = response['current_session_id'] == _currentSessionId &&
                     response['is_active'] == true;

      if (!isValid) {
        _logger.w('⚠️ الجلسة غير صالحة، تسجيل خروج تلقائي');
        await signOut();
      }

      return isValid;
    } catch (e) {
      _logger.e('❌ خطأ في فحص الجلسة: $e');
      return false;
    }
  }

  /// تحديث موقع المستخدم
  Future<void> updateLocation({
    required double latitude,
    required double longitude,
    String? locationName,
  }) async {
    if (_currentUserId == null) return;

    try {
      await _supabase
          .from('users_unified')
          .update({
            'last_location_lat': latitude,
            'last_location_lng': longitude,
            'last_location_name': locationName,
            'last_seen': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', _currentUserId!);

      _logger.i('📍 تم تحديث الموقع: $latitude, $longitude');
    } catch (e) {
      _logger.e('❌ خطأ في تحديث الموقع: $e');
    }
  }

  /// التحقق من صحة الجلسة الحالية
  Future<bool> isSessionValid() async {
    if (_currentSessionToken == null || _currentUserId == null) {
      return false;
    }

    try {
      final sessionResponse = await _supabase
          .from('auth_sessions')
          .select('expires_at, is_active')
          .eq('session_token', _currentSessionToken!)
          .eq('user_id', _currentUserId!)
          .maybeSingle();

      if (sessionResponse == null || !sessionResponse['is_active']) {
        return false;
      }

      final expiresAt = DateTime.parse(sessionResponse['expires_at']);
      return expiresAt.isAfter(DateTime.now());
    } catch (e) {
      _logger.e('❌ خطأ في التحقق من الجلسة: $e');
      return false;
    }
  }

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _currentUserId;

  /// الحصول على معرف الجلسة الحالية
  String? get currentSessionId => _currentSessionId;

  /// فحص ما إذا كان المستخدم مسجل الدخول
  bool get isSignedIn => _currentUserId != null;

  /// تنظيف الموارد
  void dispose() {
    _stopHeartbeat();
  }
}


