// ===== 🔐 خدمة المصادقة الموحدة =====
// Unified Authentication Service for Camera App
// تاريخ الإنشاء: 2025-07-20
// الهدف: التعامل مع الجدول الموحد الجديد users_unified

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

class UnifiedAuthService {
  static final UnifiedAuthService _instance = UnifiedAuthService._internal();
  factory UnifiedAuthService() => _instance;
  UnifiedAuthService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Logger _logger = Logger();
  
  String? _currentUserId;
  String? _currentSessionId;

  /// تسجيل الدخول باستخدام الجدول الموحد
  Future<Map<String, dynamic>> signIn({
    required String nationalId,
    required String password,
    double? locationLat,
    double? locationLng,
    String? locationName,
  }) async {
    try {
      final email = '$<EMAIL>';
      _logger.i('🔐 محاولة تسجيل الدخول: $email');

      // البحث عن المستخدم في الجدول الموحد
      final userResponse = await _supabase
          .from('users_unified')
          .select('*')
          .eq('email', email)
          .eq('is_active', true)
          .maybeSingle();

      if (userResponse == null) {
        return {
          'success': false,
          'error': 'المستخدم غير موجود أو غير نشط',
          'message': 'تحقق من الرقم الوطني',
        };
      }

      // التحقق من كلمة المرور (مبسط - في الإنتاج استخدم bcrypt)
      final storedPassword = userResponse['encrypted_password'] as String?;
      final hashedInputPassword = _hashPassword(password);
      
      // للتوافق مع النظام القديم، نتحقق من كلمة المرور بطرق متعددة
      bool passwordValid = false;
      if (storedPassword != null) {
        passwordValid = storedPassword == hashedInputPassword || 
                       password == nationalId; // كلمة المرور الافتراضية
      } else {
        passwordValid = password == nationalId; // كلمة المرور الافتراضية
      }

      if (!passwordValid) {
        return {
          'success': false,
          'error': 'كلمة المرور غير صحيحة',
          'message': 'تحقق من كلمة المرور',
        };
      }

      final userId = userResponse['id'] as String;
      _currentUserId = userId;
      _currentSessionId = _generateSessionId();

      _logger.i('🔐 تم تسجيل الدخول بنجاح: $userId');

      // تحديث حالة تسجيل الدخول
      await _updateLoginStatus(
        userId: userId,
        isOnline: true,
        sessionId: _currentSessionId!,
        locationLat: locationLat,
        locationLng: locationLng,
        locationName: locationName,
      );

      // بدء heartbeat للحفاظ على الجلسة
      _startHeartbeat();

      return {
        'success': true,
        'user_id': userId,
        'user_data': userResponse,
        'session_id': _currentSessionId,
        'message': 'تم تسجيل الدخول بنجاح',
      };

    } catch (e) {
      _logger.e('❌ خطأ في تسجيل الدخول: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في تسجيل الدخول',
      };
    }
  }

  /// تحديث حالة تسجيل الدخول
  Future<void> _updateLoginStatus({
    required String userId,
    required bool isOnline,
    String? sessionId,
    double? locationLat,
    double? locationLng,
    String? locationName,
  }) async {
    try {
      await _supabase.rpc('update_user_login_status', params: {
        'p_user_id': userId,
        'p_is_online': isOnline,
        'p_session_id': sessionId,
        'p_location_lat': locationLat,
        'p_location_lng': locationLng,
        'p_location_name': locationName,
      });

      _logger.i('🔄 تم تحديث حالة تسجيل الدخول: $isOnline');
    } catch (e) {
      _logger.e('❌ خطأ في تحديث حالة تسجيل الدخول: $e');
    }
  }

  /// بدء heartbeat للحفاظ على الجلسة
  void _startHeartbeat() {
    // إيقاف heartbeat السابق إن وجد
    _stopHeartbeat();
    
    // بدء heartbeat جديد كل 30 ثانية
    _heartbeatTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _updateHeartbeat();
    });
  }

  Timer? _heartbeatTimer;

  /// تحديث heartbeat
  Future<void> _updateHeartbeat() async {
    if (_currentUserId != null) {
      try {
        await _supabase
            .from('users_unified')
            .update({
              'last_seen': DateTime.now().toIso8601String(),
              'is_online': true,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', _currentUserId!);

        _logger.d('💓 تم تحديث heartbeat');
      } catch (e) {
        _logger.e('❌ خطأ في heartbeat: $e');
      }
    }
  }

  /// إيقاف heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      if (_currentUserId != null) {
        // تحديث حالة الاتصال
        await _updateLoginStatus(
          userId: _currentUserId!,
          isOnline: false,
        );

        _logger.i('👋 تم تسجيل الخروج: $_currentUserId');
      }

      // إيقاف heartbeat
      _stopHeartbeat();

      // مسح البيانات المحلية
      _currentUserId = null;
      _currentSessionId = null;

    } catch (e) {
      _logger.e('❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// الحصول على بيانات المستخدم الحالي
  Future<Map<String, dynamic>?> getCurrentUser() async {
    if (_currentUserId == null) return null;

    try {
      final response = await _supabase
          .from('users_unified')
          .select('*')
          .eq('id', _currentUserId!)
          .single();

      return response;
    } catch (e) {
      _logger.e('❌ خطأ في الحصول على بيانات المستخدم: $e');
      return null;
    }
  }

  /// فحص حالة الجلسة
  Future<bool> isSessionValid() async {
    if (_currentUserId == null || _currentSessionId == null) {
      return false;
    }

    try {
      final response = await _supabase
          .from('users_unified')
          .select('current_session_id, is_active, is_online')
          .eq('id', _currentUserId!)
          .single();

      final isValid = response['current_session_id'] == _currentSessionId &&
                     response['is_active'] == true;

      if (!isValid) {
        _logger.w('⚠️ الجلسة غير صالحة، تسجيل خروج تلقائي');
        await signOut();
      }

      return isValid;
    } catch (e) {
      _logger.e('❌ خطأ في فحص الجلسة: $e');
      return false;
    }
  }

  /// تحديث موقع المستخدم
  Future<void> updateLocation({
    required double latitude,
    required double longitude,
    String? locationName,
  }) async {
    if (_currentUserId == null) return;

    try {
      await _supabase
          .from('users_unified')
          .update({
            'last_location_lat': latitude,
            'last_location_lng': longitude,
            'last_location_name': locationName,
            'last_seen': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', _currentUserId!);

      _logger.i('📍 تم تحديث الموقع: $latitude, $longitude');
    } catch (e) {
      _logger.e('❌ خطأ في تحديث الموقع: $e');
    }
  }

  /// تشفير كلمة المرور (مبسط)
  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// إنشاء معرف جلسة فريد
  String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + DateTime.now().microsecond) % 999999;
    return 'session_${timestamp}_$random';
  }

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _currentUserId;

  /// الحصول على معرف الجلسة الحالية
  String? get currentSessionId => _currentSessionId;

  /// فحص ما إذا كان المستخدم مسجل الدخول
  bool get isSignedIn => _currentUserId != null;

  /// تنظيف الموارد
  void dispose() {
    _stopHeartbeat();
  }
}

// إضافة import مطلوب
import 'dart:async';
