import 'dart:async';
import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import '../utils/logger.dart';

/// خدمة رفع محسنة ومبسطة
class ImprovedUploadService {
  static final ImprovedUploadService _instance = ImprovedUploadService._internal();
  factory ImprovedUploadService() => _instance;
  ImprovedUploadService._internal();

  final _logger = getLogger();
  final _supabase = Supabase.instance.client;
  
  // إعدادات محسنة
  static const Duration _shortTimeout = Duration(seconds: 30);
  static const Duration _longTimeout = Duration(minutes: 2);
  static const int _maxRetries = 3;
  static const int _maxFileSize = 50 * 1024 * 1024; // 50MB

  /// رفع صورة مع نظام مبسط
  Future<UploadResult> uploadPhoto(String filePath, String? location) async {
    return _uploadFile(filePath, location, FileType.photo);
  }

  /// رفع فيديو مع نظام مبسط
  Future<UploadResult> uploadVideo(String filePath, String? location) async {
    return _uploadFile(filePath, location, FileType.video);
  }

  /// رفع ملف مع نظام موحد ومبسط
  Future<UploadResult> _uploadFile(String filePath, String? location, FileType type) async {
    final file = File(filePath);
    
    // التحقق من وجود الملف
    if (!await file.exists()) {
      return UploadResult.error('الملف غير موجود');
    }

    // التحقق من حجم الملف
    final fileSize = await file.length();
    if (fileSize > _maxFileSize) {
      return UploadResult.error('حجم الملف كبير جداً (الحد الأقصى 50MB)');
    }

    // فحص الاتصال
    if (!await _hasInternetConnection()) {
      await _saveForLaterUpload(filePath, location, type);
      return UploadResult.savedOffline();
    }

    // محاولة الرفع مع إعادة المحاولة
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        _logger.i('محاولة رفع ${type.name} - المحاولة $attempt/$_maxRetries');
        
        final result = await _attemptUpload(file, location, type, attempt);
        if (result.isSuccess) {
          await _deleteLocalFile(filePath);
          return result;
        }
        
        if (attempt < _maxRetries) {
          await Future.delayed(Duration(seconds: attempt * 2)); // تأخير متزايد
        }
      } catch (e) {
        _logger.w('فشلت المحاولة $attempt: $e');
        if (attempt == _maxRetries) {
          await _saveForLaterUpload(filePath, location, type);
          return UploadResult.error('فشل الرفع بعد $_maxRetries محاولات');
        }
      }
    }

    return UploadResult.error('فشل غير متوقع');
  }

  /// محاولة رفع واحدة
  Future<UploadResult> _attemptUpload(File file, String? location, FileType type, int attempt) async {
    final fileName = _generateFileName(file, type);
    final bucket = type == FileType.photo ? 'photos' : 'videos';
    final storagePath = 'uploads/$fileName';
    
    // تحديد timeout حسب حجم الملف
    final fileSize = await file.length();
    final timeout = fileSize > 10 * 1024 * 1024 ? _longTimeout : _shortTimeout;

    try {
      // رفع الملف
      await _supabase.storage
          .from(bucket)
          .upload(storagePath, file, fileOptions: const FileOptions(upsert: true))
          .timeout(timeout);

      // الحصول على الرابط
      final url = _supabase.storage.from(bucket).getPublicUrl(storagePath);

      // حفظ في قاعدة البيانات
      await _saveToDatabase(fileName, storagePath, url, location, type);

      _logger.i('تم رفع ${type.name} بنجاح: $fileName');
      return UploadResult.success(storagePath, url);

    } catch (e) {
      _logger.e('فشل رفع ${type.name}: $e');
      rethrow;
    }
  }

  /// حفظ في قاعدة البيانات
  Future<void> _saveToDatabase(String fileName, String storagePath, String url, String? location, FileType type) async {
    final userId = _supabase.auth.currentUser?.id;
    final username = _supabase.auth.currentUser?.userMetadata?['full_name'] ?? 'مجهول';
    final now = DateTime.now().toIso8601String();

    final data = {
      'user_id': userId,
      'file_name': fileName,
      'storage_path': storagePath,
      'location': location,
      'username': username,
      'created_at': now,
      'updated_at': now,
    };

    if (type == FileType.photo) {
      data['image_url'] = url;
      await _supabase.from('photos').insert(data);
    } else {
      data['video_url'] = url;
      await _supabase.from('videos').insert(data);
    }
  }

  /// حفظ للرفع لاحقاً
  Future<void> _saveForLaterUpload(String filePath, String? location, FileType type) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingKey = 'pending_${type.name}_uploads';
    final pending = prefs.getStringList(pendingKey) ?? [];
    
    final metadata = {
      'file_path': filePath,
      'location': location,
      'type': type.name,
      'created_at': DateTime.now().toIso8601String(),
      'attempts': 0,
    };

    pending.add(metadata.toString());
    await prefs.setStringList(pendingKey, pending);
    
    _logger.i('تم حفظ ${type.name} للرفع لاحقاً: $filePath');
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _hasInternetConnection() async {
    try {
      final result = await Connectivity().checkConnectivity();
      return !result.contains(ConnectivityResult.none);
    } catch (e) {
      return false;
    }
  }

  /// توليد اسم ملف فريد
  String _generateFileName(File file, FileType type) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(file.path);
    final prefix = type == FileType.photo ? 'photo' : 'video';
    return '${prefix}_$timestamp$extension';
  }

  /// حذف ملف محلي
  Future<void> _deleteLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('تم حذف الملف المحلي: $filePath');
      }
    } catch (e) {
      _logger.w('فشل حذف الملف المحلي: $e');
    }
  }

  /// رفع الملفات المحفوظة
  Future<void> uploadPendingFiles() async {
    if (!await _hasInternetConnection()) return;

    for (final type in FileType.values) {
      await _uploadPendingFilesOfType(type);
    }
  }

  /// رفع الملفات المحفوظة من نوع معين
  Future<void> _uploadPendingFilesOfType(FileType type) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingKey = 'pending_${type.name}_uploads';
    final pending = prefs.getStringList(pendingKey) ?? [];
    
    if (pending.isEmpty) return;

    final remaining = <String>[];
    
    for (final metadataStr in pending) {
      try {
        // تحويل النص إلى Map (مبسط)
        final metadata = _parseMetadata(metadataStr);
        final filePath = metadata['file_path'] as String;
        final location = metadata['location'] as String?;
        
        final result = await _uploadFile(filePath, location, type);
        if (!result.isSuccess) {
          remaining.add(metadataStr);
        }
      } catch (e) {
        _logger.e('فشل رفع ملف محفوظ: $e');
        remaining.add(metadataStr);
      }
    }

    await prefs.setStringList(pendingKey, remaining);
  }

  /// تحليل metadata مبسط
  Map<String, dynamic> _parseMetadata(String metadataStr) {
    // تحليل مبسط - يمكن تحسينه لاحقاً
    return {
      'file_path': metadataStr.split('|')[0],
      'location': metadataStr.split('|').length > 1 ? metadataStr.split('|')[1] : null,
    };
  }
}

/// نوع الملف
enum FileType { photo, video }

/// نتيجة الرفع
class UploadResult {
  final bool isSuccess;
  final String? storagePath;
  final String? url;
  final String? error;
  final bool isSavedOffline;

  UploadResult.success(this.storagePath, this.url) 
      : isSuccess = true, error = null, isSavedOffline = false;
  
  UploadResult.error(this.error) 
      : isSuccess = false, storagePath = null, url = null, isSavedOffline = false;
  
  UploadResult.savedOffline() 
      : isSuccess = false, storagePath = null, url = null, error = null, isSavedOffline = true;
}
