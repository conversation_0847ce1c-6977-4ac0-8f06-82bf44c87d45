import 'package:device_info_plus/device_info_plus.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

class DeviceService {
  final _logger = Logger();
  final _deviceInfo = DeviceInfoPlugin();
  static const _deviceIdKey = 'device_unique_id';

  Future<String> getUniqueDeviceId() async {
    try {
      // محاولة الحصول على معرف الجهاز المحفوظ
      final prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);

      if (savedId != null && savedId.isNotEmpty) {
        _logger.i('استخدام معرف الجهاز المحفوظ: ${savedId.substring(0, 8)}...');
        return savedId;
      }

      // إنشاء معرف جديد إذا لم يكن موجوداً
      final androidInfo = await _deviceInfo.androidInfo;

      // استخدام معلومات ثابتة للجهاز (لا تتغير مع إعادة التثبيت)
      final uniqueString = '${androidInfo.id}'
          '_${androidInfo.device}'
          '_${androidInfo.model}'
          '_${androidInfo.brand}'
          '_${androidInfo.hardware}';

      // إنشاء بصمة فريدة
      final deviceId = _generateHash(uniqueString);

      // حفظ المعرف للاستخدام المستقبلي
      await prefs.setString(_deviceIdKey, deviceId);

      _logger.i('تم إنشاء معرف جهاز جديد: ${deviceId.substring(0, 8)}...');

      return deviceId;
    } catch (e) {
      _logger.e('Error generating device ID: $e');
      // حل بديل في حالة الفشل
      final fallbackId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';

      // محاولة حفظ المعرف البديل
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_deviceIdKey, fallbackId);
      } catch (saveError) {
        _logger.e('Error saving fallback device ID: $saveError');
      }

      return fallbackId;
    }
  }

  String _generateHash(String input) {
    var bytes = utf8.encode(input);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<Map<String, String>> getDeviceInfo() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final deviceId = await getUniqueDeviceId();

      return {
        'device_id': deviceId,
        'fingerprint': deviceId,  // إضافة fingerprint
        'android_id': androidInfo.id,  // إضافة android_id الحقيقي
        'build_fingerprint': androidInfo.fingerprint,  // إضافة build_fingerprint
        'device_name': '${androidInfo.brand} ${androidInfo.model}',
        'deviceName': '${androidInfo.brand} ${androidInfo.model}',
        'model': androidInfo.model,
        'deviceModel': androidInfo.model,
        'brand': androidInfo.brand,
        'deviceBrand': androidInfo.brand,
        'product': androidInfo.product,
        'hardware': androidInfo.hardware,
        'android_version': androidInfo.version.release,
      };
    } catch (e) {
      _logger.e('Error getting device info: $e');
      return {
        'device_id': 'error_${DateTime.now().millisecondsSinceEpoch}',
        'fingerprint': 'error_${DateTime.now().millisecondsSinceEpoch}',
        'android_id': 'error_${DateTime.now().millisecondsSinceEpoch}',
        'device_name': 'Unknown Device',
        'deviceName': 'Unknown Device',
        'model': 'Unknown',
        'deviceModel': 'Unknown',
        'brand': 'Unknown',
        'deviceBrand': 'Unknown',
      };
    }
  }
}
