import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/logger.dart';
import 'session_service.dart';
import 'migration_service.dart';

final sessionManagerProvider = Provider((ref) => SessionManager());

/// خدمة إدارة جلسات المستخدمين مع تتبع الحالة المباشرة
class SessionManager {
  final _logger = getLogger();
  final _supabase = Supabase.instance.client;
  final _liveTrackingService = SessionService();

  Timer? _heartbeatTimer;
  Timer? _cleanupTimer;
  String? _currentSessionId;
  bool _isSessionActive = false;

  static const Duration _heartbeatInterval = Duration(minutes: 2);
  static const Duration _sessionTimeout = Duration(minutes: 30);
  static const Duration _cleanupInterval = Duration(hours: 1);

  /// بدء جلسة جديدة عند تسجيل الدخول
  Future<String?> startSession({
    required String userId,
    String? deviceId,
  }) async {
    try {
      _logger.i('🚀 Starting new session for user: $userId');

      // الحصول على معلومات الجهاز والشبكة
      final deviceInfo = await _getDeviceInfo();
      final ipAddress = await _getIpAddress();

      // إنهاء أي جلسات نشطة للمستخدم على نفس الجهاز
      await _endExistingSessions(userId, deviceId);

      // إنشاء جلسة جديدة
      final sessionData = {
        'user_id': userId,
        'device_id': deviceId,
        'session_token': _generateSessionToken(),
        'ip_address': ipAddress,
        'user_agent': deviceInfo,
        'started_at': DateTime.now().toIso8601String(),
        'last_activity': DateTime.now().toIso8601String(),
        'expires_at': DateTime.now().add(_sessionTimeout).toIso8601String(),
        'is_active': true,
      };

      final response = await _supabase
          .from('user_sessions')
          .insert(sessionData)
          .select('id')
          .single();

      _currentSessionId = response['id'];
      _isSessionActive = true;

      // حفظ معرف الجلسة محلياً
      await _saveSessionLocally(_currentSessionId!);

      // بدء مراقبة الجلسة
      _startHeartbeat();
      _startCleanupTimer();

      // 🆕 ترحيل الملفات القديمة
      try {
        await MigrationService().runMigrationIfNeeded();
        _logger.i('🔄 تم فحص وترحيل الملفات القديمة');
      } catch (e) {
        _logger.w('⚠️ فشل في ترحيل الملفات القديمة: $e');
      }

      // 🆕 بدء Live Tracking Service
      try {
        await _liveTrackingService.startSession(
          userId: userId,
          deviceId: deviceId ?? 'unknown',
          appVersion: '3.0.2',
        );
        _logger.i('🔄 تم بدء Live Tracking Service');
      } catch (e) {
        _logger.w('⚠️ فشل في بدء Live Tracking Service: $e');
        // لا نرمي خطأ هنا لأن الجلسة الأساسية نجحت
      }

      _logger.i('✅ Session started successfully: $_currentSessionId');
      return _currentSessionId;

    } catch (e) {
      _logger.e('❌ Failed to start session: $e');
      return null;
    }
  }

  /// تحديث نشاط الجلسة (heartbeat)
  Future<void> updateActivity() async {
    if (!_isSessionActive || _currentSessionId == null) return;

    try {
      await _supabase
          .from('user_sessions')
          .update({
            'last_activity': DateTime.now().toIso8601String(),
            'expires_at': DateTime.now().add(_sessionTimeout).toIso8601String(),
          })
          .eq('id', _currentSessionId!)
          .eq('is_active', true);

      _logger.d('💓 Session activity updated: $_currentSessionId');
    } catch (e) {
      _logger.w('⚠️ Failed to update session activity: $e');
    }
  }

  /// إنهاء الجلسة الحالية
  Future<void> endSession({String reason = 'logout'}) async {
    try {
      // 🆕 إيقاف Live Tracking Service أولاً
      try {
        await _liveTrackingService.endSession();
        _logger.i('🛑 تم إيقاف Live Tracking Service');
      } catch (e) {
        _logger.w('⚠️ فشل في إيقاف Live Tracking Service: $e');
      }

      if (_currentSessionId != null) {
        await _supabase
            .from('user_sessions')
            .update({
              'ended_at': DateTime.now().toIso8601String(),
              'is_active': false,
              'end_reason': reason,
            })
            .eq('id', _currentSessionId!);

        _logger.i('🛑 Session ended: $_currentSessionId (reason: $reason)');
      }

      // تنظيف الحالة المحلية
      await _clearLocalSession();
      _stopHeartbeat();
      _stopCleanupTimer();

      _currentSessionId = null;
      _isSessionActive = false;

    } catch (e) {
      _logger.e('❌ Failed to end session: $e');
    }
  }

  /// بدء مراقبة الجلسة (heartbeat)
  void _startHeartbeat() {
    _stopHeartbeat(); // تأكد من عدم وجود timer سابق

    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      updateActivity();
    });

    _logger.i('💓 Heartbeat started (every ${_heartbeatInterval.inMinutes} minutes)');
  }

  /// إيقاف مراقبة الجلسة
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// بدء تنظيف الجلسات المنتهية
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(_cleanupInterval, (timer) {
      _cleanupExpiredSessions();
    });
  }

  /// إيقاف تنظيف الجلسات
  void _stopCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
  }

  /// تنظيف الجلسات المنتهية الصلاحية
  Future<void> _cleanupExpiredSessions() async {
    try {
      await _supabase
          .from('user_sessions')
          .update({
            'is_active': false,
            'end_reason': 'timeout',
            'ended_at': DateTime.now().toIso8601String(),
          })
          .lt('expires_at', DateTime.now().toIso8601String())
          .eq('is_active', true);

      _logger.i('🧹 Cleaned up expired sessions');
    } catch (e) {
      _logger.w('⚠️ Failed to cleanup expired sessions: $e');
    }
  }

  /// إنهاء الجلسات النشطة للمستخدم
  Future<void> _endExistingSessions(String userId, String? deviceId) async {
    try {
      var query = _supabase
          .from('user_sessions')
          .update({
            'ended_at': DateTime.now().toIso8601String(),
            'is_active': false,
            'end_reason': 'new_session',
          })
          .eq('user_id', userId)
          .eq('is_active', true);

      if (deviceId != null) {
        query = query.eq('device_id', deviceId);
      }

      await query;
      _logger.i('🔄 Ended existing sessions for user: $userId');
    } catch (e) {
      _logger.w('⚠️ Failed to end existing sessions: $e');
    }
  }

  /// توليد رمز جلسة فريد
  String _generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + DateTime.now().microsecond).toString();
    return 'session_$random';
  }

  /// الحصول على معلومات الجهاز
  Future<String> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await deviceInfo.androidInfo;
        return 'Android ${androidInfo.version.release} - ${androidInfo.model}';
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return 'iOS ${iosInfo.systemVersion} - ${iosInfo.model}';
      }
      return 'Unknown Device';
    } catch (e) {
      return 'Device Info Error';
    }
  }

  /// الحصول على عنوان IP (تقريبي)
  Future<String?> _getIpAddress() async {
    try {
      // في التطبيق الحقيقي، يمكن استخدام خدمة خارجية للحصول على IP
      return null; // سيتم تعيينه من الخادم
    } catch (e) {
      return null;
    }
  }

  /// حفظ معرف الجلسة محلياً
  Future<void> _saveSessionLocally(String sessionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_session_id', sessionId);
      await prefs.setString('session_started_at', DateTime.now().toIso8601String());
    } catch (e) {
      _logger.e('Failed to save session locally: $e');
    }
  }

  /// مسح الجلسة المحلية
  Future<void> _clearLocalSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_session_id');
      await prefs.remove('session_started_at');
    } catch (e) {
      _logger.e('Failed to clear local session: $e');
    }
  }

  /// استعادة الجلسة من التخزين المحلي
  Future<void> restoreSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionId = prefs.getString('current_session_id');
      
      if (sessionId != null) {
        // التحقق من صحة الجلسة في قاعدة البيانات
        final session = await _supabase
            .from('user_sessions')
            .select('*')
            .eq('id', sessionId)
            .eq('is_active', true)
            .maybeSingle();

        if (session != null) {
          final expiresAt = DateTime.parse(session['expires_at']);
          if (expiresAt.isAfter(DateTime.now())) {
            _currentSessionId = sessionId;
            _isSessionActive = true;
            _startHeartbeat();
            _logger.i('✅ Session restored: $sessionId');
            return;
          }
        }
      }

      // إذا لم تكن الجلسة صالحة، امسحها
      await _clearLocalSession();
    } catch (e) {
      _logger.e('Failed to restore session: $e');
      await _clearLocalSession();
    }
  }

  /// الحصول على معرف الجلسة الحالية
  String? get currentSessionId => _currentSessionId;

  /// التحقق من حالة الجلسة
  bool get isSessionActive => _isSessionActive;

  /// تنظيف الموارد
  void dispose() {
    _stopHeartbeat();
    _stopCleanupTimer();
    _liveTrackingService.dispose();
  }
}
