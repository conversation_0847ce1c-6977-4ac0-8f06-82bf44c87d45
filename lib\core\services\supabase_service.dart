import 'package:flutter_riverpod/flutter_riverpod.dart' hide Provider;
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class SupabaseService {
  final supabase.SupabaseClient client;

  SupabaseService(this.client);

  static SupabaseService initialize() {
    return SupabaseService(supabase.Supabase.instance.client);
  }

  // Service role client for admin operations
  static supabase.SupabaseClient get serviceRoleClient {
    return supabase.SupabaseClient(
      dotenv.env['SUPABASE_URL']!,
      dotenv.env['SUPABASE_SERVICE_ROLE_KEY']!,
    );
  }
}

final supabaseServiceProvider = StateProvider<SupabaseService>((ref) {
  return SupabaseService.initialize();
});
