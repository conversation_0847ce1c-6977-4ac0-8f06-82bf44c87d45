-- 📊 دوال التحليل المتقدمة للمواقع
-- Advanced Analytics Functions for Locations
-- Date: 2025-01-16

-- ===== 📈 دالة تحليل استخدام المواقع =====
CREATE OR REPLACE FUNCTION analyze_locations_usage(
    p_days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    location_code TEXT,
    location_type TEXT,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    photos_last_30_days INTEGER,
    videos_last_30_days INTEGER,
    files_last_30_days INTEGER,
    usage_trend TEXT,
    last_activity TIMESTAMP WITH TIME ZONE,
    days_since_last_use INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH recent_activity AS (
        -- نشاط الصور في الفترة المحددة
        SELECT 
            full_location_code,
            COUNT(*) as recent_photos
        FROM photos 
        WHERE capture_timestamp >= NOW() - (p_days_back || ' days')::INTERVAL
        AND status = 'active'
        GROUP BY full_location_code
    ),
    recent_videos AS (
        -- نشاط الفيديوهات في الفترة المحددة
        SELECT 
            full_location_code,
            COUNT(*) as recent_videos
        FROM videos 
        WHERE capture_timestamp >= NOW() - (p_days_back || ' days')::INTERVAL
        AND status = 'active'
        GROUP BY full_location_code
    )
    SELECT 
        l.location_code,
        l.location_type,
        l.total_photos,
        l.total_videos,
        (l.total_photos + l.total_videos) as total_files,
        COALESCE(ra.recent_photos, 0) as photos_last_30_days,
        COALESCE(rv.recent_videos, 0) as videos_last_30_days,
        (COALESCE(ra.recent_photos, 0) + COALESCE(rv.recent_videos, 0)) as files_last_30_days,
        CASE 
            WHEN (COALESCE(ra.recent_photos, 0) + COALESCE(rv.recent_videos, 0)) = 0 THEN 'غير نشط'
            WHEN (COALESCE(ra.recent_photos, 0) + COALESCE(rv.recent_videos, 0)) < 5 THEN 'نشاط قليل'
            WHEN (COALESCE(ra.recent_photos, 0) + COALESCE(rv.recent_videos, 0)) < 20 THEN 'نشاط متوسط'
            ELSE 'نشاط عالي'
        END as usage_trend,
        l.last_used_at as last_activity,
        CASE 
            WHEN l.last_used_at IS NULL THEN NULL
            ELSE EXTRACT(DAY FROM (NOW() - l.last_used_at))::INTEGER
        END as days_since_last_use
    FROM locations l
    LEFT JOIN recent_activity ra ON l.location_code = ra.full_location_code
    LEFT JOIN recent_videos rv ON l.location_code = rv.full_location_code
    WHERE l.is_active = true
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

-- ===== 🏆 دالة أكثر المواقع استخداماً =====
CREATE OR REPLACE FUNCTION get_top_locations(
    p_limit INTEGER DEFAULT 10,
    p_location_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    rank INTEGER,
    location_code TEXT,
    location_type TEXT,
    total_files INTEGER,
    total_photos INTEGER,
    total_videos INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_score DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ROW_NUMBER() OVER (ORDER BY 
            (l.total_photos + l.total_videos) DESC, 
            l.last_used_at DESC NULLS LAST
        )::INTEGER as rank,
        l.location_code,
        l.location_type,
        (l.total_photos + l.total_videos) as total_files,
        l.total_photos,
        l.total_videos,
        l.last_used_at,
        -- حساب نقاط الاستخدام (عدد الملفات + نقاط حداثة الاستخدام)
        (
            (l.total_photos + l.total_videos) * 1.0 +
            CASE 
                WHEN l.last_used_at IS NULL THEN 0
                WHEN l.last_used_at >= NOW() - INTERVAL '7 days' THEN 50
                WHEN l.last_used_at >= NOW() - INTERVAL '30 days' THEN 20
                WHEN l.last_used_at >= NOW() - INTERVAL '90 days' THEN 5
                ELSE 0
            END
        ) as usage_score
    FROM locations l
    WHERE l.is_active = true
    AND (p_location_type IS NULL OR l.location_type = p_location_type)
    ORDER BY usage_score DESC, l.last_used_at DESC NULLS LAST
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة إحصائيات شاملة للمواقع =====
CREATE OR REPLACE FUNCTION get_locations_summary_stats()
RETURNS TABLE (
    metric_name TEXT,
    metric_value TEXT,
    metric_category TEXT
) AS $$
BEGIN
    RETURN QUERY
    -- إحصائيات عامة
    SELECT 'إجمالي المواقع'::TEXT, COUNT(*)::TEXT, 'عام'::TEXT FROM locations
    UNION ALL
    SELECT 'المواقع النشطة'::TEXT, COUNT(*)::TEXT, 'عام'::TEXT FROM locations WHERE is_active = true
    UNION ALL
    SELECT 'المواقع المتاحة'::TEXT, COUNT(*)::TEXT, 'عام'::TEXT FROM locations WHERE is_available = true
    
    UNION ALL
    -- إحصائيات مواقع U
    SELECT 'مواقع U - العدد'::TEXT, COUNT(*)::TEXT, 'مواقع U'::TEXT FROM locations WHERE location_type = 'U'
    UNION ALL
    SELECT 'مواقع U - المستخدمة'::TEXT, COUNT(*)::TEXT, 'مواقع U'::TEXT 
    FROM locations WHERE location_type = 'U' AND last_used_at IS NOT NULL
    UNION ALL
    SELECT 'مواقع U - إجمالي الصور'::TEXT, SUM(total_photos)::TEXT, 'مواقع U'::TEXT 
    FROM locations WHERE location_type = 'U'
    UNION ALL
    SELECT 'مواقع U - إجمالي الفيديوهات'::TEXT, SUM(total_videos)::TEXT, 'مواقع U'::TEXT 
    FROM locations WHERE location_type = 'U'
    
    UNION ALL
    -- إحصائيات مواقع C
    SELECT 'مواقع C - العدد'::TEXT, COUNT(*)::TEXT, 'مواقع C'::TEXT FROM locations WHERE location_type = 'C'
    UNION ALL
    SELECT 'مواقع C - المستخدمة'::TEXT, COUNT(*)::TEXT, 'مواقع C'::TEXT 
    FROM locations WHERE location_type = 'C' AND last_used_at IS NOT NULL
    UNION ALL
    SELECT 'مواقع C - إجمالي الصور'::TEXT, SUM(total_photos)::TEXT, 'مواقع C'::TEXT 
    FROM locations WHERE location_type = 'C'
    UNION ALL
    SELECT 'مواقع C - إجمالي الفيديوهات'::TEXT, SUM(total_videos)::TEXT, 'مواقع C'::TEXT 
    FROM locations WHERE location_type = 'C'
    
    UNION ALL
    -- إحصائيات الاستخدام
    SELECT 'المواقع غير المستخدمة'::TEXT, COUNT(*)::TEXT, 'استخدام'::TEXT 
    FROM locations WHERE last_used_at IS NULL AND is_active = true
    UNION ALL
    SELECT 'المواقع المستخدمة اليوم'::TEXT, COUNT(*)::TEXT, 'استخدام'::TEXT 
    FROM locations WHERE last_used_at >= CURRENT_DATE
    UNION ALL
    SELECT 'المواقع المستخدمة هذا الأسبوع'::TEXT, COUNT(*)::TEXT, 'استخدام'::TEXT 
    FROM locations WHERE last_used_at >= CURRENT_DATE - INTERVAL '7 days'
    UNION ALL
    SELECT 'المواقع المستخدمة هذا الشهر'::TEXT, COUNT(*)::TEXT, 'استخدام'::TEXT 
    FROM locations WHERE last_used_at >= CURRENT_DATE - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- ===== 📅 دالة تحليل الاستخدام اليومي =====
CREATE OR REPLACE FUNCTION analyze_daily_location_usage(
    p_days_back INTEGER DEFAULT 7
)
RETURNS TABLE (
    usage_date DATE,
    total_files INTEGER,
    total_photos INTEGER,
    total_videos INTEGER,
    unique_locations INTEGER,
    u_locations_used INTEGER,
    c_locations_used INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH daily_photos AS (
        SELECT 
            DATE(capture_timestamp) as usage_date,
            COUNT(*) as photo_count,
            COUNT(DISTINCT full_location_code) as unique_photo_locations,
            COUNT(DISTINCT CASE WHEN location_type = 'U' THEN full_location_code END) as u_photo_locations,
            COUNT(DISTINCT CASE WHEN location_type = 'C' THEN full_location_code END) as c_photo_locations
        FROM photos 
        WHERE capture_timestamp >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
        AND status = 'active'
        GROUP BY DATE(capture_timestamp)
    ),
    daily_videos AS (
        SELECT 
            DATE(capture_timestamp) as usage_date,
            COUNT(*) as video_count,
            COUNT(DISTINCT full_location_code) as unique_video_locations,
            COUNT(DISTINCT CASE WHEN location_type = 'U' THEN full_location_code END) as u_video_locations,
            COUNT(DISTINCT CASE WHEN location_type = 'C' THEN full_location_code END) as c_video_locations
        FROM videos 
        WHERE capture_timestamp >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
        AND status = 'active'
        GROUP BY DATE(capture_timestamp)
    )
    SELECT 
        COALESCE(dp.usage_date, dv.usage_date) as usage_date,
        (COALESCE(dp.photo_count, 0) + COALESCE(dv.video_count, 0)) as total_files,
        COALESCE(dp.photo_count, 0) as total_photos,
        COALESCE(dv.video_count, 0) as total_videos,
        GREATEST(
            COALESCE(dp.unique_photo_locations, 0),
            COALESCE(dv.unique_video_locations, 0)
        ) as unique_locations,
        GREATEST(
            COALESCE(dp.u_photo_locations, 0),
            COALESCE(dv.u_video_locations, 0)
        ) as u_locations_used,
        GREATEST(
            COALESCE(dp.c_photo_locations, 0),
            COALESCE(dv.c_video_locations, 0)
        ) as c_locations_used
    FROM daily_photos dp
    FULL OUTER JOIN daily_videos dv ON dp.usage_date = dv.usage_date
    ORDER BY usage_date DESC;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔍 دالة البحث في المواقع =====
CREATE OR REPLACE FUNCTION search_locations(
    p_search_term TEXT DEFAULT NULL,
    p_location_type TEXT DEFAULT NULL,
    p_only_used BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    location_code TEXT,
    location_type TEXT,
    location_name TEXT,
    total_files INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN,
    is_available BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.location_code,
        l.location_type,
        l.location_name_ar as location_name,
        (l.total_photos + l.total_videos) as total_files,
        l.last_used_at,
        l.is_active,
        l.is_available
    FROM locations l
    WHERE 
        (p_search_term IS NULL OR l.location_code ILIKE '%' || p_search_term || '%')
        AND (p_location_type IS NULL OR l.location_type = p_location_type)
        AND (NOT p_only_used OR l.last_used_at IS NOT NULL)
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

-- ===== 📋 تعليقات على الدوال =====
COMMENT ON FUNCTION analyze_locations_usage(INTEGER) IS 'تحليل استخدام المواقع خلال فترة محددة';
COMMENT ON FUNCTION get_top_locations(INTEGER, TEXT) IS 'الحصول على أكثر المواقع استخداماً';
COMMENT ON FUNCTION get_locations_summary_stats() IS 'إحصائيات شاملة لجميع المواقع';
COMMENT ON FUNCTION analyze_daily_location_usage(INTEGER) IS 'تحليل الاستخدام اليومي للمواقع';
COMMENT ON FUNCTION search_locations(TEXT, TEXT, BOOLEAN) IS 'البحث في المواقع مع فلاتر متقدمة';
