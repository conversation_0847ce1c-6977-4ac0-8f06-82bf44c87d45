-- ===== 🔍 إعداد مرن للمستخدمين المتصلين =====
-- يتكيف مع أي هيكل موجود لجدول users

-- ===== 📊 فحص هيكل جدول users الحالي =====
SELECT 'فحص هيكل جدول users الحالي:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- ===== 🔧 إضافة الحقول المفقودة فقط =====
DO $$ 
BEGIN
    -- إضافة username إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') THEN
        ALTER TABLE users ADD COLUMN username TEXT;
        RAISE NOTICE 'تم إضافة حقل username';
    ELSE
        RAISE NOTICE 'حقل username موجود بالفعل';
    END IF;
    
    -- إضافة is_admin إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'تم إضافة حقل is_admin';
    ELSE
        RAISE NOTICE 'حقل is_admin موجود بالفعل';
    END IF;
    
    -- إضافة full_name إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE users ADD COLUMN full_name TEXT;
        RAISE NOTICE 'تم إضافة حقل full_name';
    ELSE
        RAISE NOTICE 'حقل full_name موجود بالفعل';
    END IF;
    
    -- إضافة location_code إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') THEN
        ALTER TABLE users ADD COLUMN location_code TEXT;
        RAISE NOTICE 'تم إضافة حقل location_code';
    ELSE
        RAISE NOTICE 'حقل location_code موجود بالفعل';
    END IF;
END $$;

-- ===== 📋 إنشاء جدول الجلسات =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id TEXT,
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 minutes'),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== 📋 فهارس لتحسين الأداء =====
CREATE INDEX IF NOT EXISTS idx_user_sessions_active_lookup 
ON user_sessions (is_active, expires_at, last_activity) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active 
ON user_sessions (user_id, is_active) WHERE is_active = TRUE;

-- ===== 🧹 دالة تنظيف الجلسات المنتهية =====
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE expired_count INTEGER;
BEGIN
    UPDATE user_sessions SET is_active = FALSE, ended_at = NOW(), end_reason = 'timeout'
    WHERE is_active = TRUE AND (expires_at < NOW() OR last_activity < NOW() - INTERVAL '30 minutes');
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END; $$;

-- ===== 👥 دالة مرنة للحصول على المستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
    user_id UUID,
    user_identifier TEXT,
    user_display_name TEXT,
    location_info TEXT,
    session_id UUID,
    device_info TEXT,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    ip_address INET,
    is_admin BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        COALESCE(
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') 
             THEN u.username ELSE NULL END),
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email') 
             THEN u.email ELSE NULL END),
            u.id::TEXT
        ) as user_identifier,
        COALESCE(
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') 
             THEN u.full_name ELSE NULL END),
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'name') 
             THEN u.name ELSE NULL END),
            'مستخدم'
        ) as user_display_name,
        COALESCE(
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') 
             THEN u.location_code ELSE NULL END),
            'غير محدد'
        ) as location_info,
        s.id as session_id,
        s.user_agent as device_info,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.ip_address,
        COALESCE(
            (CASE WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') 
             THEN u.is_admin ELSE FALSE END),
            FALSE
        ) as is_admin
    FROM user_sessions s 
    JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE 
      AND s.expires_at > NOW() 
      AND s.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 📊 دالة إحصائيات مرنة =====
CREATE OR REPLACE FUNCTION get_online_users_stats()
RETURNS TABLE (
    total_online INTEGER,
    admin_online INTEGER,
    regular_users_online INTEGER,
    active_sessions INTEGER,
    avg_session_duration INTERVAL
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT s.user_id)::INTEGER as total_online,
        COUNT(DISTINCT CASE 
            WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') 
                 AND u.is_admin = TRUE 
            THEN s.user_id 
            ELSE NULL 
        END)::INTEGER as admin_online,
        COUNT(DISTINCT CASE 
            WHEN NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') 
                 OR COALESCE(u.is_admin, FALSE) = FALSE 
            THEN s.user_id 
            ELSE NULL 
        END)::INTEGER as regular_users_online,
        COUNT(s.id)::INTEGER as active_sessions,
        AVG(NOW() - s.started_at) as avg_session_duration
    FROM user_sessions s 
    JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE 
      AND s.expires_at > NOW() 
      AND s.last_activity > NOW() - INTERVAL '30 minutes';
END; $$;

-- ===== ⏰ دالة تحديث نشاط الجلسة =====
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id UUID)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions SET last_activity = NOW(), expires_at = NOW() + INTERVAL '30 minutes'
    WHERE id = p_session_id AND is_active = TRUE AND expires_at > NOW();
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== 🛑 دالة إنهاء جلسة مستخدم =====
CREATE OR REPLACE FUNCTION end_user_session(p_session_id UUID, p_end_reason TEXT DEFAULT 'admin_terminated')
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions SET is_active = FALSE, ended_at = NOW(), end_reason = p_end_reason
    WHERE id = p_session_id AND is_active = TRUE;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== ✅ اختبار النظام =====
SELECT 'تنظيف الجلسات المنتهية...' as step;
SELECT cleanup_expired_sessions() as expired_cleaned;

SELECT 'المستخدمون المتصلون حالياً:' as step;
SELECT * FROM get_online_users();

SELECT 'إحصائيات المتصلين:' as step;
SELECT * FROM get_online_users_stats();

-- ===== 🎉 رسالة النجاح =====
SELECT '🎉 تم إعداد النظام المرن للمستخدمين المتصلين بنجاح!' as status;
SELECT 'النظام يتكيف مع أي هيكل موجود لجدول users' as flexibility;
SELECT 'قم بتشغيل التطبيق وسجل دخول لاختبار النظام' as next_step;
