-- ===== فحص حالة الرفع والملفات =====

-- 1. فحص الصور المرفوعة
SELECT 
    'الصور' as نوع_الملف,
    COUNT(*) as العدد_الكلي,
    COUNT(CASE WHEN image_url LIKE 'https://%' THEN 1 END) as المرفوعة_فعلياً,
    COUNT(CASE WHEN image_url LIKE 'local://%' THEN 1 END) as المحلية_فقط,
    ROUND(
        COUNT(CASE WHEN image_url LIKE 'https://%' THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as نسبة_النجاح
FROM photos;

-- 2. فحص الفيديوهات المرفوعة
SELECT 
    'الفيديوهات' as نوع_الملف,
    COUNT(*) as العدد_الكلي,
    COUNT(CASE WHEN video_url LIKE 'https://%' THEN 1 END) as المرفوعة_فعلياً,
    COUNT(CASE WHEN video_url LIKE 'local://%' THEN 1 END) as المحلية_فقط,
    ROUND(
        COUNT(CASE WHEN video_url LIKE 'https://%' THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as نسبة_النجاح
FROM videos;

-- 3. فحص قائمة الرفع الجديدة
SELECT 
    'قائمة الرفع' as نوع_الملف,
    COUNT(*) as العدد_الكلي,
    COUNT(CASE WHEN status = 'uploaded' THEN 1 END) as المرفوعة,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as في_الانتظار,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as الفاشلة,
    COUNT(CASE WHEN status = 'uploading' THEN 1 END) as جاري_الرفع
FROM upload_queue;

-- 4. آخر 10 صور
SELECT 
    file_name,
    CASE 
        WHEN image_url LIKE 'https://%' THEN 'مرفوعة ✅'
        WHEN image_url LIKE 'local://%' THEN 'محلية ❌'
        ELSE 'غير معروف'
    END as الحالة,
    location,
    username,
    created_at
FROM photos 
ORDER BY created_at DESC 
LIMIT 10;

-- 5. آخر 10 فيديوهات
SELECT 
    file_name,
    CASE 
        WHEN video_url LIKE 'https://%' THEN 'مرفوعة ✅'
        WHEN video_url LIKE 'local://%' THEN 'محلية ❌'
        ELSE 'غير معروف'
    END as الحالة,
    location,
    username,
    date_time
FROM videos 
ORDER BY date_time DESC 
LIMIT 10;

-- 6. فحص Storage Buckets
SELECT 
    id as اسم_البكت,
    public as عام,
    file_size_limit / 1024 / 1024 as الحد_الأقصى_MB,
    array_length(allowed_mime_types, 1) as عدد_الأنواع_المسموحة
FROM storage.buckets 
WHERE id IN ('photos', 'videos');

-- 7. فحص Storage Policies
SELECT 
    policyname as اسم_السياسة,
    cmd as النوع,
    CASE 
        WHEN 'authenticated' = ANY(roles) THEN 'مستخدمين مصادق عليهم'
        WHEN 'public' = ANY(roles) THEN 'عام'
        ELSE array_to_string(roles, ', ')
    END as الأدوار
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
ORDER BY cmd, policyname;

-- 8. فحص الملفات الفاشلة في قائمة الرفع
SELECT 
    file_name,
    file_type,
    status,
    upload_attempts,
    error_message,
    created_at
FROM upload_queue 
WHERE status = 'failed'
ORDER BY created_at DESC
LIMIT 10;

-- 9. إحصائيات حسب المستخدم
SELECT 
    username,
    COUNT(*) as عدد_الصور,
    COUNT(CASE WHEN image_url LIKE 'https://%' THEN 1 END) as المرفوعة
FROM photos 
WHERE username IS NOT NULL
GROUP BY username
ORDER BY عدد_الصور DESC;

-- 10. إحصائيات حسب الموقع
SELECT 
    location,
    COUNT(*) as عدد_الملفات
FROM (
    SELECT location FROM photos WHERE location IS NOT NULL
    UNION ALL
    SELECT location FROM videos WHERE location IS NOT NULL
) combined
GROUP BY location
ORDER BY عدد_الملفات DESC
LIMIT 10;
