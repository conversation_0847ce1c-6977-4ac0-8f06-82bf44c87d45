import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/logger.dart';

/// خدمة رفع محسنة مع إدارة أفضل للأخطاء
class EnhancedUploadService {
  static final EnhancedUploadService _instance = EnhancedUploadService._internal();
  factory EnhancedUploadService() => _instance;
  EnhancedUploadService._internal();

  final _logger = getLogger();
  final _supabase = Supabase.instance.client;
  
  Timer? _uploadTimer;
  bool _isUploading = false;
  
  // إعدادات الرفع محسنة
  static const int _maxRetries = 5; // زيادة المحاولات
  static const Duration _uploadTimeout = Duration(minutes: 3); // زيادة timeout للفيديوهات
  static const Duration _checkInterval = Duration(seconds: 20); // فحص أبطأ قليلاً

  /// بدء خدمة الرفع التلقائي
  void startAutoUpload() {
    _logger.i('🚀 بدء خدمة الرفع المحسنة...');
    
    _uploadTimer?.cancel();
    _uploadTimer = Timer.periodic(_checkInterval, (timer) {
      if (!_isUploading) {
        _processUploads();
      }
    });
  }

  /// إيقاف خدمة الرفع
  void stopAutoUpload() {
    _uploadTimer?.cancel();
    _logger.i('🛑 تم إيقاف خدمة الرفع');
  }

  /// إضافة ملف إلى قائمة الرفع
  Future<String?> addToUploadQueue({
    required String filePath,
    required String fileName,
    required String fileType, // 'photo' or 'video'
    required String location,
    String? username,
    int? fileSizeBytes,
    int? durationSeconds,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        _logger.e('❌ المستخدم غير مصادق عليه');
        return null;
      }

      // حساب حجم الملف إذا لم يكن محدد
      if (fileSizeBytes == null) {
        final file = File(filePath);
        if (await file.exists()) {
          fileSizeBytes = await file.length();
        }
      }

      // إضافة معلومات إضافية للـ metadata
      final enrichedMetadata = {
        ...?metadata,
        'duration_seconds': durationSeconds,
        'original_path': filePath,
        'added_at': DateTime.now().toIso8601String(),
      };

      // استخدام RPC لإضافة الملف إلى قائمة الرفع مع معالجة الأخطاء
      try {
        final result = await _supabase.rpc('add_to_upload_queue', params: {
          'p_file_name': fileName,
          'p_file_path': filePath,
          'p_file_type': fileType,
          'p_file_size_bytes': fileSizeBytes,
          'p_location': location,
          'p_username': username,
          'p_user_id': userId,
          'p_metadata': enrichedMetadata,
        });

        _logger.i('✅ تم إضافة $fileName إلى قائمة الرفع: $result');
        return result as String?;
      } catch (e) {
        if (e.toString().contains('permission denied') ||
            e.toString().contains('does not exist')) {
          _logger.w('⚠️ جدول upload_queue غير متاح، سيتم الرفع مباشرة');
          // يمكن إضافة رفع مباشر هنا كـ fallback
          return null;
        }
        rethrow;
      }

    } catch (e) {
      _logger.e('❌ فشل في إضافة الملف إلى قائمة الرفع: $e');
      return null;
    }
  }

  /// معالجة قائمة الرفع
  Future<void> _processUploads() async {
    if (_isUploading) return;
    
    try {
      _isUploading = true;

      // فحص الاتصال بالإنترنت
      if (!await _hasInternetConnection()) {
        _logger.d('⚠️ لا يوجد اتصال بالإنترنت');
        return;
      }

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        _logger.w('⚠️ المستخدم غير مصادق عليه');
        return;
      }

      // الحصول على الملفات المعلقة مع معالجة الأخطاء
      List<dynamic> pendingFiles = [];
      try {
        pendingFiles = await _supabase.rpc('get_pending_uploads', params: {
          'p_user_id': userId,
          'p_limit': 5, // معالجة 5 ملفات في المرة الواحدة
        }) as List<dynamic>;
      } catch (e) {
        if (e.toString().contains('permission denied') ||
            e.toString().contains('does not exist')) {
          _logger.w('⚠️ جدول upload_queue غير متاح، استخدام النظام القديم');
          return; // تخطي هذه المرة
        }
        rethrow;
      }

      if (pendingFiles.isEmpty) {
        _logger.d('📭 لا توجد ملفات معلقة للرفع');
        return;
      }

      _logger.i('📤 معالجة ${pendingFiles.length} ملف معلق...');

      // معالجة كل ملف
      for (final fileData in pendingFiles) {
        await _uploadSingleFile(fileData);
      }

    } catch (e) {
      _logger.e('❌ خطأ في معالجة قائمة الرفع: $e');
    } finally {
      _isUploading = false;
    }
  }

  /// رفع ملف واحد
  Future<void> _uploadSingleFile(Map<String, dynamic> fileData) async {
    final queueId = fileData['id'] as String;
    final fileName = fileData['file_name'] as String;
    final filePath = fileData['file_path'] as String;
    final fileType = fileData['file_type'] as String;

    try {
      _logger.i('📤 بدء رفع: $fileName');

      // تحديث حالة الرفع إلى "uploading"
      await _updateUploadStatus(queueId, 'uploading');

      // فحص وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('الملف غير موجود: $filePath');
      }

      // رفع الملف حسب النوع
      bool success = false;
      if (fileType == 'photo') {
        success = await _uploadPhoto(file, fileData);
      } else if (fileType == 'video') {
        success = await _uploadVideo(file, fileData);
      }

      if (success) {
        await _updateUploadStatus(queueId, 'uploaded');
        _logger.i('✅ تم رفع $fileName بنجاح');
        
        // حذف الملف المحلي بعد الرفع الناجح
        await _deleteLocalFile(filePath);
      } else {
        throw Exception('فشل في رفع الملف');
      }

    } catch (e) {
      _logger.e('❌ فشل في رفع $fileName: $e');
      await _updateUploadStatus(queueId, 'failed', e.toString());
    }
  }

  /// رفع صورة
  Future<bool> _uploadPhoto(File file, Map<String, dynamic> fileData) async {
    try {
      final fileName = fileData['file_name'] as String;
      final location = fileData['location'] as String;
      final username = fileData['username'] as String?;
      final fileSizeBytes = fileData['file_size_bytes'] as int?;

      // فحص إذا كانت الصورة موجودة بالفعل
      final existing = await _supabase
          .from('photos')
          .select('id')
          .eq('file_name', fileName)
          .maybeSingle();

      if (existing != null) {
        _logger.i('📸 الصورة موجودة بالفعل: $fileName');
        return true;
      }

      // رفع إلى Storage مع معالجة أخطاء محسنة
      String? imageUrl;
      try {
        // محاولة رفع مباشر بدون مجلد uploads أولاً
        try {
          await _supabase.storage
              .from('photos')
              .upload(fileName, file)
              .timeout(_uploadTimeout);

          imageUrl = _supabase.storage
              .from('photos')
              .getPublicUrl(fileName);

          _logger.i('✅ تم رفع الصورة مباشرة: $fileName');
        } catch (e1) {
          _logger.w('⚠️ فشل الرفع المباشر، محاولة مع مجلد uploads: $e1');

          // محاولة مع مجلد uploads
          try {
            await _supabase.storage
                .from('photos')
                .upload('uploads/$fileName', file)
                .timeout(_uploadTimeout);

            imageUrl = _supabase.storage
                .from('photos')
                .getPublicUrl('uploads/$fileName');

            _logger.i('✅ تم رفع الصورة في مجلد uploads: $fileName');
          } catch (e2) {
            _logger.w('⚠️ فشل رفع Storage، استخدام رابط محلي: $e2');
            imageUrl = 'local://photos/$fileName';
          }
        }
      } catch (e) {
        _logger.w('⚠️ فشل رفع Storage، استخدام رابط محلي: $e');
        imageUrl = 'local://photos/$fileName';
      }

      // تحليل كود الموقع
      final locationData = _parseLocationCode(location);

      // حفظ في قاعدة البيانات
      await _supabase.from('photos').insert({
        'file_name': fileName,
        'image_url': imageUrl,
        'storage_path': 'uploads/$fileName',
        'location': location,
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'username': username,
        'user_id': _supabase.auth.currentUser?.id,
        'file_size_bytes': fileSizeBytes,
        'upload_status': 'uploaded',
        'created_at': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      _logger.e('❌ خطأ في رفع الصورة: $e');
      return false;
    }
  }

  /// رفع فيديو
  Future<bool> _uploadVideo(File file, Map<String, dynamic> fileData) async {
    try {
      final fileName = fileData['file_name'] as String;
      final location = fileData['location'] as String;
      final username = fileData['username'] as String?;
      final fileSizeBytes = fileData['file_size_bytes'] as int?;
      final metadata = fileData['metadata'] as Map<String, dynamic>?;
      final durationSeconds = metadata?['duration_seconds'] as int?;

      // فحص إذا كان الفيديو موجود بالفعل
      final existing = await _supabase
          .from('videos')
          .select('id')
          .eq('file_name', fileName)
          .maybeSingle();

      if (existing != null) {
        _logger.i('🎬 الفيديو موجود بالفعل: $fileName');
        return true;
      }

      // رفع إلى Storage مع إعادة المحاولة
      String? videoUrl;
      String storagePath = 'uploads/$fileName';

      // محاولة رفع مع إعادة المحاولة
      videoUrl = await _uploadVideoWithRetry(file, fileName, storagePath);

      // تحليل كود الموقع
      final locationData = _parseLocationCode(location);

      // حفظ في قاعدة البيانات
      await _supabase.from('videos').insert({
        'file_name': fileName,
        'video_url': videoUrl,
        'storage_path': storagePath,
        'location': location,
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'username': username,
        'user_id': _supabase.auth.currentUser?.id,
        'file_size_bytes': fileSizeBytes,
        'duration_seconds': durationSeconds,
        'upload_status': 'uploaded',
        'date_time': DateTime.now().toIso8601String(),
      });

      return true;
    } catch (e) {
      _logger.e('❌ خطأ في رفع الفيديو: $e');
      return false;
    }
  }

  /// تحليل كود الموقع
  Map<String, String?> _parseLocationCode(String? location) {
    if (location == null || location.isEmpty) {
      return {'type': null, 'number': null};
    }

    final regex = RegExp(r'^([UC])(\d+)$');
    final match = regex.firstMatch(location);

    if (match != null) {
      return {
        'type': match.group(1),
        'number': match.group(2),
      };
    }

    return {'type': null, 'number': null};
  }

  /// تحديث حالة الرفع
  Future<void> _updateUploadStatus(String queueId, String status, [String? errorMessage]) async {
    try {
      await _supabase.rpc('update_upload_status', params: {
        'p_queue_id': queueId,
        'p_status': status,
        'p_error_message': errorMessage,
      });
    } catch (e) {
      _logger.e('❌ فشل في تحديث حالة الرفع: $e');
    }
  }

  /// فحص الاتصال بالإنترنت مع اختبار فعلي
  Future<bool> _hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        return false;
      }

      // اختبار فعلي للاتصال
      try {
        await _supabase
            .from('photos')
            .select('id')
            .limit(1)
            .timeout(const Duration(seconds: 5));
        return true;
      } catch (e) {
        _logger.w('⚠️ فشل اختبار الاتصال بقاعدة البيانات: $e');
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// حذف الملف المحلي
  Future<void> _deleteLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.d('🗑️ تم حذف الملف المحلي: $filePath');
      }
    } catch (e) {
      _logger.w('⚠️ فشل في حذف الملف المحلي: $e');
    }
  }

  /// تنظيف قائمة الرفع
  Future<void> cleanupUploadQueue() async {
    try {
      final result = await _supabase.rpc('cleanup_upload_queue', params: {
        'p_days_old': 7,
      });
      _logger.i('🧹 تم تنظيف $result عنصر من قائمة الرفع');
    } catch (e) {
      _logger.e('❌ فشل في تنظيف قائمة الرفع: $e');
    }
  }

  /// الحصول على إحصائيات الرفع
  Future<Map<String, dynamic>> getUploadStats() async {
    try {
      final stats = await _supabase
          .from('upload_stats')
          .select('*');
      
      return {
        'stats': stats,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      _logger.e('❌ فشل في الحصول على إحصائيات الرفع: $e');
      return {};
    }
  }

  /// رفع فيديو مع إعادة المحاولة
  Future<String> _uploadVideoWithRetry(File file, String fileName, String storagePath) async {
    const maxAttempts = 3;

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        _logger.i('🎬 محاولة رفع الفيديو #$attempt: $fileName');

        // محاولة videos bucket أولاً
        try {
          await _supabase.storage
              .from('videos')
              .upload(storagePath, file)
              .timeout(_uploadTimeout);

          final videoUrl = _supabase.storage
              .from('videos')
              .getPublicUrl(storagePath);

          _logger.i('✅ تم رفع الفيديو إلى videos bucket: $fileName');
          return videoUrl;
        } catch (e) {
          _logger.w('⚠️ فشل videos bucket في المحاولة #$attempt: $e');

          // محاولة photos bucket كـ fallback
          try {
            await _supabase.storage
                .from('photos')
                .upload(storagePath, file)
                .timeout(_uploadTimeout);

            final videoUrl = _supabase.storage
                .from('photos')
                .getPublicUrl(storagePath);

            _logger.i('✅ تم رفع الفيديو إلى photos bucket: $fileName');
            return videoUrl;
          } catch (e2) {
            _logger.w('⚠️ فشل photos bucket في المحاولة #$attempt: $e2');

            if (attempt == maxAttempts) {
              // آخر محاولة - استخدام رابط محلي
              _logger.w('⚠️ فشل رفع Storage، استخدام رابط محلي: $fileName');
              return 'local://videos/$fileName';
            }

            // انتظار قبل المحاولة التالية
            await Future.delayed(Duration(seconds: attempt * 2));
          }
        }
      } catch (e) {
        _logger.e('❌ خطأ في المحاولة #$attempt: $e');

        if (attempt == maxAttempts) {
          return 'local://videos/$fileName';
        }

        // انتظار قبل المحاولة التالية
        await Future.delayed(Duration(seconds: attempt * 3));
      }
    }

    return 'local://videos/$fileName';
  }

  /// إيقاف الخدمة
  void dispose() {
    stopAutoUpload();
  }
}
