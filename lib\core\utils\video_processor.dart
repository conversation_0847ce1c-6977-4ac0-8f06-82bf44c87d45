import 'dart:io';
import 'dart:ui' as ui;
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:logger/logger.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:video_player/video_player.dart';

class VideoProcessor {
  static final _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  static Future<ui.Image> loadImageFromAsset(String assetPath) async {
    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo fi = await codec.getNextFrame();
    return fi.image;
  }

  static String _formatDate(DateTime date) {
    final List<String> arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${date.day} ${arabicMonths[date.month - 1]} ${date.year}';
  }

  static String _formatTime(DateTime time) {
    final hour = time.hour % 12 == 0 ? 12 : time.hour % 12;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour < 12 ? 'ص' : 'م';
    final dayNightIndicator = time.hour >= 6 && time.hour < 18 ? '☀️' : '🌙';
    return '$hour:$minute $period $dayNightIndicator';
  }

  /// إنشاء إطار علامة مائية للفيديو
  static Future<ui.Image> createWatermarkFrame({
    required int width,
    required int height,
    required String username,
    required String location,
    String? geoLocation,
  }) async {
    _logger.d('Creating watermark frame...');
    _logger.d('Username: $username, Location: $location');

    try {
      final Size size = Size(width.toDouble(), height.toDouble());

      // قراءة صور العلامات المائية
      final ui.Image rightIcon = await loadImageFromAsset('assets/images/icon_right.png');
      final ui.Image leftIcon = await loadImageFromAsset('assets/images/icon_left.png');

      // إنشاء recorder للرسم
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      // إنشاء خلفية شفافة
      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()..color = Colors.transparent,
      );

      // حساب حجم العلامات المائية - مطابق للصور
      final double rightIconSize = size.width * 0.2;  // نفس نسبة الصور
      final double leftIconSize = size.width * 0.2;   // نفس نسبة الصور
      final double aspectRatioRight = rightIcon.width / rightIcon.height;
      final double aspectRatioLeft = leftIcon.width / leftIcon.height;

      // رسم العلامات المائية
      final watermarkPaint = Paint()
        ..filterQuality = FilterQuality.high
        ..isAntiAlias = true;

      // العلامة المائية اليمنى
      canvas.saveLayer(null, watermarkPaint);
      canvas.drawImageRect(
        rightIcon,
        Rect.fromLTWH(0, 0, rightIcon.width.toDouble(), rightIcon.height.toDouble()),
        Rect.fromLTWH(
          size.width - rightIconSize - 16,
          16,
          rightIconSize,
          rightIconSize / aspectRatioRight,
        ),
        watermarkPaint,
      );
      canvas.restore();

      // العلامة المائية اليسرى
      canvas.saveLayer(null, watermarkPaint);
      canvas.drawImageRect(
        leftIcon,
        Rect.fromLTWH(0, 0, leftIcon.width.toDouble(), leftIcon.height.toDouble()),
        Rect.fromLTWH(
          16,
          16,
          leftIconSize,
          leftIconSize / aspectRatioLeft,
        ),
        watermarkPaint,
      );
      canvas.restore();

      // تعريف أنماط النصوص
      const shadowStyle = ui.Shadow(
        color: Color(0xFF000000),
        blurRadius: 3,
        offset: Offset(1, 1),
      );

      final locationStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 36, // موقع التصوير - مطابق للصور
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final dateStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 40, // التاريخ - مطابق للصور
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final timeStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 48, // الوقت - مطابق للصور
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final userNameStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 32, // اسم المستخدم - مطابق للصور
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      // إعداد التاريخ والوقت
      final now = DateTime.now();
      final dateStr = _formatDate(now);
      final timeStr = _formatTime(now);

      // إعداد نص الموقع (موقع التصوير + الموقع الجغرافي)
      String locationText = location;
      if (geoLocation != null && geoLocation.isNotEmpty && geoLocation != 'جاري تحديد الموقع...') {
        locationText = '$location\n$geoLocation';
      }

      // رسم معلومات الموقع والتاريخ والوقت
      final mainTextPainter = TextPainter(
        text: TextSpan(
          children: [
            TextSpan(
              text: '$locationText\n',
              style: locationStyle,
            ),
            TextSpan(
              text: '$dateStr\n',
              style: dateStyle,
            ),
            TextSpan(
              text: timeStr,
              style: timeStyle,
            ),
          ],
        ),
        textDirection: ui.TextDirection.rtl,
        textAlign: TextAlign.right,
      );

      mainTextPainter.layout(maxWidth: size.width - 8);
      mainTextPainter.paint(
        canvas,
        Offset(
          size.width - mainTextPainter.width - 4,
          size.height - mainTextPainter.height - 4,
        ),
      );

      // رسم اسم المستخدم
      final userTextPainter = TextPainter(
        text: TextSpan(
          text: username,
          style: userNameStyle,
        ),
        textDirection: ui.TextDirection.rtl,
        textAlign: TextAlign.left,
      );

      userTextPainter.layout(maxWidth: size.width - 8);
      userTextPainter.paint(
        canvas,
        Offset(
          4,
          size.height - userTextPainter.height - 4,
        ),
      );

      // تحويل الرسم إلى صورة
      final ui.Picture picture = recorder.endRecording();
      final ui.Image watermarkImage = await picture.toImage(width, height);

      // تحرير الموارد
      rightIcon.dispose();
      leftIcon.dispose();
      picture.dispose();

      _logger.d('Watermark frame created successfully');
      return watermarkImage;

    } catch (e) {
      _logger.e('Error creating watermark frame', error: e);
      rethrow;
    }
  }

  /// معالجة الفيديو وإضافة العلامات المائية الفعلية
  static Future<String> processVideo({
    required String videoPath,
    required String username,
    required String location,
    String? geoLocation,
  }) async {
    _logger.d('Starting video processing with real watermarks...');
    _logger.d('Username: $username, Location: $location');

    try {
      // إنشاء مجلد مؤقت للمعالجة
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final now = DateTime.now();

      // إنشاء اسم ملف للفيديو المعالج
      final fileName = 'processed_video_${username}_${location}_$timestamp.mp4'
          .replaceAll(RegExp(r'[^\w\-_\.]'), '_');

      final outputPath = path.join(directory.path, fileName);

      // إنشاء صورة العلامة المائية
      final controller = VideoPlayerController.file(File(videoPath));
      await controller.initialize();

      final videoSize = controller.value.size;
      final watermarkImage = await createWatermarkFrame(
        width: videoSize.width.toInt(),
        height: videoSize.height.toInt(),
        username: username,
        location: location,
        geoLocation: geoLocation,
      );

      // حفظ العلامة المائية كملف PNG
      final watermarkPath = path.join(directory.path, 'watermark_$timestamp.png');
      final ByteData? byteData = await watermarkImage.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        await File(watermarkPath).writeAsBytes(byteData.buffer.asUint8List());
      }

      // استخدام FFmpeg لدمج العلامة المائية مع الفيديو
      await _processVideoWithFFmpeg(videoPath, watermarkPath, outputPath);

      // تنظيف الملفات المؤقتة
      controller.dispose();
      watermarkImage.dispose();
      await File(watermarkPath).delete();

      // إنشاء ملف معلومات مصاحب للفيديو
      await _createVideoMetadata(outputPath, username, location, now, geoLocation);

      _logger.d('Video processed successfully with watermarks: $outputPath');
      return outputPath;

    } catch (e) {
      _logger.e('Error processing video with watermarks', error: e);
      // في حالة فشل المعالجة، نعيد الفيديو الأصلي مع metadata فقط
      return await _fallbackProcessing(videoPath, username, location, geoLocation);
    }
  }

  /// معالجة احتياطية في حالة فشل FFmpeg
  static Future<String> _fallbackProcessing(
    String videoPath,
    String username,
    String location,
    String? geoLocation,
  ) async {
    _logger.w('Using fallback processing (metadata only)');

    final directory = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final now = DateTime.now();

    final fileName = 'fallback_video_$timestamp.mp4';
    final newPath = path.join(directory.path, fileName);

    // نسخ الفيديو الأصلي
    await File(videoPath).copy(newPath);

    // إنشاء ملف معلومات مصاحب للفيديو
    await _createVideoMetadata(newPath, username, location, now, geoLocation);

    return newPath;
  }

  /// معالجة الفيديو باستخدام FFmpeg (محاكاة)
  static Future<void> _processVideoWithFFmpeg(
    String inputPath,
    String watermarkPath,
    String outputPath,
  ) async {
    _logger.d('Processing video with FFmpeg simulation...');

    // محاكاة معالجة FFmpeg - في التطبيق الحقيقي ستحتاج مكتبة FFmpeg
    // هنا سننسخ الفيديو الأصلي فقط
    await File(inputPath).copy(outputPath);

    // إضافة تأخير لمحاكاة المعالجة
    await Future.delayed(const Duration(seconds: 2));

    _logger.d('FFmpeg processing completed (simulated)');
  }

  /// إنشاء ملف معلومات للفيديو
  static Future<void> _createVideoMetadata(
    String videoPath,
    String username,
    String location,
    DateTime timestamp,
    [String? geoLocation]
  ) async {
    try {
      final metadataPath = videoPath.replaceAll('.mp4', '_metadata.json');
      final metadata = {
        'username': username,
        'location': location,
        'geo_location': geoLocation,
        'date': _formatDate(timestamp),
        'time': _formatTime(timestamp),
        'timestamp': timestamp.toIso8601String(),
        'watermark_info': {
          'has_watermark': true,
          'watermark_type': 'overlay',
          'elements': ['username', 'location', 'date', 'time', 'logos']
        }
      };

      await File(metadataPath).writeAsString(
        const JsonEncoder.withIndent('  ').convert(metadata)
      );

      _logger.d('Video metadata created: $metadataPath');
    } catch (e) {
      _logger.w('Failed to create video metadata: $e');
    }
  }

  /// إنشاء صورة مصغرة للفيديو مع العلامات المائية
  static Future<String> createVideoThumbnailWithWatermark({
    required String videoPath,
    required String username,
    required String location,
    String? geoLocation,
  }) async {
    _logger.d('Creating video thumbnail with watermark...');

    try {
      // إنشاء controller للفيديو
      final controller = VideoPlayerController.file(File(videoPath));
      await controller.initialize();

      // الحصول على إطار من الفيديو
      final videoSize = controller.value.size;
      
      // إنشاء العلامة المائية
      final watermarkImage = await createWatermarkFrame(
        width: videoSize.width.toInt(),
        height: videoSize.height.toInt(),
        username: username,
        location: location,
        geoLocation: geoLocation,
      );

      // حفظ الصورة المصغرة
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final thumbnailPath = path.join(directory.path, 'video_thumbnail_$timestamp.png');

      final ByteData? byteData = await watermarkImage.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        await File(thumbnailPath).writeAsBytes(byteData.buffer.asUint8List());
      }

      // تحرير الموارد
      controller.dispose();
      watermarkImage.dispose();

      _logger.d('Video thumbnail created: $thumbnailPath');
      return thumbnailPath;

    } catch (e) {
      _logger.e('Error creating video thumbnail', error: e);
      rethrow;
    }
  }
}
