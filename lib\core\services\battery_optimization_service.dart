import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../utils/logger.dart';

/// خدمة تحسين استهلاك البطارية للعمل في الخلفية
class BatteryOptimizationService {
  static final BatteryOptimizationService _instance = BatteryOptimizationService._internal();
  factory BatteryOptimizationService() => _instance;
  BatteryOptimizationService._internal();

  final _logger = getLogger();

  /// طلب إيقاف تحسين البطارية للتطبيق
  Future<bool> requestIgnoreBatteryOptimizations() async {
    try {
      _logger.i('🔋 طلب إيقاف تحسين البطارية...');

      // فحص إذا كان الإذن ممنوح بالفعل
      bool isGranted = await Permission.ignoreBatteryOptimizations.isGranted;
      if (isGranted) {
        _logger.i('✅ إذن تحسين البطارية ممنوح بالفعل');
        return true;
      }

      // طلب الإذن
      PermissionStatus status = await Permission.ignoreBatteryOptimizations.request();
      
      if (status.isGranted) {
        _logger.i('✅ تم منح إذن تحسين البطارية');
        return true;
      } else {
        _logger.w('⚠️ تم رفض إذن تحسين البطارية');
        return false;
      }

    } catch (e) {
      _logger.e('❌ خطأ في طلب إذن تحسين البطارية: $e');
      return false;
    }
  }

  /// فحص حالة تحسين البطارية
  Future<bool> isBatteryOptimizationIgnored() async {
    try {
      return await Permission.ignoreBatteryOptimizations.isGranted;
    } catch (e) {
      _logger.e('❌ خطأ في فحص حالة تحسين البطارية: $e');
      return false;
    }
  }

  /// طلب إضافة التطبيق لقائمة التطبيقات المحمية (للهواتف الصينية)
  Future<void> requestAutoStartPermission() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final manufacturer = androidInfo.manufacturer.toLowerCase();

      _logger.i('🏭 الشركة المصنعة: $manufacturer');

      // للهواتف الصينية التي تحتاج إعدادات خاصة
      if (_isChineseManufacturer(manufacturer)) {
        _logger.i('📱 هاتف صيني - قد تحتاج إعدادات إضافية');
        await _showAutoStartInstructions(manufacturer);
      }

    } catch (e) {
      _logger.e('❌ خطأ في فحص نوع الهاتف: $e');
    }
  }

  /// فحص إذا كان الهاتف من شركة صينية
  bool _isChineseManufacturer(String manufacturer) {
    const chineseManufacturers = [
      'xiaomi', 'huawei', 'oppo', 'vivo', 'oneplus', 
      'realme', 'honor', 'meizu', 'zte', 'lenovo'
    ];
    
    return chineseManufacturers.any((brand) => manufacturer.contains(brand));
  }

  /// عرض تعليمات Auto Start للهواتف الصينية
  Future<void> _showAutoStartInstructions(String manufacturer) async {
    String instructions = '';
    
    switch (manufacturer) {
      case 'xiaomi':
        instructions = '''
للسماح للتطبيق بالعمل في الخلفية:
1. اذهب إلى الإعدادات
2. التطبيقات
3. إدارة التطبيقات
4. Moon Memory
5. فعل "التشغيل التلقائي"
6. فعل "العمل في الخلفية"
        ''';
        break;
      case 'huawei':
        instructions = '''
للسماح للتطبيق بالعمل في الخلفية:
1. اذهب إلى الإعدادات
2. التطبيقات
3. Moon Memory
4. البطارية
5. فعل "التشغيل التلقائي"
6. فعل "العمل في الخلفية"
        ''';
        break;
      case 'oppo':
      case 'realme':
        instructions = '''
للسماح للتطبيق بالعمل في الخلفية:
1. اذهب إلى الإعدادات
2. البطارية
3. تحسين الطاقة
4. التطبيقات عالية الاستهلاك
5. Moon Memory
6. فعل "السماح بالعمل في الخلفية"
        ''';
        break;
      default:
        instructions = '''
للسماح للتطبيق بالعمل في الخلفية:
1. اذهب إلى إعدادات الهاتف
2. التطبيقات
3. Moon Memory
4. البطارية أو الطاقة
5. فعل "العمل في الخلفية" أو "التشغيل التلقائي"
        ''';
    }

    _logger.i('📋 تعليمات Auto Start:\n$instructions');
  }

  /// إعدادات ذكية لتوفير البطارية
  Map<String, dynamic> getSmartBatterySettings() {
    return {
      // تكرار التحديث حسب حالة التطبيق
      'foreground_interval': 15, // 15 ثانية عندما التطبيق مفتوح
      'background_interval': 30, // 30 ثانية في الخلفية
      'screen_off_interval': 60, // دقيقة عندما الشاشة مقفلة
      
      // دقة الموقع حسب الحالة
      'foreground_accuracy': 'high',     // دقة عالية عندما مفتوح
      'background_accuracy': 'medium',   // دقة متوسطة في الخلفية
      'screen_off_accuracy': 'low',      // دقة منخفضة عندما مقفل
      
      // إعدادات إضافية
      'max_location_age': 300,           // 5 دقائق كحد أقصى لعمر الموقع
      'min_distance_filter': 10,         // 10 متر كحد أدنى للحركة
      'timeout_duration': 15,            // 15 ثانية timeout
    };
  }

  /// فحص مستوى البطارية
  Future<int?> getBatteryLevel() async {
    try {
      const platform = MethodChannel('battery');
      final int batteryLevel = await platform.invokeMethod('getBatteryLevel');
      return batteryLevel;
    } catch (e) {
      _logger.e('❌ خطأ في فحص مستوى البطارية: $e');
      return null;
    }
  }

  /// تحديد إعدادات التحديث حسب مستوى البطارية
  Map<String, int> getIntervalByBatteryLevel(int batteryLevel) {
    if (batteryLevel <= 15) {
      // بطارية منخفضة - تحديث كل دقيقتين
      return {
        'interval': 120,
        'accuracy': 1, // low
      };
    } else if (batteryLevel <= 30) {
      // بطارية متوسطة - تحديث كل دقيقة
      return {
        'interval': 60,
        'accuracy': 2, // medium
      };
    } else {
      // بطارية جيدة - تحديث عادي
      return {
        'interval': 30,
        'accuracy': 3, // high
      };
    }
  }

  /// تسجيل معلومات البطارية
  Future<void> logBatteryInfo() async {
    try {
      final batteryLevel = await getBatteryLevel();
      final isOptimized = await isBatteryOptimizationIgnored();
      
      _logger.i('''
🔋 معلومات البطارية:
   - المستوى: ${batteryLevel ?? 'غير معروف'}%
   - تحسين البطارية: ${isOptimized ? 'مُعطل' : 'مُفعل'}
      ''');

      if (batteryLevel != null && batteryLevel <= 20) {
        _logger.w('⚠️ مستوى البطارية منخفض - سيتم تقليل تكرار التحديث');
      }

    } catch (e) {
      _logger.e('❌ خطأ في تسجيل معلومات البطارية: $e');
    }
  }
}
