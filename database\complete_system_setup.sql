-- إعد<PERSON> النظام الكامل لتطبيق Moon Memory
-- Complete System Setup for Moon Memory App
-- Date: 2025-01-15
-- يشمل: المستخدمين، الأجهزة، البصمة الرقمية، الصور، الفيديو، المواقع، الصلاحيات، والإدارة

-- ===== حذف الجداول الموجودة (للبدء من جديد) =====
-- DROP TABLE IF EXISTS admin_logs CASCADE;
-- DROP TABLE IF EXISTS user_sessions CASCADE;
-- DROP TABLE IF EXISTS videos CASCADE;
-- DROP TABLE IF EXISTS photos CASCADE;
-- DROP TABLE IF EXISTS devices CASCADE;
-- DROP TABLE IF EXISTS users CASCADE;

-- ===== إنشاء جدول المستخدمين المحسن =====
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    avatar_url TEXT,
    
    -- معلومات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user' CHECK (account_type IN ('admin', 'user', 'supervisor')),
    
    -- إعدادات الحساب
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    
    -- معلومات إضافية
    department TEXT,
    position TEXT,
    notes TEXT,
    
    -- التوقيتات
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    -- قيود
    CONSTRAINT valid_national_id CHECK (LENGTH(national_id) = 10 AND national_id ~ '^\d{10}$')
);

-- ===== إنشاء جدول الأجهزة المحسن =====
CREATE TABLE IF NOT EXISTS devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- البصمة الرقمية المتقدمة
    device_fingerprint TEXT NOT NULL UNIQUE,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    
    -- معلومات الجهاز الأساسية
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    
    -- معلومات مجمعة للبصمة
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    
    -- نقاط الثقة والأمان
    confidence_score DECIMAL(5,2) DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    trust_level TEXT DEFAULT 'untrusted' CHECK (trust_level IN ('high', 'medium', 'low', 'untrusted', 'suspicious', 'blocked')),
    
    -- إدارة المصادقة والأمان
    auth_attempts INTEGER DEFAULT 0 CHECK (auth_attempts >= 0),
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- التوقيتات
    last_verified_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود إضافية
    CONSTRAINT unique_user_android_id UNIQUE (user_id, android_id),
    CONSTRAINT valid_trust_level CHECK (trust_level IS NOT NULL)
);

-- ===== إنشاء جدول الصور المحسن =====
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    
    -- نظام المواقع الجديد
    location TEXT, -- للتوافق مع النظام القديم
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED,
    
    -- معلومات المستخدم والتقاط
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وفهرسة
    sort_order INTEGER,
    tags TEXT[], -- مصفوفة من العلامات
    description TEXT,
    
    -- معلومات تقنية
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الفيديو المحسن =====
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    
    -- نظام المواقع الجديد
    location TEXT, -- للتوافق مع النظام القديم
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED,
    
    -- معلومات المستخدم والتقاط
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وفهرسة
    sort_order INTEGER,
    tags TEXT[], -- مصفوفة من العلامات
    description TEXT,
    
    -- معلومات تقنية للفيديو
    resolution TEXT, -- مثل "1920x1080"
    fps INTEGER, -- إطارات في الثانية
    codec TEXT,
    bitrate INTEGER,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    thumbnail_url TEXT,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول جلسات المستخدمين =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    
    -- معلومات الجلسة
    session_token TEXT UNIQUE,
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيتات
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    
    -- حالة الجلسة
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT -- 'logout', 'timeout', 'admin_terminated', etc.
);

-- ===== إنشاء جدول سجلات الإدارة =====
CREATE TABLE IF NOT EXISTS admin_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) NOT NULL,
    target_user_id UUID REFERENCES users(id),
    
    -- معلومات العملية
    action TEXT NOT NULL, -- 'create_user', 'reset_password', 'block_user', etc.
    entity_type TEXT, -- 'user', 'device', 'photo', 'video'
    entity_id UUID,
    
    -- تفاصيل العملية
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    
    -- معلومات إضافية
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيت
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الإحصائيات =====
CREATE TABLE IF NOT EXISTS system_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_date DATE DEFAULT CURRENT_DATE,
    
    -- إحصائيات المستخدمين
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users_today INTEGER DEFAULT 0,
    
    -- إحصائيات الأجهزة
    total_devices INTEGER DEFAULT 0,
    active_devices INTEGER DEFAULT 0,
    blocked_devices INTEGER DEFAULT 0,
    
    -- إحصائيات الملفات
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    photos_uploaded_today INTEGER DEFAULT 0,
    videos_uploaded_today INTEGER DEFAULT 0,
    
    -- إحصائيات التخزين
    total_storage_used_mb BIGINT DEFAULT 0,
    photos_storage_mb BIGINT DEFAULT 0,
    videos_storage_mb BIGINT DEFAULT 0,
    
    -- إحصائيات المواقع
    u_locations_used INTEGER DEFAULT 0,
    c_locations_used INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(stat_date)
);

-- ===== تفعيل Row Level Security =====
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_stats ENABLE ROW LEVEL SECURITY;

-- ===== إنشاء السياسات الأمنية =====

-- سياسات المستخدمين
DROP POLICY IF EXISTS users_policy ON users;
CREATE POLICY users_policy ON users
    FOR ALL
    USING (auth.uid() = id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الأجهزة
DROP POLICY IF EXISTS devices_policy ON devices;
CREATE POLICY devices_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الصور
DROP POLICY IF EXISTS photos_policy ON photos;
CREATE POLICY photos_policy ON photos
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الفيديو
DROP POLICY IF EXISTS videos_policy ON videos;
CREATE POLICY videos_policy ON videos
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الجلسات
DROP POLICY IF EXISTS sessions_policy ON user_sessions;
CREATE POLICY sessions_policy ON user_sessions
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات سجلات الإدارة (للمشرفين فقط)
DROP POLICY IF EXISTS admin_logs_policy ON admin_logs;
CREATE POLICY admin_logs_policy ON admin_logs
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الإحصائيات (للمشرفين فقط)
DROP POLICY IF EXISTS stats_policy ON system_stats;
CREATE POLICY stats_policy ON system_stats
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- ===== إنشاء الفهارس للأداء =====

-- فهارس المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_national_id ON users(national_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);
CREATE INDEX IF NOT EXISTS idx_users_account_type ON users(account_type);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- فهارس الأجهزة
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);
CREATE INDEX IF NOT EXISTS idx_devices_trust_level ON devices(trust_level);
CREATE INDEX IF NOT EXISTS idx_devices_is_active ON devices(is_active);
CREATE INDEX IF NOT EXISTS idx_devices_is_blocked ON devices(is_blocked);
CREATE INDEX IF NOT EXISTS idx_devices_last_verified ON devices(last_verified_at);

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_status ON photos(status);
CREATE INDEX IF NOT EXISTS idx_photos_tags ON photos USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON photos(location_type, location_number, capture_timestamp, username);

-- فهارس الفيديو
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_status ON videos(status);
CREATE INDEX IF NOT EXISTS idx_videos_tags ON videos USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON videos(location_type, location_number, capture_timestamp, username);

-- فهارس الجلسات
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON user_sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_sessions_started_at ON user_sessions(started_at);

-- فهارس سجلات الإدارة
CREATE INDEX IF NOT EXISTS idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_target_user_id ON admin_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_action ON admin_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_logs_entity_type ON admin_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_admin_logs_created_at ON admin_logs(created_at);

-- ===== إنشاء الدوال المساعدة =====

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للصور
CREATE OR REPLACE FUNCTION update_photo_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM photos
            WHERE location_type = NEW.location_type
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للفيديو
CREATE OR REPLACE FUNCTION update_video_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM videos
            WHERE location_type = NEW.location_type
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتسجيل عمليات الإدارة
CREATE OR REPLACE FUNCTION log_admin_action()
RETURNS TRIGGER AS $$
DECLARE
    admin_user_id UUID;
    action_type TEXT;
    entity_type TEXT;
BEGIN
    -- الحصول على معرف المشرف الحالي
    admin_user_id := auth.uid();

    -- تحديد نوع العملية
    IF TG_OP = 'INSERT' THEN
        action_type := 'CREATE';
    ELSIF TG_OP = 'UPDATE' THEN
        action_type := 'UPDATE';
    ELSIF TG_OP = 'DELETE' THEN
        action_type := 'DELETE';
    END IF;

    -- تحديد نوع الكيان
    entity_type := TG_TABLE_NAME;

    -- تسجيل العملية
    INSERT INTO admin_logs (
        admin_id,
        target_user_id,
        action,
        entity_type,
        entity_id,
        description,
        old_values,
        new_values
    ) VALUES (
        admin_user_id,
        CASE
            WHEN TG_TABLE_NAME = 'users' THEN
                CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END
            WHEN TG_TABLE_NAME IN ('devices', 'photos', 'videos') THEN
                CASE WHEN TG_OP = 'DELETE' THEN OLD.user_id ELSE NEW.user_id END
            ELSE NULL
        END,
        action_type || '_' || UPPER(entity_type),
        entity_type,
        CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
        'Admin action: ' || action_type || ' on ' || entity_type,
        CASE WHEN TG_OP IN ('UPDATE', 'DELETE') THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END
    );

    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الإحصائيات اليومية
CREATE OR REPLACE FUNCTION update_daily_stats()
RETURNS VOID AS $$
DECLARE
    today_date DATE := CURRENT_DATE;
BEGIN
    INSERT INTO system_stats (
        stat_date,
        total_users,
        active_users,
        new_users_today,
        total_devices,
        active_devices,
        blocked_devices,
        total_photos,
        total_videos,
        photos_uploaded_today,
        videos_uploaded_today,
        total_storage_used_mb,
        photos_storage_mb,
        videos_storage_mb,
        u_locations_used,
        c_locations_used
    ) VALUES (
        today_date,
        (SELECT COUNT(*) FROM users),
        (SELECT COUNT(*) FROM users WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM devices),
        (SELECT COUNT(*) FROM devices WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM devices WHERE is_blocked = TRUE),
        (SELECT COUNT(*) FROM photos WHERE status = 'active'),
        (SELECT COUNT(*) FROM videos WHERE status = 'active'),
        (SELECT COUNT(*) FROM photos WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM videos WHERE DATE(created_at) = today_date),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active') +
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'U' AND status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'C' AND status = 'active')
    )
    ON CONFLICT (stat_date)
    DO UPDATE SET
        total_users = EXCLUDED.total_users,
        active_users = EXCLUDED.active_users,
        new_users_today = EXCLUDED.new_users_today,
        total_devices = EXCLUDED.total_devices,
        active_devices = EXCLUDED.active_devices,
        blocked_devices = EXCLUDED.blocked_devices,
        total_photos = EXCLUDED.total_photos,
        total_videos = EXCLUDED.total_videos,
        photos_uploaded_today = EXCLUDED.photos_uploaded_today,
        videos_uploaded_today = EXCLUDED.videos_uploaded_today,
        total_storage_used_mb = EXCLUDED.total_storage_used_mb,
        photos_storage_mb = EXCLUDED.photos_storage_mb,
        videos_storage_mb = EXCLUDED.videos_storage_mb,
        u_locations_used = EXCLUDED.u_locations_used,
        c_locations_used = EXCLUDED.c_locations_used,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- ===== تطبيق المحفزات =====

-- محفزات تحديث updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON devices;
CREATE TRIGGER update_devices_updated_at
    BEFORE UPDATE ON devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_photos_updated_at ON photos;
CREATE TRIGGER update_photos_updated_at
    BEFORE UPDATE ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_videos_updated_at ON videos;
CREATE TRIGGER update_videos_updated_at
    BEFORE UPDATE ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- محفزات الترقيم التلقائي
DROP TRIGGER IF EXISTS trigger_update_photo_sort_order ON photos;
CREATE TRIGGER trigger_update_photo_sort_order
    BEFORE INSERT ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_sort_order();

DROP TRIGGER IF EXISTS trigger_update_video_sort_order ON videos;
CREATE TRIGGER trigger_update_video_sort_order
    BEFORE INSERT ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_sort_order();

-- ===== إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- ===== إنشاء المستخدم المشرف الافتراضي =====
-- ملاحظة: يجب تشغيل هذا بعد إنشاء المستخدم في Auth
-- INSERT INTO users (
--     id,
--     national_id,
--     full_name,
--     email,
--     is_admin,
--     account_type,
--     created_at
-- ) VALUES (
--     'admin-uuid-here'::UUID,
--     '**********',
--     'مشرف النظام',
--     '<EMAIL>',
--     TRUE,
--     'admin',
--     NOW()
-- ) ON CONFLICT (id) DO NOTHING;

-- ===== رسائل النجاح والاختبار =====
SELECT 'تم إعداد النظام الكامل بنجاح! ✅' as status;

-- عرض الجداول المُنشأة
SELECT 'الجداول المُنشأة:' as info;
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'devices', 'photos', 'videos', 'user_sessions', 'admin_logs', 'system_stats')
ORDER BY table_name;

-- عرض الدوال المُنشأة
SELECT 'الدوال المُنشأة:' as info;
SELECT routine_name
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_type = 'FUNCTION'
AND routine_name IN ('update_updated_at_column', 'update_photo_sort_order', 'update_video_sort_order', 'log_admin_action', 'update_daily_stats')
ORDER BY routine_name;

-- تحديث الإحصائيات الأولية
SELECT update_daily_stats();

SELECT 'النظام جاهز للاستخدام! 🚀' as final_message;
