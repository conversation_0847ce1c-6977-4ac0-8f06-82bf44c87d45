-- ===== فحص شامل لقاعدة البيانات المحلية =====
-- استخدم: psql -U postgres -d moon_memory_local -f analyze_local_database.sql

\echo '========== معلومات قاعدة البيانات =========='
SELECT current_database() as "قاعدة البيانات الحالية", version() as "إصدار PostgreSQL";

\echo '========== الجداول الموجودة =========='
SELECT 
    schemaname as "المخطط",
    tablename as "اسم الجدول",
    tableowner as "المالك"
FROM pg_tables 
WHERE schemaname IN ('public', 'auth', 'storage')
ORDER BY schemaname, tablename;

\echo '========== بنية جدول public.users =========='
\d public.users

\echo '========== بنية جدول auth.users =========='
\d auth.users

\echo '========== بنية جدول photos =========='
\d public.photos

\echo '========== بنية جدول videos =========='
\d public.videos

\echo '========== بنية جدول devices =========='
\d public.devices

\echo '========== بنية جدول user_sessions =========='
\d public.user_sessions

\echo '========== بنية جدول upload_queue =========='
\d public.upload_queue

\echo '========== عدد السجلات في كل جدول =========='
SELECT 
    'public.users' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.users
UNION ALL
SELECT 
    'auth.users' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM auth.users
UNION ALL
SELECT 
    'public.photos' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.photos
UNION ALL
SELECT 
    'public.videos' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.videos
UNION ALL
SELECT 
    'public.devices' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.devices
UNION ALL
SELECT 
    'public.user_sessions' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.user_sessions
UNION ALL
SELECT 
    'public.upload_queue' as "الجدول",
    COUNT(*) as "عدد السجلات"
FROM public.upload_queue;

\echo '========== المفاتيح الخارجية =========='
SELECT
    tc.table_name as "الجدول",
    kcu.column_name as "العمود",
    ccu.table_name as "يشير إلى جدول",
    ccu.column_name as "يشير إلى عمود"
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
ORDER BY tc.table_name;

\echo '========== عينة من البيانات =========='
\echo 'آخر 3 مستخدمين في public.users:'
SELECT id, full_name, email, created_at FROM public.users ORDER BY created_at DESC LIMIT 3;

\echo 'آخر 3 مستخدمين في auth.users:'
SELECT id, email, created_at FROM auth.users ORDER BY created_at DESC LIMIT 3;

\echo '========== انتهى التحليل =========='
