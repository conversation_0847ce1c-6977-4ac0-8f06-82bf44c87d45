import 'dart:io';
import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'cache_service.dart';


class VideosService {
  static final _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  final SupabaseClient _client;
  final CacheService _cacheService;
  final String _videosBucket = 'videos'; // استخدام bucket الفيديوهات
  
  String? _currentUserId;
  String? _currentUsername;

  VideosService(this._client, this._cacheService) {
    _initializeUser();
  }

  void _initializeUser() {
    final user = _client.auth.currentUser;
    if (user != null) {
      _currentUserId = user.id;
      _currentUsername = user.userMetadata?['full_name'] ?? 'مستخدم';
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      return !connectivityResults.contains(ConnectivityResult.none);
    } catch (e) {
      _logger.w('Error checking internet connection: $e');
      return false;
    }
  }

  /// تنظيف معرف المستخدم للاستخدام في المسارات
  String _sanitizeUserId(String userId) {
    return userId.replaceAll(RegExp(r'[^\w\-]'), '_');
  }

  /// رفع فيديو إلى التخزين مع نظام ذكي للحفظ
  Future<String?> uploadVideo(String videoPath, String? location) async {
    _logger.i('🎬 === VideosService.uploadVideo called ===');
    _logger.i('🎬 Video path: $videoPath');
    _logger.i('🎬 Location: $location');

    try {
      final userId = _currentUserId;
      _logger.i('Current user ID: $userId');

      if (userId == null) {
        _logger.e('User not logged in');
        throw 'يجب تسجيل الدخول أولاً';
      }

      // فحص الاتصال بالإنترنت
      _logger.i('Checking internet connection...');
      final hasInternet = await _checkInternetConnection();
      _logger.i('Has internet: $hasInternet');

      if (hasInternet) {
        // رفع مباشر مع الإنترنت
        _logger.i('Starting direct upload...');
        return await _uploadDirectly(videoPath, location, userId);
      } else {
        // حفظ محلي بدون إنترنت
        _logger.i('No internet, saving offline...');
        await _saveOffline(videoPath, location, userId);
        return null; // لا يوجد storage path للملفات المحفوظة محلياً
      }
    } catch (e) {
      _logger.e('Error processing video: $e');
      rethrow;
    }
  }

  /// رفع مباشر مع الإنترنت
  Future<String> _uploadDirectly(String videoPath, String? location, String userId) async {
    _logger.i('🎬 === _uploadDirectly called ===');
    _logger.i('🎬 Video path: $videoPath');
    _logger.i('🎬 Location: $location');
    _logger.i('🎬 User ID: $userId');

    final file = File(videoPath);
    if (!await file.exists()) {
      throw Exception('Video file does not exist: $videoPath');
    }
    final originalSize = await file.length();
    _logger.i('🎬 File exists, size: $originalSize bytes (${(originalSize / 1024 / 1024).toStringAsFixed(2)} MB)');

    // تحسين: زيادة timeout للملفات الكبيرة
    final timeoutDuration = originalSize > 10 * 1024 * 1024
        ? const Duration(minutes: 5)  // 5 دقائق للملفات أكبر من 10MB
        : const Duration(minutes: 2); // 2 دقيقة للملفات الصغيرة

    _logger.i('🎬 Upload timeout set to: ${timeoutDuration.inMinutes} minutes');

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(videoPath);
    final fileName = 'video_$timestamp$extension';
    final safeUserId = _sanitizeUserId(userId);
    final storagePath = 'users/$safeUserId/$fileName';

    _logger.i('🎬 Generated fileName: $fileName');
    _logger.i('🎬 Storage path: $storagePath');

    // فحص إذا كان الفيديو مرفوع بالفعل
    _logger.i('🎬 Checking if video already exists...');
    try {
      final existingVideo = await _client
          .from('videos')
          .select('id, file_name')
          .eq('file_name', fileName)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingVideo != null) {
        _logger.i('🎬 Video already exists in database: $fileName');
        return storagePath; // إرجاع المسار الموجود
      }
      _logger.i('🎬 Video does not exist, proceeding with upload...');
    } catch (e) {
      _logger.w('🎬 Error checking existing video: $e');
      // المتابعة مع الرفع في حالة الخطأ
    }

    // حفظ نسخة في التخزين المؤقت
    _logger.i('🎬 Caching file...');
    await _cacheService.cacheFile(fileName, file);
    _logger.i('🎬 File cached successfully');

    // رفع الفيديو إلى التخزين (مع fallback)
    _logger.i('🎬 Starting upload to storage...');
    _logger.i('🎬 Trying videos bucket: $_videosBucket');

    // رفع الفيديو مباشرة بدون فحص buckets (لتجنب مشاكل المصادقة)
    try {
      _logger.i('🎬 Attempting direct upload to videos bucket...');

      // محاولة رفع إلى videos bucket أولاً مع timeout
      try {
        await _client.storage.from(_videosBucket).upload(
              storagePath,
              file,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: true,
              ),
            ).timeout(
              timeoutDuration,
              onTimeout: () {
                _logger.w('🎬 ⏰ Upload timeout after ${timeoutDuration.inMinutes} minutes');
                throw 'Upload timeout';
              },
            );
        _logger.i('🎬 ✅ Video uploaded to videos bucket: $storagePath');
      } catch (videosError) {
        _logger.w('🎬 ⚠️ Videos bucket failed, trying photos bucket: $videosError');
        // fallback إلى photos bucket مع timeout
        await _client.storage.from('photos').upload(
              storagePath,
              file,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: true,
              ),
            ).timeout(
              timeoutDuration,
              onTimeout: () {
                _logger.w('🎬 ⏰ Photos bucket upload timeout after ${timeoutDuration.inMinutes} minutes');
                throw 'Upload timeout to photos bucket';
              },
            );
        _logger.i('🎬 ✅ Video uploaded to photos bucket: $storagePath');
      }
    } catch (e) {
      _logger.e('🎬 ❌ Upload failed: $e');
      _logger.e('🎬 ❌ Error type: ${e.runtimeType}');
      _logger.e('🎬 ❌ Error details: ${e.toString()}');
      rethrow;
    }

    // الحصول على رابط الفيديو العام
    _logger.i('🎬 Getting video URL...');
    final videoUrl = await getVideoUrl(storagePath);
    if (videoUrl == null) {
      _logger.e('🎬 ❌ Failed to get video URL');
      throw 'فشل في الحصول على رابط الفيديو';
    }
    _logger.i('🎬 ✅ Video URL obtained: ${videoUrl.substring(0, 50)}...');

    // تحليل كود الموقع الجديد
    final locationData = _parseLocationCode(location);
    final currentTime = DateTime.now();

    // إنشاء سجل في قاعدة البيانات (جدول videos الصحيح)
    _logger.i('🎬 Inserting video record into database...');
    _logger.i('🎬 Username: $_currentUsername');
    _logger.i('🎬 Location data: $locationData');

    try {
      await _client.from('videos').insert({
        'user_id': userId,
        'file_name': fileName, // إضافة file_name المطلوب
        'storage_path': storagePath,
        'video_url': videoUrl,
        'date_time': currentTime.toIso8601String(),
        'location': location, // الحقل القديم للتوافق
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'username': _currentUsername,
        'capture_timestamp': currentTime.toIso8601String(),
        'upload_timestamp': currentTime.toIso8601String(),
      });
      _logger.i('🎬 ✅ Video record inserted successfully');
    } catch (e) {
      _logger.e('🎬 ❌ Failed to insert video record: $e');
      rethrow;
    }

    _logger.i('🎬 🎉 Video uploaded successfully: $storagePath');

    // حذف الفيديو من المعرض المحلي بعد الرفع بنجاح
    await _deleteVideoFromLocalGallery(videoPath);

    return storagePath;
  }

  /// حفظ محلي بدون إنترنت
  Future<void> _saveOffline(String videoPath, String? location, String userId) async {
    // إنشاء مجلد الوسائط المحلية
    final directory = await getApplicationDocumentsDirectory();
    final offlineDir = Directory('${directory.path}/offline_videos');
    if (!await offlineDir.exists()) {
      await offlineDir.create(recursive: true);
    }

    // نسخ الفيديو إلى مجلد الوسائط المحلية
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(videoPath);
    final fileName = 'video_offline_$timestamp$extension';
    final savedPath = '${offlineDir.path}/$fileName';

    await File(videoPath).copy(savedPath);

    // حفظ معلومات الفيديو للرفع لاحقاً
    await _saveOfflineMetadata(savedPath, fileName, location, userId);

    // حذف الملف المؤقت
    await File(videoPath).delete();

    _logger.i('Video saved offline: $savedPath');
  }

  /// حفظ معلومات الفيديو المحفوظ محلياً
  Future<void> _saveOfflineMetadata(String savedPath, String fileName, String? location, String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final offlineVideos = prefs.getStringList('offline_videos') ?? [];

    final metadata = {
      'path': savedPath,
      'fileName': fileName,
      'location': location,
      'userId': userId,
      'username': _currentUsername,
      'timestamp': DateTime.now().toIso8601String(),
    };

    offlineVideos.add(jsonEncode(metadata));
    await prefs.setStringList('offline_videos', offlineVideos);
  }

  /// رفع ومشاركة فيديو
  Future<void> uploadAndShareVideo(String videoPath, String? location) async {
    try {
      await uploadVideo(videoPath, location);
      await Share.shareXFiles([XFile(videoPath)]);
    } catch (e) {
      _logger.e('Error sharing video: $e');
      rethrow;
    }
  }

  /// الحصول على الرابط العام للفيديو
  Future<String?> getVideoUrl(String storagePath) async {
    try {
      final response = await _client.storage.from(_videosBucket).createSignedUrl(
            storagePath,
            3600 * 24 * 365,
          );
      return response;
    } catch (e) {
      _logger.w('Error getting video URL from videos bucket, trying photos bucket: $e');
      try {
        // fallback إلى bucket الصور
        final response = await _client.storage.from('photos').createSignedUrl(
              storagePath,
              3600 * 24 * 365,
            );
        return response;
      } catch (e2) {
        _logger.e('Error getting video URL from both buckets: $e2');
        return null;
      }
    }
  }

  /// تحليل كود الموقع
  Map<String, dynamic> _parseLocationCode(String? location) {
    if (location == null || location.isEmpty) {
      return {'type': 'unknown', 'number': null};
    }

    // تحليل الكود مثل C109, B205, A301
    final match = RegExp(r'^([A-Z])(\d+)$').firstMatch(location.toUpperCase());
    if (match != null) {
      return {
        'type': match.group(1),
        'number': int.tryParse(match.group(2) ?? '0') ?? 0,
      };
    }

    return {'type': 'custom', 'number': null};
  }

  /// حذف الفيديو من المعرض المحلي بعد الرفع بنجاح
  Future<void> _deleteVideoFromLocalGallery(String videoPath) async {
    try {
      _logger.i('🎬 🗑️ Deleting video from local gallery: $videoPath');

      final file = File(videoPath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('🎬 ✅ Video deleted from local gallery successfully');
      } else {
        _logger.w('🎬 ⚠️ Video file not found for deletion: $videoPath');
      }
    } catch (e) {
      _logger.e('🎬 ❌ Error deleting video from local gallery: $e');
      // لا نرمي الخطأ هنا لأن الرفع نجح، فقط الحذف فشل
    }
  }
}
