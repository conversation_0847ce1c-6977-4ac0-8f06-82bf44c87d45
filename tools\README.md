# 🌙 أدوات إدارة نظام ذاكرة القمر

مجموعة شاملة من الأدوات المتقدمة لإدارة وصيانة ومراقبة نظام ذاكرة القمر.

## 📋 قائمة الأدوات

### 🔧 الأدوات الرئيسية

| الأداة | الوصف | الملف |
|--------|--------|-------|
| **أداة سطر الأوامر الرئيسية** | واجهة موحدة لجميع عمليات الإدارة | `moon_memory_cli.py` |
| **مدير قاعدة البيانات** | إدارة وصيانة قاعدة البيانات | `database_admin_tool.py` |
| **مدير النسخ الاحتياطية** | نسخ احتياطية متقدمة واستعادة | `backup_manager.py` |
| **مراقب النظام** | مراقبة الأداء والتنبيهات | `system_monitor.py` |
| **واجهة الويب للمراقبة** | لوحة معلومات تفاعلية | `web_monitor.py` |

### ⚙️ ملفات الإعدادات

| الملف | الوصف |
|-------|--------|
| `db_config.json` | إعدادات قاعدة البيانات والنظام |

## 🚀 البدء السريع

### 1. تثبيت المتطلبات

```bash
pip install psycopg2-binary psutil flask schedule requests
```

### 2. إعداد قاعدة البيانات

```bash
# تحرير ملف الإعدادات
nano db_config.json

# فحص الاتصال
python moon_memory_cli.py database stats
```

### 3. الاستخدام الأساسي

```bash
# عرض المساعدة
python moon_memory_cli.py --help

# فحص صحة النظام
python moon_memory_cli.py tools health-check

# إنشاء نسخة احتياطية
python moon_memory_cli.py backup create full

# مراقبة النظام
python moon_memory_cli.py monitor status
```

## 📖 دليل الاستخدام التفصيلي

### 🗄️ إدارة قاعدة البيانات

#### عرض الإحصائيات
```bash
python moon_memory_cli.py database stats
```

#### تشغيل الصيانة
```bash
# صيانة عادية
python moon_memory_cli.py database maintenance

# صيانة كاملة (تشمل إعادة بناء الفهارس)
python moon_memory_cli.py database maintenance --full
```

#### تحسين الأداء
```bash
python moon_memory_cli.py database optimize
```

#### فحص سلامة البيانات
```bash
python moon_memory_cli.py database integrity
```

#### تنظيف البيانات المحذوفة
```bash
python moon_memory_cli.py database cleanup
```

### 💾 إدارة النسخ الاحتياطية

#### إنشاء نسخة احتياطية كاملة
```bash
python moon_memory_cli.py backup create full
python moon_memory_cli.py backup create full --name "backup_before_update"
```

#### إنشاء نسخة احتياطية تزايدية
```bash
python moon_memory_cli.py backup create incremental
```

#### عرض قائمة النسخ الاحتياطية
```bash
python moon_memory_cli.py backup list
```

#### استعادة نسخة احتياطية
```bash
python moon_memory_cli.py backup restore backup_name --confirm
```

#### إعداد النسخ الاحتياطية المجدولة
```bash
python moon_memory_cli.py backup schedule
```

#### تنظيف النسخ القديمة
```bash
python moon_memory_cli.py backup cleanup
```

### 🔍 مراقبة النظام

#### عرض حالة النظام الحالية
```bash
python moon_memory_cli.py monitor status
```

#### بدء المراقبة المستمرة
```bash
# مراقبة كل دقيقة
python moon_memory_cli.py monitor start --interval 60

# مراقبة كل 30 ثانية
python moon_memory_cli.py monitor start --interval 30
```

#### تشغيل واجهة الويب للمراقبة
```bash
python moon_memory_cli.py monitor web --port 5000
```

ثم افتح المتصفح على: `http://localhost:5000`

### 📋 إنشاء التقارير

#### تقرير النشاط اليومي
```bash
python moon_memory_cli.py report daily
```

#### تقرير استخدام التخزين
```bash
python moon_memory_cli.py report storage
```

#### تقرير الأمان
```bash
python moon_memory_cli.py report security
```

#### تقرير شامل
```bash
# عرض نصي
python moon_memory_cli.py report full

# حفظ في ملف JSON
python moon_memory_cli.py report full --format json --output full_report.json
```

### 🛠️ أدوات إضافية

#### فحص صحة النظام الشامل
```bash
python moon_memory_cli.py tools health-check
```

## 🔧 الاستخدام المتقدم

### تشغيل الأدوات منفصلة

#### أداة إدارة قاعدة البيانات
```bash
python database_admin_tool.py stats
python database_admin_tool.py maintenance --full
python database_admin_tool.py backup --name manual_backup
```

#### مدير النسخ الاحتياطية
```python
from backup_manager import BackupManager

manager = BackupManager()
result = manager.create_full_backup()
print(result)
```

#### مراقب النظام
```python
from system_monitor import SystemMonitor

monitor = SystemMonitor()
monitor.start_monitoring(interval_seconds=30)
```

#### واجهة الويب للمراقبة
```bash
python web_monitor.py --host 0.0.0.0 --port 8080
```

### إعداد المراقبة التلقائية

#### إعداد Cron للنسخ الاحتياطية
```bash
# إضافة إلى crontab
0 2 * * * /usr/bin/python3 /path/to/moon_memory_cli.py backup create full
0 */6 * * * /usr/bin/python3 /path/to/moon_memory_cli.py backup create incremental
```

#### إعداد Systemd للمراقبة
```ini
# /etc/systemd/system/moon-memory-monitor.service
[Unit]
Description=Moon Memory System Monitor
After=network.target

[Service]
Type=simple
User=moonmemory
WorkingDirectory=/path/to/tools
ExecStart=/usr/bin/python3 moon_memory_cli.py monitor start --interval 60
Restart=always

[Install]
WantedBy=multi-user.target
```

## ⚙️ إعدادات متقدمة

### ملف db_config.json

```json
{
  "host": "localhost",
  "port": 5432,
  "database": "moon_memory",
  "user": "postgres",
  "password": "your_password",
  "backup_path": "./backups",
  "maintenance_schedule": {
    "auto_vacuum": true,
    "auto_analyze": true,
    "cleanup_interval_days": 7
  },
  "monitoring": {
    "performance_threshold": {
      "query_time_ms": 500,
      "connection_count": 50,
      "database_size_gb": 10
    },
    "alerts": {
      "email_enabled": false,
      "email_recipients": ["<EMAIL>"],
      "webhook_url": "https://hooks.slack.com/..."
    }
  }
}
```

### إعداد التنبيهات عبر البريد الإلكتروني

```json
{
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password"
  },
  "monitoring": {
    "alerts": {
      "email_enabled": true,
      "email_recipients": [
        "<EMAIL>",
        "<EMAIL>"
      ]
    }
  }
}
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```bash
# التحقق من الإعدادات
cat db_config.json

# اختبار الاتصال
psql -h localhost -U postgres -d moon_memory -c "SELECT 1;"
```

#### مشكلة في النسخ الاحتياطية
```bash
# التحقق من الأذونات
ls -la ./backups/

# التحقق من مساحة القرص
df -h
```

#### مشكلة في المراقبة
```bash
# التحقق من المتطلبات
pip list | grep -E "(psutil|psycopg2|flask)"

# فحص السجلات
tail -f system_monitor.log
```

## 📊 مراقبة الأداء

### مؤشرات الأداء الرئيسية

- **استخدام المعالج**: يجب أن يكون أقل من 80%
- **استخدام الذاكرة**: يجب أن يكون أقل من 85%
- **استخدام القرص**: يجب أن يكون أقل من 90%
- **اتصالات قاعدة البيانات**: يجب أن تكون أقل من 50
- **وقت الاستجابة**: يجب أن يكون أقل من 500ms

### تحسين الأداء

1. **تحسين قاعدة البيانات**: تشغيل `database optimize` أسبوعياً
2. **تنظيف البيانات**: تشغيل `database cleanup` يومياً
3. **مراقبة مستمرة**: تفعيل المراقبة التلقائية
4. **نسخ احتياطية منتظمة**: نسخة كاملة يومياً وتزايدية كل 6 ساعات

## 🔒 الأمان

### أفضل الممارسات

1. **حماية ملف الإعدادات**:
   ```bash
   chmod 600 db_config.json
   ```

2. **استخدام مستخدم منفصل**:
   ```bash
   sudo useradd -r -s /bin/false moonmemory
   sudo chown -R moonmemory:moonmemory /path/to/tools
   ```

3. **تشفير النسخ الاحتياطية**:
   ```bash
   gpg --symmetric --cipher-algo AES256 backup_file.tar.gz
   ```

4. **مراقبة محاولات الوصول**:
   ```bash
   python moon_memory_cli.py report security
   ```

## 📞 الدعم والمساعدة

- **السجلات**: تحقق من ملفات `.log` في مجلد الأدوات
- **الوثائق**: راجع التعليقات في الكود المصدري
- **فحص النظام**: استخدم `tools health-check` للتشخيص
- **المجتمع**: انضم إلى مجتمع ذاكرة القمر للدعم

---

**تم تطوير هذه الأدوات بواسطة فريق ذاكرة القمر - يناير 2025**
