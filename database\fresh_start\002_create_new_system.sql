-- إنشاء النظام الجديد من الصفر - نظام البصمة الرقمية المتقدم
-- Create New System from Scratch - Advanced Device Fingerprint System
-- Date: 2025-01-12

-- إنشاء جدول المستخدمين (إذا لم يكن موجود)
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الأجهزة الجديد بالنظام المتقدم
CREATE TABLE devices (
    -- المعرفات الأساسية
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- البصمة الرقمية المتقدمة
    device_fingerprint TEXT NOT NULL UNIQUE,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    
    -- معلومات الجهاز الأساسية
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    
    -- معلومات مجمعة للبصمة
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    
    -- نقاط الثقة والأمان
    confidence_score DECIMAL(5,2) DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    trust_level TEXT DEFAULT 'untrusted' CHECK (trust_level IN ('high', 'medium', 'low', 'untrusted', 'suspicious', 'blocked')),
    
    -- إدارة المصادقة والأمان
    auth_attempts INTEGER DEFAULT 0 CHECK (auth_attempts >= 0),
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    
    -- التوقيتات
    last_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود إضافية
    CONSTRAINT unique_user_android_id UNIQUE (user_id, android_id),
    CONSTRAINT valid_trust_level CHECK (trust_level IS NOT NULL)
);

-- إنشاء الفهارس للأداء
CREATE INDEX idx_devices_user_id ON devices(user_id);
CREATE INDEX idx_devices_device_fingerprint ON devices(device_fingerprint);
CREATE INDEX idx_devices_android_id ON devices(android_id);
CREATE INDEX idx_devices_trust_level ON devices(trust_level);
CREATE INDEX idx_devices_last_verified ON devices(last_verified_at);
CREATE INDEX idx_devices_created_at ON devices(created_at);
CREATE INDEX idx_devices_blocked ON devices(is_blocked) WHERE is_blocked = TRUE;

-- إضافة تعليقات توضيحية
COMMENT ON TABLE devices IS 'جدول الأجهزة مع نظام البصمة الرقمية المتقدم';
COMMENT ON COLUMN devices.device_fingerprint IS 'البصمة الرقمية المتقدمة للجهاز (SHA-256)';
COMMENT ON COLUMN devices.android_id IS 'معرف Android الفريد للجهاز';
COMMENT ON COLUMN devices.build_fingerprint IS 'بصمة البناء الخاصة بنظام التشغيل';
COMMENT ON COLUMN devices.confidence_score IS 'نقاط الثقة في البصمة (0-100)';
COMMENT ON COLUMN devices.trust_level IS 'مستوى الثقة في الجهاز';
COMMENT ON COLUMN devices.hardware_info IS 'معلومات الهاردوير المجمعة';
COMMENT ON COLUMN devices.system_info IS 'معلومات النظام المجمعة';
COMMENT ON COLUMN devices.raw_fingerprint_data IS 'البيانات الخام للبصمة الرقمية';
COMMENT ON COLUMN devices.auth_attempts IS 'عدد محاولات المصادقة الفاشلة';
COMMENT ON COLUMN devices.is_blocked IS 'هل الجهاز محجوب';
COMMENT ON COLUMN devices.blocked_until IS 'وقت انتهاء الحجب';

-- تفعيل Row Level Security
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين - يمكنهم رؤية أجهزتهم فقط
CREATE POLICY devices_user_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id);

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الدالة على جدول الأجهزة
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- رسالة نجاح
SELECT 'تم إنشاء النظام الجديد بنجاح! 🚀' as creation_status;
SELECT 'الجداول والفهارس والسياسات جاهزة' as details;
