// ===== 📸 خدمة الوسائط الموحدة =====
// Unified Media Service for Camera App
// تاريخ الإنشاء: 2025-07-20
// الهدف: رفع الصور والفيديوهات مع الجدول الموحد

import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'unified_auth_service.dart';

class UnifiedMediaService {
  static final UnifiedMediaService _instance = UnifiedMediaService._internal();
  factory UnifiedMediaService() => _instance;
  UnifiedMediaService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final Logger _logger = Logger();

  /// رفع صورة مع الجدول الموحد
  Future<Map<String, dynamic>> uploadPhoto({
    required File imageFile,
    required String location,
    String? username,
    double? locationLat,
    double? locationLng,
    String? locationName,
  }) async {
    try {
      // الحصول على بيانات المستخدم من الجدول الموحد
      final currentUser = await _getCurrentUserFromUnified();
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final userId = currentUser['id'] as String;
      final userFullName = currentUser['full_name'] as String? ?? 'مجهول';

      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = 'photo_${userId}_${timestamp}$extension';
      final storagePath = 'uploads/photos/$fileName';

      _logger.i('📸 بدء رفع الصورة: $fileName');

      // رفع الملف إلى التخزين
      await _supabase.storage
          .from('photos')
          .upload(storagePath, imageFile);

      _logger.i('📸 تم رفع الملف إلى التخزين: $storagePath');

      // الحصول على رابط الصورة
      final imageUrl = _supabase.storage
          .from('photos')
          .getPublicUrl(storagePath);

      _logger.i('📸 رابط الصورة: $imageUrl');

      // تحليل كود الموقع
      final locationData = _parseLocationCode(location);

      // حفظ في قاعدة البيانات
      final dbResponse = await _supabase.from('photos').insert({
        'user_id': userId,
        'file_name': fileName,
        'image_url': imageUrl,
        'storage_path': storagePath,
        'location': location,
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'full_location_code': location,
        'username': username ?? userFullName,
        'file_size_bytes': await imageFile.length(),
        'capture_timestamp': DateTime.now().toIso8601String(),
        'upload_timestamp': DateTime.now().toIso8601String(),
        'upload_status': 'uploaded',
        'status': 'active',
        'location_lat': locationLat,
        'location_lng': locationLng,
        'location_name': locationName,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select().single();

      _logger.i('📸 تم حفظ الصورة في قاعدة البيانات: ${dbResponse['id']}');

      // تحديث إحصائيات المستخدم
      await _updateUserStats(userId, 'photo');

      return {
        'success': true,
        'photo_id': dbResponse['id'],
        'image_url': imageUrl,
        'storage_path': storagePath,
        'file_name': fileName,
        'message': 'تم رفع الصورة بنجاح',
      };

    } catch (e) {
      _logger.e('❌ خطأ في رفع الصورة: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في رفع الصورة',
      };
    }
  }

  /// رفع فيديو مع الجدول الموحد
  Future<Map<String, dynamic>> uploadVideo({
    required File videoFile,
    required String location,
    String? username,
    int? durationSeconds,
    double? locationLat,
    double? locationLng,
    String? locationName,
  }) async {
    try {
      // الحصول على بيانات المستخدم من الجدول الموحد
      final currentUser = await _getCurrentUserFromUnified();
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final userId = currentUser['id'] as String;
      final userFullName = currentUser['full_name'] as String? ?? 'مجهول';

      // إنشاء اسم ملف فريد
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(videoFile.path);
      final fileName = 'video_${userId}_${timestamp}$extension';
      final storagePath = 'uploads/videos/$fileName';

      _logger.i('🎬 بدء رفع الفيديو: $fileName');

      // رفع الملف إلى التخزين (محاولة videos bucket أولاً، ثم photos كـ fallback)
      String? videoUrl;
      try {
        await _supabase.storage
            .from('videos')
            .upload(storagePath, videoFile);
        videoUrl = _supabase.storage
            .from('videos')
            .getPublicUrl(storagePath);
        _logger.i('🎬 تم رفع الفيديو إلى videos bucket');
      } catch (e) {
        _logger.w('🎬 فشل رفع إلى videos bucket، محاولة photos bucket: $e');
        await _supabase.storage
            .from('photos')
            .upload(storagePath, videoFile);
        videoUrl = _supabase.storage
            .from('photos')
            .getPublicUrl(storagePath);
        _logger.i('🎬 تم رفع الفيديو إلى photos bucket');
      }

      _logger.i('🎬 رابط الفيديو: $videoUrl');

      // تحليل كود الموقع
      final locationData = _parseLocationCode(location);

      // حفظ في قاعدة البيانات
      final dbResponse = await _supabase.from('videos').insert({
        'user_id': userId,
        'file_name': fileName,
        'video_url': videoUrl,
        'storage_path': storagePath,
        'location': location,
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'full_location_code': location,
        'username': username ?? userFullName,
        'file_size_bytes': await videoFile.length(),
        'duration_seconds': durationSeconds,
        'capture_timestamp': DateTime.now().toIso8601String(),
        'upload_timestamp': DateTime.now().toIso8601String(),
        'upload_status': 'uploaded',
        'status': 'active',
        'location_lat': locationLat,
        'location_lng': locationLng,
        'location_name': locationName,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select().single();

      _logger.i('🎬 تم حفظ الفيديو في قاعدة البيانات: ${dbResponse['id']}');

      // تحديث إحصائيات المستخدم
      await _updateUserStats(userId, 'video');

      return {
        'success': true,
        'video_id': dbResponse['id'],
        'video_url': videoUrl,
        'storage_path': storagePath,
        'file_name': fileName,
        'message': 'تم رفع الفيديو بنجاح',
      };

    } catch (e) {
      _logger.e('❌ خطأ في رفع الفيديو: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في رفع الفيديو',
      };
    }
  }

  /// الحصول على بيانات المستخدم الحالي من الجدول الموحد
  Future<Map<String, dynamic>?> _getCurrentUserFromUnified() async {
    try {
      // يمكن الحصول على user_id من خدمة المصادقة الموحدة
      final authService = UnifiedAuthService();
      final userId = authService.currentUserId;

      if (userId == null) return null;

      final response = await _supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .eq('is_active', true)
          .single();

      return response;
    } catch (e) {
      _logger.e('❌ خطأ في الحصول على بيانات المستخدم: $e');
      return null;
    }
  }

  /// تحديث إحصائيات المستخدم
  Future<void> _updateUserStats(String userId, String mediaType) async {
    try {
      // يمكن إضافة إحصائيات للمستخدم في الجدول الموحد
      await _supabase
          .from('users_unified')
          .update({
            'last_seen': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      _logger.d('📊 تم تحديث إحصائيات المستخدم: $mediaType');
    } catch (e) {
      _logger.e('❌ خطأ في تحديث الإحصائيات: $e');
    }
  }

  /// تحليل كود الموقع
  Map<String, String?> _parseLocationCode(String location) {
    if (location.isEmpty) {
      return {'type': null, 'number': null};
    }

    final regex = RegExp(r'^([UC])(\d+)$');
    final match = regex.firstMatch(location.toUpperCase());

    if (match != null) {
      return {
        'type': match.group(1),
        'number': match.group(2),
      };
    }

    return {'type': null, 'number': null};
  }

  /// الحصول على ملفات المستخدم
  Future<Map<String, dynamic>> getUserMedia({
    int limit = 20,
    int offset = 0,
    String? locationType,
  }) async {
    try {
      final currentUser = await _getCurrentUserFromUnified();
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final userId = currentUser['id'] as String;

      // الحصول على الصور
      var photosQuery = _supabase
          .from('photos')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'active')
          .order('upload_timestamp', ascending: false)
          .range(offset, offset + limit - 1);

      if (locationType != null) {
        photosQuery = photosQuery.eq('location_type', locationType);
      }

      final photos = await photosQuery;

      // الحصول على الفيديوهات
      var videosQuery = _supabase
          .from('videos')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'active')
          .order('upload_timestamp', ascending: false)
          .range(offset, offset + limit - 1);

      if (locationType != null) {
        videosQuery = videosQuery.eq('location_type', locationType);
      }

      final videos = await videosQuery;

      return {
        'success': true,
        'photos': photos,
        'videos': videos,
        'total_photos': photos.length,
        'total_videos': videos.length,
      };

    } catch (e) {
      _logger.e('❌ خطأ في الحصول على ملفات المستخدم: $e');
      return {
        'success': false,
        'error': e.toString(),
        'photos': [],
        'videos': [],
      };
    }
  }
}


