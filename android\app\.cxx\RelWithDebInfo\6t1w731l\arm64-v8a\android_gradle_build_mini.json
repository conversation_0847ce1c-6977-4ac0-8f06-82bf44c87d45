{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\CascadeProjects\\moon_memory_v2\\mobile_app\\android\\app\\.cxx\\RelWithDebInfo\\6t1w731l\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\CascadeProjects\\moon_memory_v2\\mobile_app\\android\\app\\.cxx\\RelWithDebInfo\\6t1w731l\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}