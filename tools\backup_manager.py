#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطية المتقدم لذاكرة القمر
Moon Memory Advanced Backup Manager

أداة شاملة لإدارة النسخ الاحتياطية مع:
- نسخ احتياطية تلقائية مجدولة
- ضغط وتشفير
- نسخ احتياطية تزايدية
- استعادة انتقائية
- مراقبة سلامة النسخ

المطور: فريق ذاكرة القمر
التاريخ: يناير 2025
"""

import os
import sys
import json
import gzip
import shutil
import hashlib
import logging
import subprocess
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import threading
import tarfile
import psycopg2
import psycopg2.extras

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backup_manager.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BackupManager:
    """مدير النسخ الاحتياطية المتقدم"""
    
    def __init__(self, config_file: str = 'db_config.json'):
        """تهيئة مدير النسخ الاحتياطية"""
        self.config = self._load_config(config_file)
        self.backup_path = Path(self.config.get('backup_path', './backups'))
        self.backup_path.mkdir(parents=True, exist_ok=True)
        
        # مجلدات النسخ الاحتياطية
        self.full_backup_path = self.backup_path / 'full'
        self.incremental_backup_path = self.backup_path / 'incremental'
        self.media_backup_path = self.backup_path / 'media'
        
        for path in [self.full_backup_path, self.incremental_backup_path, self.media_backup_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        self.scheduler_running = False
        
    def _load_config(self, config_file: str) -> Dict:
        """تحميل إعدادات النسخ الاحتياطية"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"ملف الإعدادات {config_file} غير موجود")
            return {}
    
    def _get_db_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        return psycopg2.connect(
            host=self.config['host'],
            port=self.config['port'],
            database=self.config['database'],
            user=self.config['user'],
            password=self.config['password']
        )
    
    def create_full_backup(self, backup_name: str = None, compress: bool = True) -> Dict:
        """إنشاء نسخة احتياطية كاملة"""
        if not backup_name:
            backup_name = f"full_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"بدء إنشاء نسخة احتياطية كاملة: {backup_name}")
        
        backup_info = {
            'name': backup_name,
            'type': 'full',
            'start_time': datetime.now().isoformat(),
            'status': 'in_progress',
            'files': []
        }
        
        try:
            # نسخة احتياطية لقاعدة البيانات
            db_backup_result = self._backup_database(backup_name, compress)
            backup_info['files'].append(db_backup_result)
            
            # نسخة احتياطية للملفات المرفوعة
            media_backup_result = self._backup_media_files(backup_name, compress)
            backup_info['files'].append(media_backup_result)
            
            # نسخة احتياطية للإعدادات
            config_backup_result = self._backup_configurations(backup_name)
            backup_info['files'].append(config_backup_result)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info['end_time'] = datetime.now().isoformat()
            backup_info['status'] = 'completed'
            backup_info['total_size'] = sum(f.get('size', 0) for f in backup_info['files'])
            
            info_file = self.full_backup_path / f"{backup_name}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تمت النسخة الاحتياطية الكاملة بنجاح: {backup_name}")
            return backup_info
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
            backup_info['end_time'] = datetime.now().isoformat()
            return backup_info
    
    def _backup_database(self, backup_name: str, compress: bool = True) -> Dict:
        """نسخة احتياطية لقاعدة البيانات"""
        logger.info("إنشاء نسخة احتياطية لقاعدة البيانات...")
        
        db_file = self.full_backup_path / f"{backup_name}_database.sql"
        
        # استخدام pg_dump
        cmd = [
            'pg_dump',
            '-h', self.config['host'],
            '-p', str(self.config['port']),
            '-U', self.config['user'],
            '-d', self.config['database'],
            '-f', str(db_file),
            '--verbose',
            '--no-password'
        ]
        
        env = os.environ.copy()
        env['PGPASSWORD'] = self.config['password']
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"فشل في نسخ قاعدة البيانات: {result.stderr}")
        
        file_info = {
            'type': 'database',
            'path': str(db_file),
            'size': db_file.stat().st_size
        }
        
        # ضغط الملف إذا كان مطلوباً
        if compress:
            compressed_file = db_file.with_suffix('.sql.gz')
            with open(db_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            db_file.unlink()  # حذف الملف غير المضغوط
            file_info['path'] = str(compressed_file)
            file_info['size'] = compressed_file.stat().st_size
            file_info['compressed'] = True
        
        # حساب checksum
        file_info['checksum'] = self._calculate_checksum(file_info['path'])
        
        logger.info(f"تمت نسخة قاعدة البيانات: {file_info['size']} بايت")
        return file_info
    
    def _backup_media_files(self, backup_name: str, compress: bool = True) -> Dict:
        """نسخة احتياطية للملفات المرفوعة"""
        logger.info("إنشاء نسخة احتياطية للملفات المرفوعة...")
        
        # الحصول على مسارات الملفات من قاعدة البيانات
        media_paths = self._get_media_file_paths()
        
        if not media_paths:
            logger.warning("لا توجد ملفات وسائط للنسخ الاحتياطي")
            return {
                'type': 'media',
                'path': None,
                'size': 0,
                'file_count': 0
            }
        
        # إنشاء أرشيف tar
        archive_file = self.media_backup_path / f"{backup_name}_media.tar"
        if compress:
            archive_file = archive_file.with_suffix('.tar.gz')
        
        mode = 'w:gz' if compress else 'w'
        
        with tarfile.open(archive_file, mode) as tar:
            file_count = 0
            for media_path in media_paths:
                if os.path.exists(media_path):
                    # إضافة الملف للأرشيف مع الحفاظ على البنية
                    arcname = os.path.relpath(media_path, '/')
                    tar.add(media_path, arcname=arcname)
                    file_count += 1
                else:
                    logger.warning(f"الملف غير موجود: {media_path}")
        
        file_info = {
            'type': 'media',
            'path': str(archive_file),
            'size': archive_file.stat().st_size,
            'file_count': file_count,
            'compressed': compress
        }
        
        # حساب checksum
        file_info['checksum'] = self._calculate_checksum(file_info['path'])
        
        logger.info(f"تمت نسخة الملفات المرفوعة: {file_count} ملف، {file_info['size']} بايت")
        return file_info
    
    def _backup_configurations(self, backup_name: str) -> Dict:
        """نسخة احتياطية للإعدادات"""
        logger.info("إنشاء نسخة احتياطية للإعدادات...")
        
        config_file = self.full_backup_path / f"{backup_name}_config.tar.gz"
        
        # ملفات الإعدادات المهمة
        config_files = [
            'db_config.json',
            'app_config.json',
            '.env',
            'nginx.conf',
            'docker-compose.yml'
        ]
        
        with tarfile.open(config_file, 'w:gz') as tar:
            file_count = 0
            for config_path in config_files:
                if os.path.exists(config_path):
                    tar.add(config_path)
                    file_count += 1
        
        file_info = {
            'type': 'configuration',
            'path': str(config_file),
            'size': config_file.stat().st_size if config_file.exists() else 0,
            'file_count': file_count,
            'compressed': True
        }
        
        if file_info['size'] > 0:
            file_info['checksum'] = self._calculate_checksum(file_info['path'])
        
        logger.info(f"تمت نسخة الإعدادات: {file_count} ملف")
        return file_info
    
    def _get_media_file_paths(self) -> List[str]:
        """الحصول على مسارات ملفات الوسائط من قاعدة البيانات"""
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()
            
            # الحصول على مسارات الصور
            cursor.execute("""
                SELECT DISTINCT storage_path 
                FROM public.photos 
                WHERE status = 'active' AND storage_path IS NOT NULL
            """)
            photo_paths = [row[0] for row in cursor.fetchall()]
            
            # الحصول على مسارات الفيديوهات
            cursor.execute("""
                SELECT DISTINCT storage_path 
                FROM public.videos 
                WHERE status = 'active' AND storage_path IS NOT NULL
            """)
            video_paths = [row[0] for row in cursor.fetchall()]
            
            cursor.close()
            conn.close()
            
            return photo_paths + video_paths
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على مسارات الملفات: {e}")
            return []
    
    def _calculate_checksum(self, file_path: str) -> str:
        """حساب checksum للملف"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def create_incremental_backup(self, since_date: datetime = None) -> Dict:
        """إنشاء نسخة احتياطية تزايدية"""
        if not since_date:
            # آخر نسخة احتياطية كاملة
            since_date = self._get_last_full_backup_date()
        
        backup_name = f"incremental_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"بدء إنشاء نسخة احتياطية تزايدية منذ: {since_date}")
        
        backup_info = {
            'name': backup_name,
            'type': 'incremental',
            'since_date': since_date.isoformat() if since_date else None,
            'start_time': datetime.now().isoformat(),
            'status': 'in_progress',
            'changes': []
        }
        
        try:
            # البحث عن التغييرات في قاعدة البيانات
            db_changes = self._get_database_changes(since_date)
            backup_info['changes'].extend(db_changes)
            
            # البحث عن الملفات الجديدة أو المحدثة
            media_changes = self._get_media_changes(since_date)
            backup_info['changes'].extend(media_changes)
            
            # إنشاء أرشيف للتغييرات
            if backup_info['changes']:
                archive_path = self._create_incremental_archive(backup_name, backup_info['changes'])
                backup_info['archive_path'] = str(archive_path)
                backup_info['archive_size'] = archive_path.stat().st_size
            
            backup_info['end_time'] = datetime.now().isoformat()
            backup_info['status'] = 'completed'
            backup_info['change_count'] = len(backup_info['changes'])
            
            # حفظ معلومات النسخة التزايدية
            info_file = self.incremental_backup_path / f"{backup_name}_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تمت النسخة الاحتياطية التزايدية: {backup_info['change_count']} تغيير")
            return backup_info
            
        except Exception as e:
            logger.error(f"خطأ في النسخة الاحتياطية التزايدية: {e}")
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
            backup_info['end_time'] = datetime.now().isoformat()
            return backup_info
    
    def _get_last_full_backup_date(self) -> Optional[datetime]:
        """الحصول على تاريخ آخر نسخة احتياطية كاملة"""
        try:
            info_files = list(self.full_backup_path.glob("*_info.json"))
            if not info_files:
                return None
            
            latest_file = max(info_files, key=lambda f: f.stat().st_mtime)
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                info = json.load(f)
                return datetime.fromisoformat(info['start_time'])
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على تاريخ آخر نسخة احتياطية: {e}")
            return None
    
    def _get_database_changes(self, since_date: datetime) -> List[Dict]:
        """الحصول على التغييرات في قاعدة البيانات"""
        changes = []
        
        try:
            conn = self._get_db_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # التغييرات في الصور
            cursor.execute("""
                SELECT 'photo' as type, id, file_name, username, created_at, updated_at
                FROM public.photos 
                WHERE updated_at > %s OR created_at > %s
            """, (since_date, since_date))
            
            for row in cursor.fetchall():
                changes.append({
                    'type': 'database_change',
                    'table': 'photos',
                    'record_type': row['type'],
                    'record_id': str(row['id']),
                    'file_name': row['file_name'],
                    'username': row['username'],
                    'timestamp': row['updated_at'].isoformat()
                })
            
            # التغييرات في الفيديوهات
            cursor.execute("""
                SELECT 'video' as type, id, file_name, username, created_at, updated_at
                FROM public.videos 
                WHERE updated_at > %s OR created_at > %s
            """, (since_date, since_date))
            
            for row in cursor.fetchall():
                changes.append({
                    'type': 'database_change',
                    'table': 'videos',
                    'record_type': row['type'],
                    'record_id': str(row['id']),
                    'file_name': row['file_name'],
                    'username': row['username'],
                    'timestamp': row['updated_at'].isoformat()
                })
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على تغييرات قاعدة البيانات: {e}")
        
        return changes
    
    def _get_media_changes(self, since_date: datetime) -> List[Dict]:
        """الحصول على التغييرات في ملفات الوسائط"""
        changes = []
        
        try:
            # البحث في مجلدات الوسائط عن الملفات المحدثة
            media_dirs = ['/uploads', '/processed', '/thumbnails']
            
            for media_dir in media_dirs:
                if os.path.exists(media_dir):
                    for root, dirs, files in os.walk(media_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            file_stat = os.stat(file_path)
                            file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
                            
                            if file_mtime > since_date:
                                changes.append({
                                    'type': 'file_change',
                                    'path': file_path,
                                    'size': file_stat.st_size,
                                    'modified_time': file_mtime.isoformat()
                                })
        
        except Exception as e:
            logger.error(f"خطأ في الحصول على تغييرات الملفات: {e}")
        
        return changes
    
    def _create_incremental_archive(self, backup_name: str, changes: List[Dict]) -> Path:
        """إنشاء أرشيف للتغييرات التزايدية"""
        archive_path = self.incremental_backup_path / f"{backup_name}.tar.gz"
        
        with tarfile.open(archive_path, 'w:gz') as tar:
            for change in changes:
                if change['type'] == 'file_change' and os.path.exists(change['path']):
                    arcname = os.path.relpath(change['path'], '/')
                    tar.add(change['path'], arcname=arcname)
        
        return archive_path

    def list_backups(self) -> Dict:
        """عرض قائمة النسخ الاحتياطية المتاحة"""
        backups = {
            'full_backups': [],
            'incremental_backups': []
        }

        # النسخ الكاملة
        for info_file in self.full_backup_path.glob("*_info.json"):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                    backups['full_backups'].append(backup_info)
            except Exception as e:
                logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية {info_file}: {e}")

        # النسخ التزايدية
        for info_file in self.incremental_backup_path.glob("*_info.json"):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                    backups['incremental_backups'].append(backup_info)
            except Exception as e:
                logger.error(f"خطأ في قراءة معلومات النسخة التزايدية {info_file}: {e}")

        # ترتيب حسب التاريخ
        backups['full_backups'].sort(key=lambda x: x.get('start_time', ''), reverse=True)
        backups['incremental_backups'].sort(key=lambda x: x.get('start_time', ''), reverse=True)

        return backups

    def restore_backup(self, backup_name: str, restore_options: Dict = None) -> Dict:
        """استعادة نسخة احتياطية"""
        if not restore_options:
            restore_options = {
                'restore_database': True,
                'restore_media': True,
                'restore_config': False,
                'confirm': False
            }

        if not restore_options.get('confirm', False):
            return {
                'status': 'error',
                'message': 'يجب تأكيد عملية الاستعادة بتمرير confirm=True'
            }

        logger.info(f"بدء استعادة النسخة الاحتياطية: {backup_name}")

        restore_info = {
            'backup_name': backup_name,
            'start_time': datetime.now().isoformat(),
            'status': 'in_progress',
            'operations': []
        }

        try:
            # البحث عن معلومات النسخة الاحتياطية
            backup_info = self._find_backup_info(backup_name)
            if not backup_info:
                raise Exception(f"لم يتم العثور على النسخة الاحتياطية: {backup_name}")

            # استعادة قاعدة البيانات
            if restore_options.get('restore_database', True):
                db_result = self._restore_database(backup_info)
                restore_info['operations'].append(db_result)

            # استعادة ملفات الوسائط
            if restore_options.get('restore_media', True):
                media_result = self._restore_media_files(backup_info)
                restore_info['operations'].append(media_result)

            # استعادة الإعدادات
            if restore_options.get('restore_config', False):
                config_result = self._restore_configurations(backup_info)
                restore_info['operations'].append(config_result)

            restore_info['end_time'] = datetime.now().isoformat()
            restore_info['status'] = 'completed'

            logger.info(f"تمت استعادة النسخة الاحتياطية بنجاح: {backup_name}")
            return restore_info

        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            restore_info['status'] = 'failed'
            restore_info['error'] = str(e)
            restore_info['end_time'] = datetime.now().isoformat()
            return restore_info

    def _find_backup_info(self, backup_name: str) -> Optional[Dict]:
        """البحث عن معلومات النسخة الاحتياطية"""
        # البحث في النسخ الكاملة
        info_file = self.full_backup_path / f"{backup_name}_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                return json.load(f)

        # البحث في النسخ التزايدية
        info_file = self.incremental_backup_path / f"{backup_name}_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                return json.load(f)

        return None

    def _restore_database(self, backup_info: Dict) -> Dict:
        """استعادة قاعدة البيانات"""
        logger.info("استعادة قاعدة البيانات...")

        # البحث عن ملف قاعدة البيانات
        db_file_info = None
        for file_info in backup_info.get('files', []):
            if file_info.get('type') == 'database':
                db_file_info = file_info
                break

        if not db_file_info:
            raise Exception("لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية")

        db_file_path = db_file_info['path']

        # إلغاء ضغط الملف إذا كان مضغوطاً
        if db_file_info.get('compressed', False):
            uncompressed_path = db_file_path.replace('.gz', '')
            with gzip.open(db_file_path, 'rb') as f_in:
                with open(uncompressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            db_file_path = uncompressed_path

        # التحقق من checksum
        current_checksum = self._calculate_checksum(db_file_path)
        expected_checksum = db_file_info.get('checksum')

        if expected_checksum and current_checksum != expected_checksum:
            raise Exception("فشل في التحقق من سلامة ملف قاعدة البيانات")

        # استعادة قاعدة البيانات باستخدام psql
        cmd = [
            'psql',
            '-h', self.config['host'],
            '-p', str(self.config['port']),
            '-U', self.config['user'],
            '-d', self.config['database'],
            '-f', db_file_path,
            '--quiet'
        ]

        env = os.environ.copy()
        env['PGPASSWORD'] = self.config['password']

        result = subprocess.run(cmd, env=env, capture_output=True, text=True)

        # تنظيف الملف المؤقت
        if db_file_info.get('compressed', False):
            os.unlink(db_file_path)

        if result.returncode != 0:
            raise Exception(f"فشل في استعادة قاعدة البيانات: {result.stderr}")

        return {
            'operation': 'restore_database',
            'status': 'success',
            'file_path': db_file_info['path'],
            'checksum_verified': expected_checksum is not None
        }

    def _restore_media_files(self, backup_info: Dict) -> Dict:
        """استعادة ملفات الوسائط"""
        logger.info("استعادة ملفات الوسائط...")

        # البحث عن أرشيف الوسائط
        media_file_info = None
        for file_info in backup_info.get('files', []):
            if file_info.get('type') == 'media':
                media_file_info = file_info
                break

        if not media_file_info or not media_file_info.get('path'):
            logger.warning("لا توجد ملفات وسائط في النسخة الاحتياطية")
            return {
                'operation': 'restore_media',
                'status': 'skipped',
                'reason': 'no_media_files'
            }

        archive_path = media_file_info['path']

        # التحقق من checksum
        current_checksum = self._calculate_checksum(archive_path)
        expected_checksum = media_file_info.get('checksum')

        if expected_checksum and current_checksum != expected_checksum:
            raise Exception("فشل في التحقق من سلامة أرشيف الوسائط")

        # استخراج الأرشيف
        with tarfile.open(archive_path, 'r:*') as tar:
            tar.extractall(path='/')

        return {
            'operation': 'restore_media',
            'status': 'success',
            'archive_path': archive_path,
            'file_count': media_file_info.get('file_count', 0),
            'checksum_verified': expected_checksum is not None
        }

    def _restore_configurations(self, backup_info: Dict) -> Dict:
        """استعادة الإعدادات"""
        logger.info("استعادة الإعدادات...")

        # البحث عن ملف الإعدادات
        config_file_info = None
        for file_info in backup_info.get('files', []):
            if file_info.get('type') == 'configuration':
                config_file_info = file_info
                break

        if not config_file_info or not config_file_info.get('path'):
            logger.warning("لا توجد ملفات إعدادات في النسخة الاحتياطية")
            return {
                'operation': 'restore_config',
                'status': 'skipped',
                'reason': 'no_config_files'
            }

        archive_path = config_file_info['path']

        # التحقق من checksum
        current_checksum = self._calculate_checksum(archive_path)
        expected_checksum = config_file_info.get('checksum')

        if expected_checksum and current_checksum != expected_checksum:
            raise Exception("فشل في التحقق من سلامة أرشيف الإعدادات")

        # استخراج الأرشيف
        with tarfile.open(archive_path, 'r:*') as tar:
            tar.extractall(path='.')

        return {
            'operation': 'restore_config',
            'status': 'success',
            'archive_path': archive_path,
            'file_count': config_file_info.get('file_count', 0),
            'checksum_verified': expected_checksum is not None
        }

    def setup_scheduled_backups(self):
        """إعداد النسخ الاحتياطية المجدولة"""
        logger.info("إعداد النسخ الاحتياطية المجدولة...")

        # نسخة احتياطية كاملة يومياً في 2:00 صباحاً
        schedule.every().day.at("02:00").do(self._scheduled_full_backup)

        # نسخة احتياطية تزايدية كل 6 ساعات
        schedule.every(6).hours.do(self._scheduled_incremental_backup)

        # تنظيف النسخ القديمة أسبوعياً
        schedule.every().sunday.at("01:00").do(self._cleanup_old_backups)

        self.scheduler_running = True

        # تشغيل المجدول في thread منفصل
        scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        scheduler_thread.start()

        logger.info("تم إعداد النسخ الاحتياطية المجدولة")

    def _run_scheduler(self):
        """تشغيل المجدول"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة

    def _scheduled_full_backup(self):
        """نسخة احتياطية كاملة مجدولة"""
        try:
            logger.info("بدء النسخة الاحتياطية الكاملة المجدولة")
            result = self.create_full_backup()
            if result['status'] == 'completed':
                logger.info("تمت النسخة الاحتياطية الكاملة المجدولة بنجاح")
            else:
                logger.error(f"فشلت النسخة الاحتياطية الكاملة المجدولة: {result.get('error')}")
        except Exception as e:
            logger.error(f"خطأ في النسخة الاحتياطية الكاملة المجدولة: {e}")

    def _scheduled_incremental_backup(self):
        """نسخة احتياطية تزايدية مجدولة"""
        try:
            logger.info("بدء النسخة الاحتياطية التزايدية المجدولة")
            result = self.create_incremental_backup()
            if result['status'] == 'completed':
                logger.info(f"تمت النسخة الاحتياطية التزايدية المجدولة: {result['change_count']} تغيير")
            else:
                logger.error(f"فشلت النسخة الاحتياطية التزايدية المجدولة: {result.get('error')}")
        except Exception as e:
            logger.error(f"خطأ في النسخة الاحتياطية التزايدية المجدولة: {e}")

    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            logger.info("بدء تنظيف النسخ الاحتياطية القديمة")

            # الاحتفاظ بآخر 7 نسخ كاملة
            self._cleanup_backups_by_count(self.full_backup_path, 7)

            # الاحتفاظ بالنسخ التزايدية لآخر 30 يوم
            cutoff_date = datetime.now() - timedelta(days=30)
            self._cleanup_backups_by_date(self.incremental_backup_path, cutoff_date)

            logger.info("تم تنظيف النسخ الاحتياطية القديمة")

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية: {e}")

    def _cleanup_backups_by_count(self, backup_dir: Path, keep_count: int):
        """تنظيف النسخ الاحتياطية بالعدد"""
        info_files = sorted(
            backup_dir.glob("*_info.json"),
            key=lambda f: f.stat().st_mtime,
            reverse=True
        )

        for info_file in info_files[keep_count:]:
            try:
                # قراءة معلومات النسخة الاحتياطية
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)

                # حذف ملفات النسخة الاحتياطية
                for file_info in backup_info.get('files', []):
                    file_path = Path(file_info['path'])
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"تم حذف الملف: {file_path}")

                # حذف ملف المعلومات
                info_file.unlink()
                logger.info(f"تم حذف النسخة الاحتياطية: {backup_info['name']}")

            except Exception as e:
                logger.error(f"خطأ في حذف النسخة الاحتياطية {info_file}: {e}")

    def _cleanup_backups_by_date(self, backup_dir: Path, cutoff_date: datetime):
        """تنظيف النسخ الاحتياطية بالتاريخ"""
        for info_file in backup_dir.glob("*_info.json"):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)

                backup_date = datetime.fromisoformat(backup_info['start_time'])

                if backup_date < cutoff_date:
                    # حذف ملفات النسخة الاحتياطية
                    archive_path = backup_info.get('archive_path')
                    if archive_path and os.path.exists(archive_path):
                        os.unlink(archive_path)
                        logger.info(f"تم حذف الأرشيف: {archive_path}")

                    # حذف ملف المعلومات
                    info_file.unlink()
                    logger.info(f"تم حذف النسخة التزايدية: {backup_info['name']}")

            except Exception as e:
                logger.error(f"خطأ في حذف النسخة التزايدية {info_file}: {e}")

    def stop_scheduler(self):
        """إيقاف المجدول"""
        self.scheduler_running = False
        schedule.clear()
        logger.info("تم إيقاف مجدول النسخ الاحتياطية")
