-- ===== 🛡️ نسخة آمنة ومحسنة للمستخدمين المتصلين =====
-- تعمل مع أي هيكل لجدول users بدون أخطاء

-- ===== 📊 فحص الحقول المتاحة =====
SELECT 'الحقول المتاحة في جدول users:' as info;
SELECT column_name, data_type
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- ===== 🔧 إضافة الحقول المفقودة بأمان =====
DO $$ 
BEGIN
    -- إضافة username
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') THEN
        ALTER TABLE users ADD COLUMN username TEXT;
        RAISE NOTICE '✅ تم إضافة حقل username';
    ELSE
        RAISE NOTICE 'ℹ️ حقل username موجود بالفعل';
    END IF;
    
    -- إضافة full_name
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE users ADD COLUMN full_name TEXT;
        RAISE NOTICE '✅ تم إضافة حقل full_name';
    ELSE
        RAISE NOTICE 'ℹ️ حقل full_name موجود بالفعل';
    END IF;
    
    -- إضافة location_code
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') THEN
        ALTER TABLE users ADD COLUMN location_code TEXT;
        RAISE NOTICE '✅ تم إضافة حقل location_code';
    ELSE
        RAISE NOTICE 'ℹ️ حقل location_code موجود بالفعل';
    END IF;
    
    -- إضافة is_admin
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ تم إضافة حقل is_admin';
    ELSE
        RAISE NOTICE 'ℹ️ حقل is_admin موجود بالفعل';
    END IF;
END $$;

-- ===== 👥 دالة آمنة للمستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
    user_id UUID,
    user_identifier TEXT,
    user_display_name TEXT,
    location_info TEXT,
    session_id UUID,
    device_info TEXT,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    ip_address INET,
    is_admin BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        COALESCE(u.username, u.id::TEXT) as user_identifier,
        COALESCE(u.full_name, 'مستخدم') as user_display_name,
        COALESCE(u.location_code, 'غير محدد') as location_info,
        s.id as session_id,
        s.user_agent as device_info,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.ip_address,
        COALESCE(u.is_admin, FALSE) as is_admin
    FROM user_sessions s 
    JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE 
      AND s.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 📊 دالة إحصائيات آمنة =====
CREATE OR REPLACE FUNCTION get_online_users_stats()
RETURNS TABLE (
    total_online INTEGER,
    admin_online INTEGER,
    regular_users_online INTEGER,
    active_sessions INTEGER,
    avg_session_duration INTERVAL,
    longest_session INTERVAL,
    newest_session INTERVAL
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT s.user_id)::INTEGER as total_online,
        COUNT(DISTINCT CASE WHEN COALESCE(u.is_admin, FALSE) = TRUE THEN s.user_id END)::INTEGER as admin_online,
        COUNT(DISTINCT CASE WHEN COALESCE(u.is_admin, FALSE) = FALSE THEN s.user_id END)::INTEGER as regular_users_online,
        COUNT(s.id)::INTEGER as active_sessions,
        AVG(NOW() - s.started_at) as avg_session_duration,
        MAX(NOW() - s.started_at) as longest_session,
        MIN(NOW() - s.started_at) as newest_session
    FROM user_sessions s 
    JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE 
      AND s.last_activity > NOW() - INTERVAL '30 minutes';
END; $$;

-- ===== 🔍 دالة تفاصيل مستخدم محدد =====
CREATE OR REPLACE FUNCTION get_user_session_details(p_user_id UUID)
RETURNS TABLE (
    user_info TEXT,
    session_id UUID,
    device_info TEXT,
    ip_address INET,
    started_at TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    is_active BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(u.username, u.full_name, u.id::TEXT) as user_info,
        s.id as session_id,
        s.user_agent as device_info,
        s.ip_address,
        s.started_at,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.is_active
    FROM user_sessions s
    JOIN users u ON s.user_id = u.id
    WHERE s.user_id = p_user_id
      AND s.is_active = TRUE
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 🛑 دالة إنهاء جلسة (للمشرف) =====
CREATE OR REPLACE FUNCTION end_user_session(
    p_session_id UUID,
    p_end_reason TEXT DEFAULT 'admin_terminated'
)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions 
    SET is_active = FALSE, ended_at = NOW(), end_reason = p_end_reason
    WHERE id = p_session_id AND is_active = TRUE;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== 🧹 دالة تنظيف الجلسات القديمة =====
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE is_active = FALSE AND ended_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END; $$;

-- ===== 📋 دالة عرض هيكل الجدول =====
CREATE OR REPLACE FUNCTION show_users_structure()
RETURNS TABLE (
    column_name TEXT,
    data_type TEXT,
    is_nullable TEXT,
    column_default TEXT
) LANGUAGE plpgsql AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.column_name::TEXT,
        c.data_type::TEXT,
        c.is_nullable::TEXT,
        COALESCE(c.column_default::TEXT, 'لا يوجد') as column_default
    FROM information_schema.columns c
    WHERE c.table_name = 'users'
    ORDER BY c.ordinal_position;
END; $$;

-- ===== ✅ اختبار النظام الآمن =====
SELECT 'هيكل جدول users بعد التحديث:' as step;
SELECT * FROM show_users_structure();

SELECT 'تنظيف الجلسات...' as step;
SELECT cleanup_expired_sessions() as cleaned;

SELECT 'المستخدمون المتصلون (آمن):' as step;
SELECT * FROM get_online_users();

SELECT 'الإحصائيات المحسنة:' as step;
SELECT * FROM get_online_users_stats();

-- ===== 🎉 رسالة النجاح =====
SELECT '🛡️ تم إعداد النظام الآمن والمحسن بنجاح!' as status;
SELECT 'جميع الحقول المطلوبة متوفرة الآن' as fields_ready;
SELECT 'النظام يعمل بدون أخطاء' as error_free;
SELECT 'جاهز لتطبيق الإدارة' as admin_ready;

-- ===== 📝 الدوال المتاحة =====
/*
-- عرض هيكل جدول users
SELECT * FROM show_users_structure();

-- المستخدمون المتصلون مع التفاصيل
SELECT * FROM get_online_users();

-- الإحصائيات الشاملة
SELECT * FROM get_online_users_stats();

-- تفاصيل جلسة مستخدم محدد
SELECT * FROM get_user_session_details('user-uuid-here');

-- إنهاء جلسة محددة (للمشرف)
SELECT end_user_session('session-uuid-here', 'admin_action');

-- تنظيف الجلسات القديمة
SELECT cleanup_old_sessions();
*/
