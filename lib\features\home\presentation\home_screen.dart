import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/permissions_service.dart';
import '../../../core/utils/logger.dart';
import '../../../core/routing/app_router.dart';
import '../../camera/presentation/camera_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final _logger = getLogger();
  bool _isCheckingPermissions = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _checkAndRequestPermissions();
  }

  Future<void> _checkAndRequestPermissions() async {
    try {
      final permissionsService = ref.read(permissionsServiceProvider);
      var permissionsResult = await permissionsService.checkAllPermissions();

      if (!permissionsResult['success']) {
        permissionsResult = await permissionsService.requestAllPermissions();
        if (!permissionsResult['success']) {
          final message = permissionsResult['message'] ?? 'permissions.all_permissions_required'.tr();
          setState(() {
            _error = message;
            _isCheckingPermissions = false;
          });
          return;
        }
      }

      if (mounted) {
        setState(() => _isCheckingPermissions = false);
      }
    } catch (e) {
      _logger.e('Error checking permissions', error: e);
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isCheckingPermissions = false;
        });
      }
    }
  }

  Future<void> _handleLogout() async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();
      if (!mounted) return;
      Navigator.of(context).pushNamedAndRemoveUntil(
        AppRoutes.login,
        (route) => false,
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.toString())),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingPermissions) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري التحقق من الأذونات...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(_error!, textAlign: TextAlign.center),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                    _isCheckingPermissions = true;
                  });
                  _checkAndRequestPermissions();
                },
                child: const Text('إعادة المحاولة'),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: _handleLogout,
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
        ),
      );
    }

    return const CameraScreen();
  }
}
