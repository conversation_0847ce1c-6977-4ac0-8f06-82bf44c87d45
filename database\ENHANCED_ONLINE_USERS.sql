-- ===== 🚀 تحسين دوال المستخدمين المتصلين =====
-- بناءً على النظام الأساسي الذي يعمل بنجاح

-- ===== 📊 فحص الحقول المتاحة في جدول users =====
SELECT 'الحقول المتاحة في جدول users:' as info;
SELECT column_name, data_type
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- ===== 🔧 إضافة الحقول المفقودة (اختياري) =====
DO $$ 
BEGIN
    -- إضافة username إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') THEN
        ALTER TABLE users ADD COLUMN username TEXT;
        RAISE NOTICE '✅ تم إضافة حقل username';
    END IF;
    
    -- إضافة full_name إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE users ADD COLUMN full_name TEXT;
        RAISE NOTICE '✅ تم إضافة حقل full_name';
    END IF;
    
    -- إضافة location_code إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') THEN
        ALTER TABLE users ADD COLUMN location_code TEXT;
        RAISE NOTICE '✅ تم إضافة حقل location_code';
    END IF;
    
    -- إضافة is_admin إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ تم إضافة حقل is_admin';
    END IF;
END $$;

-- ===== 👥 دالة محسنة للمستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
    user_id UUID,
    user_identifier TEXT,
    user_display_name TEXT,
    location_info TEXT,
    session_id UUID,
    device_info TEXT,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    ip_address INET,
    is_admin BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    has_username BOOLEAN;
    has_email BOOLEAN;
    has_full_name BOOLEAN;
    has_name BOOLEAN;
    has_location_code BOOLEAN;
    has_is_admin BOOLEAN;
BEGIN
    PERFORM cleanup_expired_sessions();

    -- فحص الحقول المتاحة مرة واحدة
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') INTO has_username;
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email') INTO has_email;
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') INTO has_full_name;
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'name') INTO has_name;
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') INTO has_location_code;
    SELECT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') INTO has_is_admin;

    RETURN QUERY EXECUTE format('
        SELECT
            u.id as user_id,
            COALESCE(%s, u.id::TEXT) as user_identifier,
            COALESCE(%s, ''مستخدم'') as user_display_name,
            COALESCE(%s, ''غير محدد'') as location_info,
            s.id as session_id,
            s.user_agent as device_info,
            s.last_activity,
            (NOW() - s.started_at) as session_duration,
            s.ip_address,
            COALESCE(%s, FALSE) as is_admin
        FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.is_active = TRUE
          AND s.last_activity > NOW() - INTERVAL ''30 minutes''
        ORDER BY s.last_activity DESC',
        CASE
            WHEN has_username THEN 'u.username'
            WHEN has_email THEN 'u.email'
            ELSE 'NULL'
        END,
        CASE
            WHEN has_full_name THEN 'u.full_name'
            WHEN has_name THEN 'u.name'
            ELSE 'NULL'
        END,
        CASE
            WHEN has_location_code THEN 'u.location_code'
            ELSE 'NULL'
        END,
        CASE
            WHEN has_is_admin THEN 'u.is_admin'
            ELSE 'NULL'
        END
    );
END; $$;

-- ===== 📊 دالة إحصائيات محسنة =====
CREATE OR REPLACE FUNCTION get_online_users_stats()
RETURNS TABLE (
    total_online INTEGER,
    admin_online INTEGER,
    regular_users_online INTEGER,
    active_sessions INTEGER,
    avg_session_duration INTERVAL,
    longest_session INTERVAL,
    newest_session INTERVAL
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT s.user_id)::INTEGER as total_online,
        COUNT(DISTINCT CASE 
            WHEN EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') 
                 AND (SELECT is_admin FROM users WHERE id = s.user_id) = TRUE 
            THEN s.user_id 
        END)::INTEGER as admin_online,
        COUNT(DISTINCT CASE 
            WHEN NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') 
                 OR COALESCE((SELECT is_admin FROM users WHERE id = s.user_id), FALSE) = FALSE 
            THEN s.user_id 
        END)::INTEGER as regular_users_online,
        COUNT(s.id)::INTEGER as active_sessions,
        AVG(NOW() - s.started_at) as avg_session_duration,
        MAX(NOW() - s.started_at) as longest_session,
        MIN(NOW() - s.started_at) as newest_session
    FROM user_sessions s 
    WHERE s.is_active = TRUE 
      AND s.last_activity > NOW() - INTERVAL '30 minutes';
END; $$;

-- ===== 🔍 دالة تفاصيل مستخدم محدد =====
CREATE OR REPLACE FUNCTION get_user_session_details(p_user_id UUID)
RETURNS TABLE (
    session_id UUID,
    device_info TEXT,
    ip_address INET,
    started_at TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    is_active BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id as session_id,
        s.user_agent as device_info,
        s.ip_address,
        s.started_at,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.is_active
    FROM user_sessions s
    WHERE s.user_id = p_user_id
      AND s.is_active = TRUE
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 🛑 دالة إنهاء جلسة مستخدم (للمشرف) =====
CREATE OR REPLACE FUNCTION end_user_session(
    p_session_id UUID,
    p_end_reason TEXT DEFAULT 'admin_terminated'
)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions 
    SET is_active = FALSE, ended_at = NOW(), end_reason = p_end_reason
    WHERE id = p_session_id AND is_active = TRUE;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== 🧹 دالة تنظيف الجلسات القديمة =====
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE is_active = FALSE AND ended_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END; $$;

-- ===== ✅ اختبار الدوال المحسنة =====
SELECT 'تنظيف الجلسات...' as step;
SELECT cleanup_expired_sessions() as cleaned;

SELECT 'المستخدمون المتصلون (محسن):' as step;
SELECT * FROM get_online_users();

SELECT 'الإحصائيات المحسنة:' as step;
SELECT * FROM get_online_users_stats();

-- ===== 🎉 رسالة النجاح =====
SELECT '🚀 تم تحسين النظام بنجاح!' as status;
SELECT 'الآن لديك معلومات شاملة عن المستخدمين المتصلين' as enhanced;
SELECT 'يمكنك استخدام هذه الدوال في تطبيق الإدارة' as admin_ready;

-- ===== 📝 دوال متاحة للاستخدام =====
/*
-- المستخدمون المتصلون مع التفاصيل
SELECT * FROM get_online_users();

-- الإحصائيات الشاملة
SELECT * FROM get_online_users_stats();

-- تفاصيل جلسة مستخدم محدد
SELECT * FROM get_user_session_details('user-uuid-here');

-- إنهاء جلسة محددة (للمشرف)
SELECT end_user_session('session-uuid-here', 'admin_action');

-- تنظيف الجلسات القديمة
SELECT cleanup_old_sessions();
*/
