import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/logger.dart';
import 'videos_service.dart';
import 'cache_service.dart';
import 'security_service.dart';


/// خدمة الرفع التلقائي للملفات
class AutoUploadService {
  static final AutoUploadService _instance = AutoUploadService._internal();
  factory AutoUploadService() => _instance;
  AutoUploadService._internal();

  final _logger = getLogger();
  final _securityService = SecurityService();

  Timer? _uploadTimer;
  bool _isUploading = false;
  bool _isOfflineMode = false;

  // الحصول على عميل Supabase
  SupabaseClient get _supabase => Supabase.instance.client;

  // الحصول على اسم المستخدم الحالي
  String? get _currentUsername => _supabase.auth.currentUser?.userMetadata?['full_name'] ??
                                  _supabase.auth.currentUser?.email?.split('@').first;

  /// تحليل كود الموقع الجديد (مثل U101, C145)
  Map<String, String?> _parseLocationCode(String? location) {
    if (location == null || location.isEmpty) {
      return {'type': null, 'number': null};
    }

    // التحقق من تطابق النمط الجديد (U/C + رقم)
    final regex = RegExp(r'^([UC])(\d+)$');
    final match = regex.firstMatch(location);

    if (match != null) {
      return {
        'type': match.group(1),
        'number': match.group(2),
      };
    }

    // إذا لم يتطابق مع النمط الجديد، اعتبره موقع قديم
    return {'type': 'U', 'number': '101'}; // قيم افتراضية
  }

  /// بدء خدمة الرفع التلقائي
  void startAutoUpload() async {
    _logger.i('🚀 Starting auto upload service with security');

    // تهيئة خدمات الأمان
    try {
      await _securityService.initialize();
      _logger.i('🛡️ Security service initialized');
    } catch (e) {
      _logger.e('❌ Security initialization failed: $e');
    }

    // تحميل حالة الاتصال المحفوظة
    _isOfflineMode = await getOfflineModeFromStorage();
    _logger.i('📡 Loaded offline mode state: $_isOfflineMode');

    // إيقاف المؤقت السابق إن وجد
    _uploadTimer?.cancel();

    // بدء مؤقت للفحص كل 30 ثانية
    _uploadTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkAndUploadPendingFiles();
    });

    // فحص فوري عند البدء
    _checkAndUploadPendingFiles();
  }

  /// إيقاف خدمة الرفع التلقائي
  void stopAutoUpload() {
    _logger.i('Stopping auto upload service');
    _uploadTimer?.cancel();
    _uploadTimer = null;
  }

  /// إعادة تعيين حالة الرفع (للطوارئ)
  void resetUploadState() {
    _isUploading = false;
    _logger.i('🔄 Upload state reset manually');
  }

  /// فحص ورفع الملفات المعلقة
  Future<void> _checkAndUploadPendingFiles() async {
    if (_isUploading) {
      _logger.d('Upload already in progress, skipping');
      return;
    }

    try {
      _isUploading = true;
      _logger.d('Starting upload check...');

      // فحص الاتصال بالإنترنت
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        _logger.d('No internet connection, switching to offline mode');
        await _setOfflineMode(true);
        return;
      }

      // اختبار الاتصال بـ Supabase
      try {
        await _supabase.from('photos').select('id').limit(1);
        _logger.d('Supabase connection test successful');
        await _setOfflineMode(false);
      } catch (e) {
        _logger.w('Supabase connection test failed: $e');

        // في حالة مشكلة المفتاح أو مشاكل تقنية في Supabase، نعمل في الوضع المحلي
        if (e.toString().contains('Invalid API key') ||
            e.toString().contains('401') ||
            e.toString().contains('Unauthorized') ||
            e.toString().contains('technical issue') ||
            e.toString().contains('server error')) {
          _logger.w('⚠️ Supabase service issue detected, continuing in offline mode');
          _logger.i('📱 App will work locally until service is restored');
          await _setOfflineMode(true);
          return;
        }

        await _setOfflineMode(true);
        return;
      }

      // الحصول على الملفات المعلقة (مع فك التشفير)
      final pendingUploads = await _getSecurePendingUploads();

      // فحص المعرض المحلي للفيديوهات أيضاً
      await _checkLocalGalleryForVideos();

      if (pendingUploads.isEmpty) {
        _logger.d('No pending uploads found');
        return;
      }

      _logger.i('Found ${pendingUploads.length} pending uploads');

      final List<String> successfulUploads = [];
      final List<String> failedUploads = [];

      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          
          // تخطي الملفات التي تم رفعها بالفعل
          if (uploadData['status'] == 'uploaded') {
            successfulUploads.add(uploadJson);
            continue;
          }

          // محاولة رفع الملف مع timeout
          final success = await _uploadFile(uploadData).timeout(
            const Duration(minutes: 2),
            onTimeout: () {
              _logger.w('Upload timeout for: ${uploadData['file_name']}');
              return false;
            },
          );
          
          if (success) {
            uploadData['status'] = 'uploaded';
            uploadData['uploaded_at'] = DateTime.now().toIso8601String();
            successfulUploads.add(jsonEncode(uploadData));
            _logger.i('Successfully uploaded: ${uploadData['file_name']}');

            // مسح الملف من التخزين المحلي بعد الرفع بنجاح (إذا كان مفعلاً)
            if (_autoDeleteEnabled) {
              await _deleteLocalFile(uploadData['file_path'] as String);
            }
          } else {
            uploadData['status'] = 'failed';
            uploadData['last_attempt'] = DateTime.now().toIso8601String();
            failedUploads.add(jsonEncode(uploadData));
            _logger.w('Failed to upload: ${uploadData['file_name']}');
          }
        } catch (e) {
          _logger.e('Error processing upload: $e');
          failedUploads.add(uploadJson);
        }
      }

      // تحديث قائمة الملفات المعلقة (مع التشفير)
      await _saveSecurePendingUploads(failedUploads);

      // حفظ الملفات المرفوعة بنجاح (مع التشفير)
      if (successfulUploads.isNotEmpty) {
        await _saveSecureUploadedFiles(successfulUploads);
      }

      _logger.i('Upload batch completed: ${successfulUploads.length} successful, ${failedUploads.length} failed');

    } catch (e) {
      _logger.e('Error in auto upload service: $e');
    } finally {
      _isUploading = false;
      _logger.d('Upload process finished, flag reset');
    }
  }

  /// رفع ملف واحد مع إعادة المحاولة
  Future<bool> _uploadFile(Map<String, dynamic> uploadData) async {
    const maxRetries = 3;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final filePath = uploadData['file_path'] as String;
        final fileName = uploadData['file_name'] as String;
        final location = uploadData['location'] as String;
        final fileType = uploadData['type'] as String; // 'photo' or 'video'

        // التحقق من وجود الملف
        final file = File(filePath);
        if (!await file.exists()) {
          _logger.w('File not found: $filePath');
          return false;
        }

        _logger.d('Upload attempt $attempt/$maxRetries for: $fileName');

        // رفع الملف حسب النوع
        bool success = false;
        if (fileType == 'photo') {
          success = await _uploadPhoto(file, fileName, location);
        } else if (fileType == 'video') {
          success = await _uploadVideo(file, fileName, location);
        } else {
          _logger.w('Unknown file type: $fileType');
          return false;
        }

        if (success) {
          _logger.i('Upload successful on attempt $attempt: $fileName');
          return true;
        }

      } catch (e) {
        _logger.w('Upload attempt $attempt failed: $e');

        // إذا كانت مشكلة JWT منتهي الصلاحية، حاول تجديد الجلسة
        if (e.toString().contains('exp') ||
            e.toString().contains('Unauthorized') ||
            e.toString().contains('403')) {
          _logger.w('JWT token expired, attempting to refresh session');
          if (await _checkAndRefreshAuth()) {
            // إذا تم تجديد الجلسة بنجاح، أعد المحاولة
            continue;
          } else {
            _logger.e('Failed to refresh session, stopping upload');
            return false;
          }
        }

        // إذا كانت مشكلة شبكة، انتظر قبل إعادة المحاولة
        if (e.toString().contains('SocketException') ||
            e.toString().contains('ClientException')) {
          if (attempt < maxRetries) {
            await Future.delayed(Duration(seconds: attempt * 5));
          }
        } else {
          // إذا كانت مشكلة أخرى، لا تعيد المحاولة
          break;
        }
      }
    }

    _logger.e('All upload attempts failed for: ${uploadData['file_name']}');
    return false;
  }

  /// رفع صورة
  Future<bool> _uploadPhoto(File file, String fileName, String location) async {
    try {
      // 🔍 فحص حالة المصادقة
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        _logger.e('❌ المستخدم غير مصادق عليه - لا يمكن رفع الصورة');
        return false;
      }

      _logger.i('🔐 المستخدم مصادق عليه: ${currentUser.id}');

      // فحص إذا كانت الصورة مرفوعة بالفعل
      final existingPhoto = await _supabase
          .from('photos')
          .select('id')
          .eq('file_name', fileName)
          .maybeSingle();

      if (existingPhoto != null) {
        _logger.i('Photo already exists in database: $fileName');
        return true; // اعتبرها نجحت لأنها موجودة بالفعل
      }

      // 🔧 تجاوز Storage والحفظ مباشرة في قاعدة البيانات
      String imageUrl = 'local://photos/$fileName'; // رابط محلي مؤقت

      try {
        // محاولة رفع إلى Storage (اختياري)
        final response = await _supabase.storage
            .from('photos')
            .upload('uploads/$fileName', file);

        if (response.isNotEmpty) {
          // الحصول على رابط الصورة من Storage
          imageUrl = _supabase.storage
              .from('photos')
              .getPublicUrl('uploads/$fileName');
        }
      } catch (storageError) {
        _logger.w('Storage upload failed, using local path: $storageError');
        // استمرار بالرابط المحلي
      }

      // الحفظ في قاعدة البيانات في جميع الحالات
      // تحليل كود الموقع الجديد
      final locationData = _parseLocationCode(location);
      final currentTime = DateTime.now();

      // حفظ معلومات الصورة في قاعدة البيانات مع النظام الجديد
      await _supabase.from('photos').insert({
        'file_name': fileName,
        'url': imageUrl,  // تغيير من file_url إلى url
        'location': location,
        'location_type': locationData['type'],
        'location_number': locationData['number'],
        'username': _currentUsername,
        'created_at': currentTime.toIso8601String(),  // تغيير من uploaded_at إلى created_at
        'capture_timestamp': currentTime.toIso8601String(),
        'upload_timestamp': currentTime.toIso8601String(),
        'user_id': _supabase.auth.currentUser?.id,
      });

      // حذف الصورة من المعرض المحلي بعد الرفع الناجح
      await _deleteLocalFile(file.path);

      return true;
    } catch (e) {
      // إذا كان الخطأ بسبب ملف موجود بالفعل، اعتبره نجاح
      if (e.toString().contains('already exists') ||
          e.toString().contains('duplicate')) {
        _logger.i('File already exists, considering as success: $fileName');
        // حذف الملف المحلي حتى لو كان موجود بالفعل
        await _deleteLocalFile(file.path);
        return true;
      }

      _logger.e('Error uploading photo: $e');
      return false;
    }
  }

  /// رفع فيديو
  Future<bool> _uploadVideo(File file, String fileName, String location) async {
    try {
      // فحص إذا كان الفيديو مرفوع بالفعل
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId != null) {
        final existingVideo = await _supabase
            .from('videos')
            .select('id, file_name')
            .eq('file_name', fileName)
            .eq('user_id', currentUserId)
            .maybeSingle();

        if (existingVideo != null) {
          _logger.i('Video already exists in database: $fileName');
          return true; // اعتبرها نجحت لأنها موجودة بالفعل
        }
      }

      String? response;
      String? videoUrl;

      // محاولة رفع الفيديو إلى bucket الفيديوهات أولاً
      try {
        response = await _supabase.storage
            .from('videos')
            .upload('uploads/$fileName', file,
                fileOptions: const FileOptions(upsert: true));

        if (response.isNotEmpty) {
          // الحصول على رابط الفيديو من bucket الفيديوهات
          videoUrl = _supabase.storage
              .from('videos')
              .getPublicUrl('uploads/$fileName');
        }
      } catch (e) {
        _logger.w('Videos bucket not available, trying photos bucket: $e');

        // fallback إلى bucket الصور
        response = await _supabase.storage
            .from('photos')
            .upload('uploads/$fileName', file,
                fileOptions: const FileOptions(upsert: true));

        if (response.isNotEmpty) {
          // الحصول على رابط الفيديو من bucket الصور
          videoUrl = _supabase.storage
              .from('photos')
              .getPublicUrl('uploads/$fileName');
        }
      }

      if (response.isNotEmpty) {

        // تحليل كود الموقع الجديد
        final locationData = _parseLocationCode(location);
        final currentTime = DateTime.now();

        // حفظ معلومات الفيديو في قاعدة البيانات مع النظام الجديد
        await _supabase.from('videos').insert({
          'file_name': fileName,
          'video_url': videoUrl,  // استخدام video_url بدلاً من url
          'storage_path': 'uploads/$fileName',  // إضافة storage_path
          'location': location,
          'location_type': locationData['type'],
          'location_number': locationData['number'],
          'username': _currentUsername,
          'date_time': currentTime.toIso8601String(),  // استخدام date_time
          'capture_timestamp': currentTime.toIso8601String(),
          'upload_timestamp': currentTime.toIso8601String(),
          'user_id': _supabase.auth.currentUser?.id,
        });

        // حذف الفيديو من المعرض المحلي بعد الرفع الناجح
        await _deleteLocalFile(file.path);

        return true;
      }
      return false;
    } catch (e) {
      _logger.e('Error uploading video: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الرفع (مع التشفير)
  Future<Map<String, int>> getUploadStats() async {
    try {
      final pendingUploads = await _getSecurePendingUploads();

      final uploadedData = await _securityService.loadSecureData('uploaded_files');
      final uploadedFiles = uploadedData?['uploads'] as List<String>? ?? [];

      return {
        'pending': pendingUploads.length,
        'uploaded': uploadedFiles.length,
        'total': pendingUploads.length + uploadedFiles.length,
      };
    } catch (e) {
      _logger.e('Error getting upload stats: $e');
      return {'pending': 0, 'uploaded': 0, 'total': 0};
    }
  }

  /// مسح الملفات المرفوعة من التخزين المحلي
  Future<void> clearUploadedFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('uploaded_files');
      _logger.i('Cleared uploaded files list');
    } catch (e) {
      _logger.e('Error clearing uploaded files: $e');
    }
  }

  /// فحص المعرض المحلي للفيديوهات المعلقة
  Future<void> _checkLocalGalleryForVideos() async {
    try {
      _logger.i('Checking local gallery for pending video uploads...');

      // الحصول على مجلد المعرض
      final Directory appDir = await getApplicationDocumentsDirectory();
      final Directory galleryDir = Directory('${appDir.path}/gallery');

      if (!await galleryDir.exists()) {
        _logger.d('Gallery directory does not exist');
        return;
      }

      // البحث عن ملفات الفيديو
      final List<FileSystemEntity> videoFiles = galleryDir
          .listSync()
          .where((file) => file.path.toLowerCase().endsWith('.mp4'))
          .toList();

      if (videoFiles.isEmpty) {
        _logger.d('No video files found in local gallery');
        return;
      }

      _logger.i('Found ${videoFiles.length} video files in local gallery');

      // معالجة كل ملف فيديو
      for (final videoFile in videoFiles) {
        try {
          _logger.i('Processing video file: ${videoFile.path}');

          // استخدام VideosService لرفع الفيديو
          final videosService = VideosService(_supabase, CacheService());
          await videosService.uploadVideo(
            videoFile.path,
            'Unknown', // سيتم تحديد الموقع من metadata إذا كان متوفراً
          );

        } catch (e) {
          _logger.e('Failed to upload video ${videoFile.path}: $e');
        }
      }

    } catch (e) {
      _logger.e('Error checking local gallery for videos: $e');
    }
  }

  /// إعادة محاولة رفع الملفات الفاشلة
  Future<void> retryFailedUploads() async {
    _logger.i('Retrying failed uploads');
    await _checkAndUploadPendingFiles();
  }

  /// مسح ملف من التخزين المحلي
  Future<void> _deleteLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        _logger.i('Deleted local file: $filePath');
      } else {
        _logger.w('File not found for deletion: $filePath');
      }
    } catch (e) {
      _logger.e('Error deleting local file: $e');
    }
  }

  /// مسح جميع الملفات المرفوعة من التخزين المحلي
  Future<void> deleteAllUploadedFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];

      int deletedCount = 0;
      for (final uploadJson in uploadedFiles) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          final filePath = uploadData['file_path'] as String;
          await _deleteLocalFile(filePath);
          deletedCount++;
        } catch (e) {
          _logger.e('Error deleting uploaded file: $e');
        }
      }

      _logger.i('Deleted $deletedCount uploaded files from local storage');
    } catch (e) {
      _logger.e('Error deleting uploaded files: $e');
    }
  }

  /// فحص وتجديد JWT token
  Future<bool> _checkAndRefreshAuth() async {
    try {
      final session = _supabase.auth.currentSession;

      if (session == null) {
        _logger.w('No active session found');
        return false;
      }

      // فحص إذا كان token قارب على الانتهاء (أقل من 5 دقائق)
      final expiresAt = session.expiresAt;
      if (expiresAt != null) {
        final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000);
        final timeUntilExpiry = expiryTime.difference(DateTime.now());

        if (timeUntilExpiry.inMinutes < 5) {
          _logger.i('Token expires soon, attempting refresh');

          try {
            await _supabase.auth.refreshSession();
            _logger.i('Session refreshed successfully');
            return true;
          } catch (e) {
            _logger.e('Failed to refresh session: $e');
            return false;
          }
        }
      }

      return true; // Token still valid
    } catch (e) {
      _logger.e('Error checking auth status: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل المسح التلقائي
  static bool _autoDeleteEnabled = true;

  static bool get autoDeleteEnabled => _autoDeleteEnabled;

  static void setAutoDelete(bool enabled) {
    _autoDeleteEnabled = enabled;
  }

  /// تعيين وضع عدم الاتصال
  Future<void> _setOfflineMode(bool isOffline) async {
    try {
      _isOfflineMode = isOffline;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_offline_mode', isOffline);

      if (isOffline) {
        _logger.w('📴 Switched to OFFLINE mode');
      } else {
        _logger.i('🌐 Switched to ONLINE mode');
      }
    } catch (e) {
      _logger.e('Error setting offline mode: $e');
    }
  }

  /// الحصول على حالة الاتصال
  bool get isOfflineMode => _isOfflineMode;

  /// فحص حالة الاتصال من SharedPreferences
  Future<bool> getOfflineModeFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('is_offline_mode') ?? false;
    } catch (e) {
      _logger.e('Error getting offline mode from storage: $e');
      return false;
    }
  }

  /// الحصول على الملفات المعلقة مع فك التشفير
  Future<List<String>> _getSecurePendingUploads() async {
    try {
      final data = await _securityService.loadSecureData('pending_uploads');
      if (data != null && data['uploads'] is List) {
        return List<String>.from(data['uploads']);
      }

      // fallback للطريقة القديمة غير المشفرة
      final prefs = await SharedPreferences.getInstance();
      final oldUploads = prefs.getStringList('pending_uploads') ?? [];

      // ترحيل البيانات للتشفير
      if (oldUploads.isNotEmpty) {
        await _saveSecurePendingUploads(oldUploads);
        await prefs.remove('pending_uploads');
        _logger.i('🔄 Migrated ${oldUploads.length} uploads to encrypted storage');
      }

      return oldUploads;
    } catch (e) {
      _logger.e('❌ Error loading secure pending uploads: $e');
      return [];
    }
  }

  /// حفظ الملفات المعلقة مع التشفير
  Future<void> _saveSecurePendingUploads(List<String> uploads) async {
    try {
      await _securityService.saveSecureData('pending_uploads', {
        'uploads': uploads,
        'timestamp': DateTime.now().toIso8601String(),
      });
      _logger.d('🔐 Saved ${uploads.length} pending uploads securely');
    } catch (e) {
      _logger.e('❌ Error saving secure pending uploads: $e');
      // fallback للطريقة القديمة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('pending_uploads', uploads);
    }
  }

  /// حفظ الملفات المرفوعة مع التشفير
  Future<void> _saveSecureUploadedFiles(List<String> uploads) async {
    try {
      final existingData = await _securityService.loadSecureData('uploaded_files');
      final existingUploads = existingData?['uploads'] as List<String>? ?? [];

      existingUploads.addAll(uploads);

      await _securityService.saveSecureData('uploaded_files', {
        'uploads': existingUploads,
        'timestamp': DateTime.now().toIso8601String(),
      });
      _logger.d('🔐 Saved ${uploads.length} uploaded files securely');
    } catch (e) {
      _logger.e('❌ Error saving secure uploaded files: $e');
      // fallback للطريقة القديمة
      final prefs = await SharedPreferences.getInstance();
      final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];
      uploadedFiles.addAll(uploads);
      await prefs.setStringList('uploaded_files', uploadedFiles);
    }
  }
}
