-- تحديث جدول الأجهزة لدعم نظام البصمة الرقمية المتقدم
-- Migration: 001_update_devices_table.sql
-- Date: 2025-01-12
-- Description: إضافة حقول البصمة الرقمية المتقدمة لجدول devices

-- إضافة الحقول الجديدة لجدول devices
ALTER TABLE devices 
ADD COLUMN IF NOT EXISTS device_fingerprint TEXT,
ADD COLUMN IF NOT EXISTS device_product TEXT,
ADD COLUMN IF NOT EXISTS device_hardware TEXT,
ADD COLUMN IF NOT EXISTS android_id TEXT,
ADD COLUMN IF NOT EXISTS build_fingerprint TEXT,
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS trust_level TEXT,
ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS hardware_info TEXT,
ADD COLUMN IF NOT EXISTS system_info TEXT,
ADD COLUMN IF NOT EXISTS screen_info TEXT,
ADD COLUMN IF NOT EXISTS cpu_info TEXT,
ADD COLUMN IF NOT EXISTS storage_info TEXT,
ADD COLUMN IF NOT EXISTS raw_fingerprint_data TEXT,
ADD COLUMN IF NOT EXISTS auth_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_auth_attempt TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_blocked BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS blocked_until TIMESTAMP WITH TIME ZONE;

-- إضافة فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_devices_device_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_trust_level ON devices(trust_level);
CREATE INDEX IF NOT EXISTS idx_devices_last_verified ON devices(last_verified_at);

-- إضافة قيود للتحقق من صحة البيانات
DO $$
BEGIN
    -- إضافة constraint للـ confidence_score
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_confidence_score'
        AND table_name = 'devices'
    ) THEN
        ALTER TABLE devices
        ADD CONSTRAINT chk_confidence_score
        CHECK (confidence_score >= 0 AND confidence_score <= 100);
    END IF;

    -- إضافة constraint للـ trust_level
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_trust_level'
        AND table_name = 'devices'
    ) THEN
        ALTER TABLE devices
        ADD CONSTRAINT chk_trust_level
        CHECK (trust_level IN ('high', 'medium', 'low', 'untrusted', 'suspicious', 'blocked'));
    END IF;

    -- إضافة constraint للـ auth_attempts
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'chk_auth_attempts'
        AND table_name = 'devices'
    ) THEN
        ALTER TABLE devices
        ADD CONSTRAINT chk_auth_attempts
        CHECK (auth_attempts >= 0);
    END IF;
END $$;

-- إضافة تعليقات للحقول
COMMENT ON COLUMN devices.device_fingerprint IS 'البصمة الرقمية المتقدمة للجهاز (SHA-256)';
COMMENT ON COLUMN devices.android_id IS 'معرف Android الفريد للجهاز';
COMMENT ON COLUMN devices.build_fingerprint IS 'بصمة البناء الخاصة بنظام التشغيل';
COMMENT ON COLUMN devices.confidence_score IS 'نقاط الثقة في البصمة (0-100)';
COMMENT ON COLUMN devices.trust_level IS 'مستوى الثقة في الجهاز';
COMMENT ON COLUMN devices.last_verified_at IS 'آخر وقت تم التحقق من الجهاز';
COMMENT ON COLUMN devices.hardware_info IS 'معلومات الهاردوير المجمعة';
COMMENT ON COLUMN devices.system_info IS 'معلومات النظام المجمعة';
COMMENT ON COLUMN devices.raw_fingerprint_data IS 'البيانات الخام للبصمة الرقمية';
COMMENT ON COLUMN devices.auth_attempts IS 'عدد محاولات المصادقة الفاشلة';
COMMENT ON COLUMN devices.is_blocked IS 'هل الجهاز محجوب';
COMMENT ON COLUMN devices.blocked_until IS 'وقت انتهاء الحجب';

-- إنشاء دالة للتحقق من الجهاز المحسنة
CREATE OR REPLACE FUNCTION verify_device_enhanced(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_build_fingerprint TEXT,
    p_confidence_score DECIMAL,
    p_trust_level TEXT,
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL,
    p_device_product TEXT DEFAULT NULL,
    p_device_hardware TEXT DEFAULT NULL,
    p_hardware_info TEXT DEFAULT NULL,
    p_system_info TEXT DEFAULT NULL,
    p_screen_info TEXT DEFAULT NULL,
    p_cpu_info TEXT DEFAULT NULL,
    p_storage_info TEXT DEFAULT NULL,
    p_raw_data TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_devices INTEGER := 3; -- الحد الأقصى للأجهزة لكل مستخدم
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود بالفعل
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- الجهاز موجود - تحديث المعلومات
        UPDATE devices SET
            device_fingerprint = p_device_fingerprint,
            android_id = p_android_id,
            build_fingerprint = p_build_fingerprint,
            confidence_score = p_confidence_score,
            trust_level = p_trust_level,
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            device_product = COALESCE(p_device_product, device_product),
            device_hardware = COALESCE(p_device_hardware, device_hardware),
            hardware_info = COALESCE(p_hardware_info, hardware_info),
            system_info = COALESCE(p_system_info, system_info),
            screen_info = COALESCE(p_screen_info, screen_info),
            cpu_info = COALESCE(p_cpu_info, cpu_info),
            storage_info = COALESCE(p_storage_info, storage_info),
            raw_fingerprint_data = COALESCE(p_raw_data, raw_fingerprint_data),
            last_verified_at = NOW(),
            auth_attempts = 0,
            is_blocked = FALSE,
            blocked_until = NULL
        WHERE id = device_record.id;
        
        result := json_build_object(
            'status', 'success',
            'action', 'updated',
            'device_id', device_record.id,
            'message', 'تم تحديث معلومات الجهاز بنجاح'
        );
    ELSE
        -- جهاز جديد - فحص الحد الأقصى للأجهزة
        SELECT COUNT(*) INTO device_count 
        FROM devices 
        WHERE user_id = p_user_id;
        
        IF device_count >= max_devices THEN
            result := json_build_object(
                'status', 'error',
                'code', 'DEVICE_LIMIT_REACHED',
                'message', 'تم الوصول للحد الأقصى من الأجهزة المسموحة'
            );
        ELSE
            -- إضافة الجهاز الجديد
            INSERT INTO devices (
                user_id,
                device_fingerprint,
                android_id,
                build_fingerprint,
                confidence_score,
                trust_level,
                device_name,
                device_model,
                device_brand,
                device_product,
                device_hardware,
                hardware_info,
                system_info,
                screen_info,
                cpu_info,
                storage_info,
                raw_fingerprint_data,
                last_verified_at,
                created_at
            ) VALUES (
                p_user_id,
                p_device_fingerprint,
                p_android_id,
                p_build_fingerprint,
                p_confidence_score,
                p_trust_level,
                p_device_name,
                p_device_model,
                p_device_brand,
                p_device_product,
                p_device_hardware,
                p_hardware_info,
                p_system_info,
                p_screen_info,
                p_cpu_info,
                p_storage_info,
                p_raw_data,
                NOW(),
                NOW()
            ) RETURNING id INTO device_record;
            
            result := json_build_object(
                'status', 'success',
                'action', 'created',
                'device_id', device_record.id,
                'message', 'تم إضافة الجهاز الجديد بنجاح'
            );
        END IF;
    END IF;
    
    RETURN result;
END;
$$;

-- إنشاء دالة لتسجيل محاولة مصادقة فاشلة
CREATE OR REPLACE FUNCTION record_failed_auth_attempt(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_attempts INTEGER := 5;
    block_duration INTERVAL := '15 minutes';
    result JSON;
BEGIN
    -- البحث عن الجهاز
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- تحديث عدد المحاولات
        UPDATE devices SET
            auth_attempts = auth_attempts + 1,
            last_auth_attempt = NOW()
        WHERE id = device_record.id;
        
        -- فحص ما إذا كان يجب حجب الجهاز
        IF (device_record.auth_attempts + 1) >= max_attempts THEN
            UPDATE devices SET
                is_blocked = TRUE,
                blocked_until = NOW() + block_duration
            WHERE id = device_record.id;
            
            result := json_build_object(
                'status', 'blocked',
                'message', 'تم حجب الجهاز بسبب تجاوز الحد الأقصى للمحاولات',
                'blocked_until', NOW() + block_duration
            );
        ELSE
            result := json_build_object(
                'status', 'failed',
                'attempts', device_record.auth_attempts + 1,
                'max_attempts', max_attempts,
                'message', 'محاولة مصادقة فاشلة'
            );
        END IF;
    ELSE
        result := json_build_object(
            'status', 'not_found',
            'message', 'الجهاز غير مسجل'
        );
    END IF;
    
    RETURN result;
END;
$$;

-- إنشاء دالة لإعادة تعيين محاولات المصادقة
CREATE OR REPLACE FUNCTION reset_auth_attempts(
    p_user_id UUID,
    p_device_fingerprint TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE devices SET
        auth_attempts = 0,
        is_blocked = FALSE,
        blocked_until = NULL,
        last_verified_at = NOW()
    WHERE user_id = p_user_id 
    AND device_fingerprint = p_device_fingerprint;
    
    RETURN FOUND;
END;
$$;

-- إنشاء دالة لتنظيف الأجهزة القديمة
CREATE OR REPLACE FUNCTION cleanup_old_devices()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- حذف الأجهزة التي لم يتم التحقق منها لأكثر من 90 يوم
    DELETE FROM devices 
    WHERE last_verified_at < NOW() - INTERVAL '90 days'
    OR (created_at < NOW() - INTERVAL '90 days' AND last_verified_at IS NULL);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

-- إنشاء مهمة دورية لتنظيف الأجهزة القديمة (يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-old-devices', '0 2 * * *', 'SELECT cleanup_old_devices();');

-- إضافة Row Level Security (RLS)
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين - يمكنهم رؤية أجهزتهم فقط
CREATE POLICY devices_user_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسة للإدارة - يمكن للإدارة رؤية جميع الأجهزة (معطلة حالياً)
-- CREATE POLICY devices_admin_policy ON devices
--     FOR ALL
--     USING (
--         -- يمكن إضافة شرط الإدارة هنا لاحقاً
--         false
--     );
