-- 🔧 إصلاح مبسط وشامل للتخزين
-- Simplified Storage Fix for Moon Memory
-- Date: 2025-01-20

-- ===== 🧹 تنظيف شامل =====

-- إلغاء تفعيل RLS نهائياً لجميع الجداول
ALTER TABLE IF EXISTS public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_sessions DISABLE ROW LEVEL SECURITY;

-- حذف جميع السياسات الموجودة
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname IN ('public', 'storage')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || 
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
END $$;

-- ===== 🔓 منح صلاحيات كاملة =====

-- صلاحيات شاملة لجميع الأدوار
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated, anon, service_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA storage TO authenticated, anon, service_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated, anon, service_role;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO authenticated, anon, service_role;
GRANT USAGE ON SCHEMA public TO authenticated, anon, service_role;
GRANT USAGE ON SCHEMA storage TO authenticated, anon, service_role;

-- ===== 📁 إعداد Storage Buckets مبسط =====

-- حذف buckets الموجودة
DELETE FROM storage.buckets WHERE id IN ('photos', 'videos');

-- إنشاء bucket الصور
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'photos', 
    'photos', 
    true, 
    52428800, -- 50MB
    ARRAY[
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'application/octet-stream'
    ]
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- إنشاء bucket الفيديوهات
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'videos', 
    'videos', 
    true, 
    104857600, -- 100MB (مخفض من 200MB)
    ARRAY[
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo',
        'video/webm',
        'video/3gpp',
        'video/x-flv',
        'application/octet-stream'
    ]
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- ===== 📊 توحيد جداول البيانات =====

-- تحديث جدول الصور ليكون موحد
ALTER TABLE public.photos 
    ADD COLUMN IF NOT EXISTS file_name TEXT,
    ADD COLUMN IF NOT EXISTS storage_path TEXT,
    ADD COLUMN IF NOT EXISTS image_url TEXT,
    ADD COLUMN IF NOT EXISTS location TEXT,
    ADD COLUMN IF NOT EXISTS username TEXT,
    ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW(),
    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- تحديث جدول الفيديوهات ليكون موحد
ALTER TABLE public.videos 
    ADD COLUMN IF NOT EXISTS file_name TEXT,
    ADD COLUMN IF NOT EXISTS storage_path TEXT,
    ADD COLUMN IF NOT EXISTS video_url TEXT,
    ADD COLUMN IF NOT EXISTS location TEXT,
    ADD COLUMN IF NOT EXISTS username TEXT,
    ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW(),
    ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- ===== 🔍 إنشاء فهارس للأداء =====

-- فهارس للصور
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON public.photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_created_at ON public.photos(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_photos_location ON public.photos(location);
CREATE INDEX IF NOT EXISTS idx_photos_file_name ON public.photos(file_name);

-- فهارس للفيديوهات
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON public.videos(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_location ON public.videos(location);
CREATE INDEX IF NOT EXISTS idx_videos_file_name ON public.videos(file_name);

-- ===== 🛠️ دوال مساعدة =====

-- دالة لتنظيف الملفات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_files()
RETURNS void AS $$
BEGIN
    -- حذف الصور الأقدم من 30 يوم بدون user_id
    DELETE FROM public.photos 
    WHERE user_id IS NULL 
    AND created_at < NOW() - INTERVAL '30 days';
    
    -- حذف الفيديوهات الأقدم من 30 يوم بدون user_id
    DELETE FROM public.videos 
    WHERE user_id IS NULL 
    AND created_at < NOW() - INTERVAL '30 days';
    
    RAISE NOTICE 'تم تنظيف الملفات القديمة';
END;
$$ LANGUAGE plpgsql;

-- دالة لإحصائيات التخزين
CREATE OR REPLACE FUNCTION get_storage_stats()
RETURNS TABLE(
    total_photos BIGINT,
    total_videos BIGINT,
    photos_size_mb NUMERIC,
    videos_size_mb NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.photos) as total_photos,
        (SELECT COUNT(*) FROM public.videos) as total_videos,
        COALESCE((SELECT SUM(metadata->>'size')::BIGINT FROM storage.objects WHERE bucket_id = 'photos'), 0) / 1024.0 / 1024.0 as photos_size_mb,
        COALESCE((SELECT SUM(metadata->>'size')::BIGINT FROM storage.objects WHERE bucket_id = 'videos'), 0) / 1024.0 / 1024.0 as videos_size_mb;
END;
$$ LANGUAGE plpgsql;

-- ===== ✅ اختبار النظام =====

-- اختبار إدراج صورة
DO $$
BEGIN
    INSERT INTO public.photos (
        user_id, 
        file_name, 
        storage_path, 
        image_url, 
        location, 
        username
    ) VALUES (
        'test-user-id',
        'test_photo.jpg',
        'uploads/test_photo.jpg',
        'https://example.com/test_photo.jpg',
        'U101',
        'مستخدم تجريبي'
    );
    
    RAISE NOTICE 'تم اختبار إدراج الصور بنجاح';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'فشل اختبار إدراج الصور: %', SQLERRM;
END $$;

-- اختبار إدراج فيديو
DO $$
BEGIN
    INSERT INTO public.videos (
        user_id, 
        file_name, 
        storage_path, 
        video_url, 
        location, 
        username
    ) VALUES (
        'test-user-id',
        'test_video.mp4',
        'uploads/test_video.mp4',
        'https://example.com/test_video.mp4',
        'C145',
        'مستخدم تجريبي'
    );
    
    RAISE NOTICE 'تم اختبار إدراج الفيديوهات بنجاح';
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'فشل اختبار إدراج الفيديوهات: %', SQLERRM;
END $$;

-- حذف البيانات التجريبية
DELETE FROM public.photos WHERE user_id = 'test-user-id';
DELETE FROM public.videos WHERE user_id = 'test-user-id';

-- ===== 📝 ملخص الإصلاح =====
DO $$
BEGIN
    RAISE NOTICE '=== تم الانتهاء من الإصلاح المبسط ===';
    RAISE NOTICE '✅ تم إلغاء RLS';
    RAISE NOTICE '✅ تم منح الصلاحيات الكاملة';
    RAISE NOTICE '✅ تم إعداد Storage Buckets';
    RAISE NOTICE '✅ تم توحيد جداول البيانات';
    RAISE NOTICE '✅ تم إنشاء الفهارس';
    RAISE NOTICE '✅ تم إنشاء الدوال المساعدة';
    RAISE NOTICE '✅ تم اختبار النظام';
    RAISE NOTICE '🚀 النظام جاهز للاستخدام';
END $$;
