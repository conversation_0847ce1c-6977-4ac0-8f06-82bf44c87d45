-- 🧪 اختبارات الأداء لاستعلامات الترتيب المحسنة
-- Performance Tests for Optimized Sorting Queries
-- Date: 2025-01-16
-- Version: 2.0

-- ===== 📊 دالة قياس أداء الاستعلامات =====
CREATE OR REPLACE FUNCTION benchmark_sorting_performance()
RETURNS TABLE (
    test_name TEXT,
    old_function_time_ms DECIMAL(10,3),
    new_function_time_ms DECIMAL(10,3),
    improvement_percent DECIMAL(5,2),
    rows_returned INTEGER,
    status TEXT
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    old_time DECIMAL(10,3);
    new_time DECIMAL(10,3);
    row_count INTEGER;
BEGIN
    -- ===== اختبار 1: ترتيب الصور حسب الموقع والتاريخ =====
    
    -- الدالة القديمة
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted('location_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    old_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    -- الدالة المحسنة
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    new_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'ترتيب الصور - موقع/تاريخ'::TEXT,
        old_time,
        new_time,
        CASE WHEN old_time > 0 THEN ROUND(((old_time - new_time) / old_time * 100), 2) ELSE 0 END,
        row_count,
        CASE WHEN new_time < old_time THEN 'تحسن ✅' ELSE 'لا تحسن ⚠️' END;
    
    -- ===== اختبار 2: ترتيب الصور حسب التاريخ والموقع =====
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted('date_location', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    old_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted_optimized('date_location', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    new_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'ترتيب الصور - تاريخ/موقع'::TEXT,
        old_time,
        new_time,
        CASE WHEN old_time > 0 THEN ROUND(((old_time - new_time) / old_time * 100), 2) ELSE 0 END,
        row_count,
        CASE WHEN new_time < old_time THEN 'تحسن ✅' ELSE 'لا تحسن ⚠️' END;
    
    -- ===== اختبار 3: ترتيب الصور حسب المستخدم والتاريخ =====
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted('user_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    old_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted_optimized('user_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    new_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'ترتيب الصور - مستخدم/تاريخ'::TEXT,
        old_time,
        new_time,
        CASE WHEN old_time > 0 THEN ROUND(((old_time - new_time) / old_time * 100), 2) ELSE 0 END,
        row_count,
        CASE WHEN new_time < old_time THEN 'تحسن ✅' ELSE 'لا تحسن ⚠️' END;
    
    -- ===== اختبار 4: ترتيب الفيديوهات حسب الموقع والتاريخ =====
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_videos_sorted('location_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    old_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_videos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 100, 0);
    end_time := clock_timestamp();
    new_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'ترتيب الفيديوهات - موقع/تاريخ'::TEXT,
        old_time,
        new_time,
        CASE WHEN old_time > 0 THEN ROUND(((old_time - new_time) / old_time * 100), 2) ELSE 0 END,
        row_count,
        CASE WHEN new_time < old_time THEN 'تحسن ✅' ELSE 'لا تحسن ⚠️' END;
    
    -- ===== اختبار 5: ترتيب مختلط للصور والفيديوهات =====
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_media_mixed_sorted('location_date', NULL, NULL, NULL, NULL, 'all', 100, 0);
    end_time := clock_timestamp();
    old_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_media_mixed_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 'all', 100, 0);
    end_time := clock_timestamp();
    new_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'ترتيب مختلط - موقع/تاريخ'::TEXT,
        old_time,
        new_time,
        CASE WHEN old_time > 0 THEN ROUND(((old_time - new_time) / old_time * 100), 2) ELSE 0 END,
        row_count,
        CASE WHEN new_time < old_time THEN 'تحسن ✅' ELSE 'لا تحسن ⚠️' END;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT
            'خطأ في الاختبار'::TEXT,
            0::DECIMAL(10,3),
            0::DECIMAL(10,3),
            0::DECIMAL(5,2),
            0::INTEGER,
            ('خطأ: ' || SQLERRM)::TEXT;
END;
$$ LANGUAGE plpgsql;

-- ===== 📈 دالة اختبار الأداء مع أحجام مختلفة =====
CREATE OR REPLACE FUNCTION benchmark_with_different_limits()
RETURNS TABLE (
    limit_size INTEGER,
    test_type TEXT,
    execution_time_ms DECIMAL(10,3),
    rows_per_second DECIMAL(10,2)
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    exec_time DECIMAL(10,3);
    limits INTEGER[] := ARRAY[10, 50, 100, 500, 1000];
    limit_val INTEGER;
    row_count INTEGER;
BEGIN
    FOREACH limit_val IN ARRAY limits
    LOOP
        -- اختبار الصور المحسنة
        start_time := clock_timestamp();
        SELECT COUNT(*) INTO row_count FROM get_photos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, limit_val, 0);
        end_time := clock_timestamp();
        exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
        
        RETURN QUERY SELECT
            limit_val,
            'صور محسنة'::TEXT,
            exec_time,
            CASE WHEN exec_time > 0 THEN ROUND((row_count::DECIMAL / exec_time * 1000), 2) ELSE 0 END;
        
        -- اختبار الفيديوهات المحسنة
        start_time := clock_timestamp();
        SELECT COUNT(*) INTO row_count FROM get_videos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, limit_val, 0);
        end_time := clock_timestamp();
        exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
        
        RETURN QUERY SELECT
            limit_val,
            'فيديوهات محسنة'::TEXT,
            exec_time,
            CASE WHEN exec_time > 0 THEN ROUND((row_count::DECIMAL / exec_time * 1000), 2) ELSE 0 END;
        
        -- اختبار مختلط محسن
        start_time := clock_timestamp();
        SELECT COUNT(*) INTO row_count FROM get_media_mixed_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 'all', limit_val, 0);
        end_time := clock_timestamp();
        exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
        
        RETURN QUERY SELECT
            limit_val,
            'مختلط محسن'::TEXT,
            exec_time,
            CASE WHEN exec_time > 0 THEN ROUND((row_count::DECIMAL / exec_time * 1000), 2) ELSE 0 END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔍 دالة اختبار استخدام الفهارس =====
CREATE OR REPLACE FUNCTION test_index_usage()
RETURNS TABLE (
    query_type TEXT,
    index_used TEXT,
    execution_plan TEXT,
    estimated_cost DECIMAL(10,2)
) AS $$
BEGIN
    -- هذه الدالة تحتاج تشغيل EXPLAIN لفحص خطط التنفيذ
    -- يتم تشغيلها يدوياً لفحص استخدام الفهارس
    
    RETURN QUERY SELECT
        'ملاحظة'::TEXT,
        'يدوي'::TEXT,
        'استخدم EXPLAIN ANALYZE للفحص التفصيلي'::TEXT,
        0::DECIMAL(10,2);
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة تقرير شامل للأداء =====
CREATE OR REPLACE FUNCTION generate_performance_report()
RETURNS TABLE (
    section TEXT,
    metric TEXT,
    value TEXT,
    status TEXT
) AS $$
DECLARE
    avg_improvement DECIMAL(5,2);
    total_tests INTEGER;
    successful_tests INTEGER;
BEGIN
    -- حساب متوسط التحسن
    SELECT 
        AVG(improvement_percent),
        COUNT(*),
        COUNT(CASE WHEN status LIKE '%✅%' THEN 1 END)
    INTO avg_improvement, total_tests, successful_tests
    FROM benchmark_sorting_performance();
    
    -- تقرير النتائج
    RETURN QUERY VALUES
        ('📊 ملخص الأداء', 'متوسط التحسن', COALESCE(avg_improvement, 0)::TEXT || '%', 
         CASE WHEN avg_improvement > 20 THEN 'ممتاز ✅' 
              WHEN avg_improvement > 10 THEN 'جيد 👍' 
              ELSE 'يحتاج تحسين ⚠️' END),
        ('📊 ملخص الأداء', 'الاختبارات الناجحة', successful_tests::TEXT || '/' || total_tests::TEXT,
         CASE WHEN successful_tests = total_tests THEN 'كامل ✅' ELSE 'جزئي ⚠️' END),
        ('📈 الفهارس', 'الفهارس المحسنة', 
         (SELECT COUNT(*)::TEXT FROM pg_indexes WHERE indexname LIKE '%_optimized'), 'نشط ✅'),
        ('🔍 قاعدة البيانات', 'حجم الصور', 
         (SELECT pg_size_pretty(pg_total_relation_size('photos'))), 'مراقب 📊'),
        ('🔍 قاعدة البيانات', 'حجم الفيديوهات', 
         (SELECT pg_size_pretty(pg_total_relation_size('videos'))), 'مراقب 📊');
END;
$$ LANGUAGE plpgsql;

-- ===== 🧪 اختبارات سريعة للتطوير =====

-- اختبار سريع للدوال المحسنة
CREATE OR REPLACE FUNCTION quick_performance_test()
RETURNS TABLE (
    test_name TEXT,
    execution_time_ms DECIMAL(10,3),
    rows_returned INTEGER,
    performance_rating TEXT
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    exec_time DECIMAL(10,3);
    row_count INTEGER;
BEGIN
    -- اختبار سريع للصور
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_photos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 50, 0);
    end_time := clock_timestamp();
    exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'صور محسنة (50 صف)'::TEXT,
        exec_time,
        row_count,
        CASE 
            WHEN exec_time < 50 THEN 'ممتاز 🚀'
            WHEN exec_time < 100 THEN 'جيد ✅'
            WHEN exec_time < 200 THEN 'مقبول ⚠️'
            ELSE 'بطيء ❌'
        END;
    
    -- اختبار سريع للفيديوهات
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_videos_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 50, 0);
    end_time := clock_timestamp();
    exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'فيديوهات محسنة (50 صف)'::TEXT,
        exec_time,
        row_count,
        CASE 
            WHEN exec_time < 50 THEN 'ممتاز 🚀'
            WHEN exec_time < 100 THEN 'جيد ✅'
            WHEN exec_time < 200 THEN 'مقبول ⚠️'
            ELSE 'بطيء ❌'
        END;
    
    -- اختبار سريع مختلط
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count FROM get_media_mixed_sorted_optimized('location_date', NULL, NULL, NULL, NULL, 'all', 50, 0);
    end_time := clock_timestamp();
    exec_time := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    
    RETURN QUERY SELECT
        'مختلط محسن (50 صف)'::TEXT,
        exec_time,
        row_count,
        CASE 
            WHEN exec_time < 100 THEN 'ممتاز 🚀'
            WHEN exec_time < 200 THEN 'جيد ✅'
            WHEN exec_time < 400 THEN 'مقبول ⚠️'
            ELSE 'بطيء ❌'
        END;
END;
$$ LANGUAGE plpgsql;

-- ===== 📋 أوامر الاختبار السريع =====

-- تشغيل اختبار سريع
-- SELECT * FROM quick_performance_test();

-- تشغيل اختبار شامل
-- SELECT * FROM benchmark_sorting_performance();

-- تشغيل تقرير الأداء
-- SELECT * FROM generate_performance_report();

-- فحص استخدام الفهارس
-- SELECT * FROM get_index_usage_stats();

-- اختبار أحجام مختلفة
-- SELECT * FROM benchmark_with_different_limits();
