-- ===== 🚀 تحسينات الأداء المتقدمة لقاعدة بيانات ذاكرة القمر =====
-- Performance Optimizations for Moon Memory Database
-- تاريخ الإنشاء: يناير 2025
-- المطور: فريق ذاكرة القمر

-- ===== 📊 فهارس محسنة للأداء =====

-- فهارس مركبة للصور
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_location_date_user 
ON public.photos (full_location_code, capture_timestamp DESC, username) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_user_date_location 
ON public.photos (username, capture_timestamp DESC, full_location_code) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_date_location_size 
ON public.photos (capture_timestamp DESC, full_location_code, file_size_bytes DESC) 
WHERE status = 'active';

-- فهارس مركبة للفيديوهات
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_location_date_user 
ON public.videos (full_location_code, capture_timestamp DESC, username) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_user_date_location 
ON public.videos (username, capture_timestamp DESC, full_location_code) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_date_location_size 
ON public.videos (capture_timestamp DESC, full_location_code, file_size_bytes DESC) 
WHERE status = 'active';

-- فهارس للبحث النصي
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_filename_gin 
ON public.photos USING gin (to_tsvector('arabic', file_name)) 
WHERE status = 'active';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_filename_gin 
ON public.videos USING gin (to_tsvector('arabic', file_name)) 
WHERE status = 'active';

-- فهارس للأجهزة والأمان
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_devices_fingerprint_trust 
ON public.devices (device_fingerprint, trust_level, is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_devices_user_active 
ON public.devices (username, is_active, last_seen DESC);

-- فهارس للجلسات
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_expires 
ON public.user_sessions (username, expires_at DESC) 
WHERE is_active = true;

-- ===== 🔧 إعدادات تحسين PostgreSQL =====

-- تحسين إعدادات الذاكرة (يجب تطبيقها في postgresql.conf)
/*
shared_buffers = 256MB                    # 25% من الذاكرة المتاحة
effective_cache_size = 1GB               # 75% من الذاكرة المتاحة
work_mem = 4MB                           # ذاكرة العمليات
maintenance_work_mem = 64MB              # ذاكرة الصيانة
checkpoint_completion_target = 0.9       # تحسين نقاط التفتيش
wal_buffers = 16MB                       # ذاكرة WAL
default_statistics_target = 100          # إحصائيات محسنة
random_page_cost = 1.1                   # تحسين للـ SSD
effective_io_concurrency = 200           # تحسين I/O للـ SSD
*/

-- ===== 📈 Views محسنة للاستعلامات المتكررة =====

-- View للوسائط النشطة مع معلومات كاملة
CREATE OR REPLACE VIEW v_active_media AS
SELECT 
    'photo' as media_type,
    id,
    file_name,
    username,
    full_location_code,
    capture_timestamp,
    file_size_bytes,
    storage_path,
    processed_image_url,
    thumbnail_url,
    created_at,
    updated_at
FROM public.photos 
WHERE status = 'active'

UNION ALL

SELECT 
    'video' as media_type,
    id,
    file_name,
    username,
    full_location_code,
    capture_timestamp,
    file_size_bytes,
    storage_path,
    processed_video_url,
    thumbnail_url,
    created_at,
    updated_at
FROM public.videos 
WHERE status = 'active';

-- View للإحصائيات السريعة
CREATE OR REPLACE VIEW v_quick_stats AS
SELECT 
    'photos' as category,
    COUNT(*) as total_count,
    COUNT(DISTINCT username) as unique_users,
    COUNT(DISTINCT full_location_code) as unique_locations,
    SUM(file_size_bytes) as total_size_bytes,
    AVG(file_size_bytes) as avg_size_bytes,
    MIN(capture_timestamp) as earliest_date,
    MAX(capture_timestamp) as latest_date
FROM public.photos 
WHERE status = 'active'

UNION ALL

SELECT 
    'videos' as category,
    COUNT(*) as total_count,
    COUNT(DISTINCT username) as unique_users,
    COUNT(DISTINCT full_location_code) as unique_locations,
    SUM(file_size_bytes) as total_size_bytes,
    AVG(file_size_bytes) as avg_size_bytes,
    MIN(capture_timestamp) as earliest_date,
    MAX(capture_timestamp) as latest_date
FROM public.videos 
WHERE status = 'active';

-- View لأحدث النشاطات
CREATE OR REPLACE VIEW v_recent_activity AS
SELECT 
    'photo_upload' as activity_type,
    username,
    full_location_code,
    file_name,
    capture_timestamp as activity_time,
    file_size_bytes
FROM public.photos 
WHERE status = 'active' 
  AND capture_timestamp >= NOW() - INTERVAL '7 days'

UNION ALL

SELECT 
    'video_upload' as activity_type,
    username,
    full_location_code,
    file_name,
    capture_timestamp as activity_time,
    file_size_bytes
FROM public.videos 
WHERE status = 'active' 
  AND capture_timestamp >= NOW() - INTERVAL '7 days'

ORDER BY activity_time DESC
LIMIT 100;

-- ===== 🔄 دوال تحسين الأداء التلقائي =====

-- دالة تحديث الإحصائيات التلقائية
CREATE OR REPLACE FUNCTION auto_update_statistics()
RETURNS void AS $$
BEGIN
    -- تحديث إحصائيات الجداول الرئيسية
    ANALYZE public.photos;
    ANALYZE public.videos;
    ANALYZE public.users;
    ANALYZE public.devices;
    ANALYZE public.locations;
    ANALYZE public.user_sessions;
    
    -- تسجيل وقت آخر تحديث
    INSERT INTO public.admin_logs (action, details, created_at)
    VALUES ('auto_statistics_update', 'تم تحديث إحصائيات قاعدة البيانات تلقائياً', NOW());
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف الجلسات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_sessions 
    WHERE expires_at < NOW() - INTERVAL '1 day';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- تسجيل العملية
    INSERT INTO public.admin_logs (action, details, created_at)
    VALUES ('cleanup_sessions', 
            format('تم حذف %s جلسة منتهية الصلاحية', deleted_count), 
            NOW());
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- دالة تحسين الفهارس التلقائية
CREATE OR REPLACE FUNCTION auto_reindex_tables()
RETURNS TABLE (
    table_name TEXT,
    index_name TEXT,
    status TEXT
) AS $$
DECLARE
    table_rec RECORD;
    index_rec RECORD;
BEGIN
    -- إعادة بناء فهارس الجداول الرئيسية
    FOR table_rec IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
          AND tablename IN ('photos', 'videos', 'users', 'devices')
    LOOP
        FOR index_rec IN
            SELECT indexname
            FROM pg_indexes
            WHERE schemaname = table_rec.schemaname
              AND tablename = table_rec.tablename
        LOOP
            BEGIN
                EXECUTE format('REINDEX INDEX CONCURRENTLY %I.%I', 
                              table_rec.schemaname, index_rec.indexname);
                
                RETURN QUERY SELECT 
                    table_rec.tablename,
                    index_rec.indexname,
                    'SUCCESS'::TEXT;
                    
            EXCEPTION WHEN OTHERS THEN
                RETURN QUERY SELECT 
                    table_rec.tablename,
                    index_rec.indexname,
                    'ERROR: ' || SQLERRM;
            END;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ===== ⏰ مهام الصيانة المجدولة =====

-- إنشاء جدول لمهام الصيانة المجدولة
CREATE TABLE IF NOT EXISTS public.maintenance_schedule (
    id SERIAL PRIMARY KEY,
    task_name TEXT NOT NULL,
    task_function TEXT NOT NULL,
    schedule_cron TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_run TIMESTAMP WITH TIME ZONE,
    next_run TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج مهام الصيانة الافتراضية
INSERT INTO public.maintenance_schedule (task_name, task_function, schedule_cron) VALUES
('تحديث الإحصائيات', 'auto_update_statistics()', '0 2 * * *'),  -- يومياً في 2 صباحاً
('تنظيف الجلسات', 'cleanup_expired_sessions()', '0 3 * * *'),   -- يومياً في 3 صباحاً
('تحسين الفهارس', 'auto_reindex_tables()', '0 4 * * 0'),        -- أسبوعياً يوم الأحد في 4 صباحاً
('تنظيف البيانات المحذوفة', 'cleanup_deleted_records()', '0 5 * * 0') -- أسبوعياً يوم الأحد في 5 صباحاً
ON CONFLICT DO NOTHING;

-- ===== 📊 مراقبة الأداء =====

-- جدول لتتبع أداء الاستعلامات
CREATE TABLE IF NOT EXISTS public.query_performance_log (
    id SERIAL PRIMARY KEY,
    query_name TEXT NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    rows_returned INTEGER,
    parameters JSONB,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- دالة تسجيل أداء الاستعلامات
CREATE OR REPLACE FUNCTION log_query_performance(
    p_query_name TEXT,
    p_execution_time_ms INTEGER,
    p_rows_returned INTEGER DEFAULT NULL,
    p_parameters JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    INSERT INTO public.query_performance_log 
    (query_name, execution_time_ms, rows_returned, parameters)
    VALUES (p_query_name, p_execution_time_ms, p_rows_returned, p_parameters);
    
    -- الاحتفاظ بآخر 1000 سجل فقط
    DELETE FROM public.query_performance_log 
    WHERE id NOT IN (
        SELECT id FROM public.query_performance_log 
        ORDER BY executed_at DESC 
        LIMIT 1000
    );
END;
$$ LANGUAGE plpgsql;

-- ===== 🎯 ملخص التحسينات =====
/*
🚀 تحسينات الأداء المطبقة:

1. فهارس مركبة محسنة:
   - فهارس للترتيب المختلط
   - فهارس للبحث النصي
   - فهارس للأمان والأجهزة

2. Views محسنة:
   - عرض الوسائط النشطة
   - إحصائيات سريعة
   - النشاطات الأخيرة

3. دوال الصيانة التلقائية:
   - تحديث الإحصائيات
   - تنظيف الجلسات
   - تحسين الفهارس

4. مراقبة الأداء:
   - تتبع أداء الاستعلامات
   - جدولة مهام الصيانة
   - تسجيل العمليات

📈 النتائج المتوقعة:
- تحسين سرعة الاستعلامات بنسبة 60-80%
- تقليل استخدام الذاكرة
- صيانة تلقائية للنظام
- مراقبة مستمرة للأداء

⚠️ ملاحظات مهمة:
- تطبيق الفهارس بـ CONCURRENTLY لتجنب الحظر
- مراقبة استخدام مساحة التخزين
- تحديث إعدادات PostgreSQL حسب الحاجة
- مراجعة دورية لأداء الاستعلامات
*/
