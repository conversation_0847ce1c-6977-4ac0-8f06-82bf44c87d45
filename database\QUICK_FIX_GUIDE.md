# 🚨 دليل الإصلاح السريع لمشكلة السياسات
## Quick Fix Guide for Policy Errors

**المشكلة:** `ERROR: 42710: policy "videos_upload_policy" for table "objects" already exists`

---

## 🔥 **الحل السريع (الموصى به):**

### **الخطوة 1: الإصلاح الطارئ**
```sql
-- تشغيل هذا الملف أولاً لحذف جميع السياسات
\i database/emergency_fix.sql
```

### **الخطوة 2: الإصلاح الشامل**
```sql
-- ثم تشغيل هذا الملف لإعادة إنشاء السياسات بشكل صحيح
\i database/complete_safe_fix.sql
```

---

## 🛠️ **البديل: الإصلاح اليدوي**

### **1. حذف السياسات المتضاربة:**
```sql
-- حذف السياسات المتضاربة واحدة تلو الأخرى
DROP POLICY IF EXISTS "videos_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "videos_view_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_view_policy" ON storage.objects;
```

### **2. إعادة إنشاء السياسات:**
```sql
-- إنشاء سياسات جديدة بأسماء مختلفة
CREATE POLICY "videos_storage_upload_new" ON storage.objects
    FOR INSERT
    WITH CHECK (bucket_id = 'videos' AND auth.uid() IS NOT NULL);

CREATE POLICY "photos_storage_upload_new" ON storage.objects
    FOR INSERT
    WITH CHECK (bucket_id = 'photos' AND auth.uid() IS NOT NULL);
```

---

## 🔍 **فحص السياسات الموجودة:**

### **لعرض جميع السياسات:**
```sql
-- عرض سياسات الجداول
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname IN ('public', 'storage')
ORDER BY schemaname, tablename, policyname;

-- عرض سياسات Storage فقط
SELECT policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects';
```

---

## ⚡ **الحل الأسرع (للاختبار فقط):**

### **إلغاء تفعيل RLS مؤقتاً:**
```sql
-- إلغاء تفعيل RLS على جميع الجداول
ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices DISABLE ROW LEVEL SECURITY;

-- منح صلاحيات كاملة
GRANT ALL ON public.photos TO authenticated;
GRANT ALL ON public.videos TO authenticated;
GRANT ALL ON public.users TO authenticated;
GRANT ALL ON public.devices TO authenticated;
```

**⚠️ تحذير:** هذا الحل للاختبار فقط وليس آمن للإنتاج!

---

## 🎯 **التوصية:**

### **للاستخدام الفوري:**
1. **شغل** `emergency_fix.sql` لحذف جميع السياسات
2. **شغل** `complete_safe_fix.sql` لإعادة إنشاءها بشكل صحيح
3. **اختبر** التطبيق للتأكد من عمله

### **للتأكد من النجاح:**
```sql
-- فحص أن السياسات تم إنشاؤها بنجاح
SELECT COUNT(*) as policy_count 
FROM pg_policies 
WHERE schemaname IN ('public', 'storage');
```

---

## 📞 **في حالة استمرار المشكلة:**

### **الحل الجذري:**
```sql
-- حذف جميع السياسات بالقوة
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN SELECT schemaname, tablename, policyname 
             FROM pg_policies 
             WHERE schemaname IN ('public', 'storage')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(r.policyname) || 
                ' ON ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename);
    END LOOP;
END $$;
```

---

## ✅ **التحقق من النجاح:**

بعد تطبيق الإصلاح، يجب أن ترى:
- ✅ لا توجد أخطاء في السياسات
- ✅ التطبيق يعمل بدون مشاكل
- ✅ يمكن رفع الصور والفيديوهات
- ✅ يمكن الوصول لقاعدة البيانات

---

**📝 ملاحظة:** استخدم `emergency_fix.sql` ثم `complete_safe_fix.sql` للحصول على أفضل النتائج.
