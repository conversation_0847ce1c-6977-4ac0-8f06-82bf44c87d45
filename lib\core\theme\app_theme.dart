import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static TextStyle get arabicText => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.normal,
      );

  static TextStyle get arabicTextBold => GoogleFonts.cairo(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get arabicTextLarge => GoogleFonts.cairo(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get arabicTextSmall => GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.normal,
      );

  static TextTheme get textTheme => TextTheme(
        displayLarge: arabicTextLarge,
        displayMedium: arabicTextBold,
        displaySmall: arabicText,
        bodyLarge: arabicText,
        bodyMedium: arabicText,
        bodySmall: arabicTextSmall,
        labelLarge: arabicText,
        labelMedium: arabicText,
        labelSmall: arabicTextSmall,
      );

  static ThemeData get theme => ThemeData(
        textTheme: textTheme,
        primarySwatch: Colors.blue,
        scaffoldBackgroundColor: Colors.white,
      );
}
