-- ===== 🔧 الإصلاح الشامل لمشكلة الجدولين المزدوجين =====
-- Comprehensive Fix for Dual Tables Issue
-- تاريخ الإنشاء: 2025-07-20
-- الهدف: حل مشاكل التزامن بين auth.users و public.users

-- ===== 📋 تحليل المشكلة =====
/*
المشاكل المكتشفة:
1. تضارب في أسماء الحقول (url vs image_url vs video_url)
2. عدم تزامن البيانات بين auth.users و public.users
3. تعقيد في إنشاء المستخدمين (خطوتين منفصلتين)
4. مشاكل في إدارة الجلسات
5. عدم وجود دوال مساعدة للتزامن

الحل المختار: الإبقاء على الجدولين مع تحسين التزامن
*/

-- ===== 🛠️ المرحلة 1: توحيد أسماء الحقول =====

-- 1.1 توحيد حقول جدول الصور
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS image_url_unified TEXT;

-- نسخ البيانات من الحقول المختلفة إلى الحقل الموحد
UPDATE public.photos 
SET image_url_unified = COALESCE(image_url, url)
WHERE image_url_unified IS NULL;

-- 1.2 توحيد حقول جدول الفيديوهات  
ALTER TABLE public.videos 
ADD COLUMN IF NOT EXISTS video_url_unified TEXT;

-- نسخ البيانات من الحقول المختلفة إلى الحقل الموحد
UPDATE public.videos 
SET video_url_unified = COALESCE(video_url, url)
WHERE video_url_unified IS NULL;

-- 1.3 توحيد حقول التاريخ والوقت
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS timestamp_unified TIMESTAMP WITH TIME ZONE;

UPDATE public.photos 
SET timestamp_unified = COALESCE(created_at, date_time, capture_timestamp)
WHERE timestamp_unified IS NULL;

ALTER TABLE public.videos 
ADD COLUMN IF NOT EXISTS timestamp_unified TIMESTAMP WITH TIME ZONE;

UPDATE public.videos 
SET timestamp_unified = COALESCE(created_at, date_time, capture_timestamp)
WHERE timestamp_unified IS NULL;

-- ===== 🔄 المرحلة 2: دوال التزامن التلقائي =====

-- 2.1 دالة تزامن بيانات المستخدم من auth إلى public
CREATE OR REPLACE FUNCTION sync_auth_to_public_user()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث البيانات في public.users عند تغيير auth.users
    UPDATE public.users 
    SET 
        email = NEW.email,
        updated_at = NOW(),
        last_login = CASE 
            WHEN NEW.last_sign_in_at IS NOT NULL AND 
                 NEW.last_sign_in_at != OLD.last_sign_in_at 
            THEN NEW.last_sign_in_at 
            ELSE last_login 
        END
    WHERE id = NEW.id;
    
    -- إنشاء سجل في public.users إذا لم يكن موجوداً
    IF NOT FOUND THEN
        INSERT INTO public.users (
            id, 
            email, 
            full_name,
            national_id,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'full_name', 'مستخدم جديد'),
            COALESCE(NEW.raw_user_meta_data->>'national_id', NEW.email),
            true,
            NEW.created_at,
            NOW()
        ) ON CONFLICT (id) DO NOTHING;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2.2 إنشاء trigger للتزامن التلقائي
DROP TRIGGER IF EXISTS trigger_sync_auth_to_public ON auth.users;
CREATE TRIGGER trigger_sync_auth_to_public
    AFTER INSERT OR UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION sync_auth_to_public_user();

-- ===== 👥 المرحلة 3: دوال إنشاء المستخدمين المحسنة =====

-- 3.1 دالة إنشاء مستخدم كامل (للاستخدام من تطبيق الإدارة)
CREATE OR REPLACE FUNCTION create_complete_user(
    p_national_id TEXT,
    p_full_name TEXT,
    p_password TEXT DEFAULT NULL,
    p_email TEXT DEFAULT NULL,
    p_department TEXT DEFAULT NULL,
    p_position TEXT DEFAULT NULL,
    p_is_admin BOOLEAN DEFAULT FALSE
)
RETURNS JSON AS $$
DECLARE
    generated_email TEXT;
    generated_password TEXT;
    new_user_id UUID;
    result JSON;
BEGIN
    -- إنشاء البريد الإلكتروني والرقم السري
    generated_email := COALESCE(p_email, p_national_id || '@moon-memory.com');
    generated_password := COALESCE(p_password, p_national_id);
    
    -- إنشاء UUID جديد
    new_user_id := gen_random_uuid();
    
    -- إدراج في public.users مباشرة (سيتم ربطه بـ auth.users لاحقاً)
    INSERT INTO public.users (
        id,
        national_id,
        full_name,
        email,
        department,
        position,
        is_active,
        is_admin,
        account_type,
        max_devices,
        storage_quota_mb,
        created_at,
        updated_at
    ) VALUES (
        new_user_id,
        p_national_id,
        p_full_name,
        generated_email,
        p_department,
        p_position,
        true,
        p_is_admin,
        CASE WHEN p_is_admin THEN 'admin' ELSE 'user' END,
        CASE WHEN p_is_admin THEN 10 ELSE 1 END,
        CASE WHEN p_is_admin THEN 10000 ELSE 1000 END,
        NOW(),
        NOW()
    );
    
    -- إرجاع النتيجة
    result := json_build_object(
        'success', true,
        'user_id', new_user_id,
        'email', generated_email,
        'password', generated_password,
        'message', 'تم إنشاء المستخدم بنجاح في قاعدة البيانات. يجب إنشاؤه في Auth باستخدام Admin API.'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في إنشاء المستخدم'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3.2 دالة التحقق من تطابق البيانات بين الجدولين
CREATE OR REPLACE FUNCTION check_user_data_sync()
RETURNS TABLE(
    user_id UUID,
    auth_email TEXT,
    public_email TEXT,
    sync_status TEXT,
    last_auth_login TIMESTAMP WITH TIME ZONE,
    last_public_login TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        au.id as user_id,
        au.email as auth_email,
        pu.email as public_email,
        CASE
            WHEN pu.id IS NULL THEN 'مفقود في public.users'
            WHEN au.email != pu.email THEN 'عدم تطابق البريد الإلكتروني'
            ELSE 'متزامن'
        END as sync_status,
        au.last_sign_in_at as last_auth_login,
        pu.last_login as last_public_login
    FROM auth.users au
    LEFT JOIN public.users pu ON au.id = pu.id
    WHERE au.email IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3.3 دالة إصلاح البيانات غير المتزامنة
CREATE OR REPLACE FUNCTION fix_unsync_users()
RETURNS JSON AS $$
DECLARE
    fixed_count INTEGER := 0;
    error_count INTEGER := 0;
    user_record RECORD;
    result JSON;
BEGIN
    -- إصلاح المستخدمين المفقودين في public.users
    FOR user_record IN
        SELECT au.id, au.email, au.raw_user_meta_data, au.created_at
        FROM auth.users au
        LEFT JOIN public.users pu ON au.id = pu.id
        WHERE pu.id IS NULL AND au.email IS NOT NULL
    LOOP
        BEGIN
            INSERT INTO public.users (
                id,
                email,
                full_name,
                national_id,
                is_active,
                created_at,
                updated_at
            ) VALUES (
                user_record.id,
                user_record.email,
                COALESCE(user_record.raw_user_meta_data->>'full_name', 'مستخدم مستورد'),
                COALESCE(user_record.raw_user_meta_data->>'national_id',
                         REPLACE(user_record.email, '@moon-memory.com', '')),
                true,
                user_record.created_at,
                NOW()
            );
            fixed_count := fixed_count + 1;
        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
        END;
    END LOOP;

    result := json_build_object(
        'success', true,
        'fixed_users', fixed_count,
        'errors', error_count,
        'message', format('تم إصلاح %s مستخدم، %s أخطاء', fixed_count, error_count)
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 📸 المرحلة 4: دوال تحسين رفع الملفات =====

-- 4.1 دالة رفع صورة محسنة
CREATE OR REPLACE FUNCTION upload_photo_enhanced(
    p_user_id UUID,
    p_file_name TEXT,
    p_image_url TEXT,
    p_storage_path TEXT,
    p_location TEXT,
    p_username TEXT DEFAULT NULL,
    p_file_size_bytes BIGINT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    location_data JSON;
    new_photo_id UUID;
    result JSON;
BEGIN
    -- تحليل كود الموقع
    location_data := json_build_object(
        'type', CASE
            WHEN p_location ~ '^[UC][0-9]+$' THEN LEFT(p_location, 1)
            ELSE NULL
        END,
        'number', CASE
            WHEN p_location ~ '^[UC][0-9]+$' THEN SUBSTRING(p_location FROM 2)
            ELSE NULL
        END
    );

    -- إدراج الصورة
    INSERT INTO public.photos (
        user_id,
        file_name,
        image_url,
        image_url_unified,  -- استخدام الحقل الموحد
        storage_path,
        location,
        location_type,
        location_number,
        full_location_code,
        username,
        file_size_bytes,
        timestamp_unified,  -- استخدام الحقل الموحد
        capture_timestamp,
        upload_timestamp,
        upload_status,
        status,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        p_file_name,
        p_image_url,
        p_image_url,  -- نفس القيمة للحقل الموحد
        p_storage_path,
        p_location,
        location_data->>'type',
        location_data->>'number',
        CASE
            WHEN location_data->>'type' IS NOT NULL AND location_data->>'number' IS NOT NULL
            THEN (location_data->>'type') || (location_data->>'number')
            ELSE NULL
        END,
        COALESCE(p_username, 'مجهول'),
        p_file_size_bytes,
        NOW(),  -- timestamp_unified
        NOW(),  -- capture_timestamp
        NOW(),  -- upload_timestamp
        'uploaded',
        'active',
        NOW(),
        NOW()
    ) RETURNING id INTO new_photo_id;

    result := json_build_object(
        'success', true,
        'photo_id', new_photo_id,
        'message', 'تم رفع الصورة بنجاح'
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في رفع الصورة'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4.2 دالة رفع فيديو محسنة
CREATE OR REPLACE FUNCTION upload_video_enhanced(
    p_user_id UUID,
    p_file_name TEXT,
    p_video_url TEXT,
    p_storage_path TEXT,
    p_location TEXT,
    p_username TEXT DEFAULT NULL,
    p_file_size_bytes BIGINT DEFAULT NULL,
    p_duration_seconds INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    location_data JSON;
    new_video_id UUID;
    result JSON;
BEGIN
    -- تحليل كود الموقع
    location_data := json_build_object(
        'type', CASE
            WHEN p_location ~ '^[UC][0-9]+$' THEN LEFT(p_location, 1)
            ELSE NULL
        END,
        'number', CASE
            WHEN p_location ~ '^[UC][0-9]+$' THEN SUBSTRING(p_location FROM 2)
            ELSE NULL
        END
    );

    -- إدراج الفيديو
    INSERT INTO public.videos (
        user_id,
        file_name,
        video_url,
        video_url_unified,  -- استخدام الحقل الموحد
        storage_path,
        location,
        location_type,
        location_number,
        full_location_code,
        username,
        file_size_bytes,
        duration_seconds,
        timestamp_unified,  -- استخدام الحقل الموحد
        capture_timestamp,
        upload_timestamp,
        upload_status,
        status,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        p_file_name,
        p_video_url,
        p_video_url,  -- نفس القيمة للحقل الموحد
        p_storage_path,
        p_location,
        location_data->>'type',
        location_data->>'number',
        CASE
            WHEN location_data->>'type' IS NOT NULL AND location_data->>'number' IS NOT NULL
            THEN (location_data->>'type') || (location_data->>'number')
            ELSE NULL
        END,
        COALESCE(p_username, 'مجهول'),
        p_file_size_bytes,
        p_duration_seconds,
        NOW(),  -- timestamp_unified
        NOW(),  -- capture_timestamp
        NOW(),  -- upload_timestamp
        'uploaded',
        'active',
        NOW(),
        NOW()
    ) RETURNING id INTO new_video_id;

    result := json_build_object(
        'success', true,
        'video_id', new_video_id,
        'message', 'تم رفع الفيديو بنجاح'
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في رفع الفيديو'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 🔍 المرحلة 5: دوال التحقق والمراقبة =====

-- 5.1 دالة فحص صحة قاعدة البيانات
CREATE OR REPLACE FUNCTION database_health_check()
RETURNS JSON AS $$
DECLARE
    auth_users_count INTEGER;
    public_users_count INTEGER;
    photos_count INTEGER;
    videos_count INTEGER;
    sync_issues INTEGER;
    result JSON;
BEGIN
    -- عد المستخدمين
    SELECT COUNT(*) INTO auth_users_count FROM auth.users;
    SELECT COUNT(*) INTO public_users_count FROM public.users;

    -- عد الملفات
    SELECT COUNT(*) INTO photos_count FROM public.photos;
    SELECT COUNT(*) INTO videos_count FROM public.videos;

    -- فحص مشاكل التزامن
    SELECT COUNT(*) INTO sync_issues
    FROM auth.users au
    LEFT JOIN public.users pu ON au.id = pu.id
    WHERE pu.id IS NULL;

    result := json_build_object(
        'auth_users', auth_users_count,
        'public_users', public_users_count,
        'photos', photos_count,
        'videos', videos_count,
        'sync_issues', sync_issues,
        'sync_percentage', CASE
            WHEN auth_users_count > 0
            THEN ROUND(((auth_users_count - sync_issues)::DECIMAL / auth_users_count) * 100, 2)
            ELSE 100
        END,
        'status', CASE
            WHEN sync_issues = 0 THEN 'ممتاز'
            WHEN sync_issues <= 2 THEN 'جيد'
            ELSE 'يحتاج إصلاح'
        END,
        'timestamp', NOW()
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.2 دالة تنظيف البيانات المكررة
CREATE OR REPLACE FUNCTION cleanup_duplicate_data()
RETURNS JSON AS $$
DECLARE
    deleted_photos INTEGER := 0;
    deleted_videos INTEGER := 0;
    result JSON;
BEGIN
    -- حذف الصور المكررة (نفس المستخدم ونفس اسم الملف)
    WITH duplicates AS (
        SELECT id, ROW_NUMBER() OVER (
            PARTITION BY user_id, file_name
            ORDER BY created_at DESC
        ) as rn
        FROM public.photos
    )
    DELETE FROM public.photos
    WHERE id IN (
        SELECT id FROM duplicates WHERE rn > 1
    );

    GET DIAGNOSTICS deleted_photos = ROW_COUNT;

    -- حذف الفيديوهات المكررة
    WITH duplicates AS (
        SELECT id, ROW_NUMBER() OVER (
            PARTITION BY user_id, file_name
            ORDER BY created_at DESC
        ) as rn
        FROM public.videos
    )
    DELETE FROM public.videos
    WHERE id IN (
        SELECT id FROM duplicates WHERE rn > 1
    );

    GET DIAGNOSTICS deleted_videos = ROW_COUNT;

    result := json_build_object(
        'success', true,
        'deleted_photos', deleted_photos,
        'deleted_videos', deleted_videos,
        'message', format('تم حذف %s صورة مكررة و %s فيديو مكرر', deleted_photos, deleted_videos)
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
