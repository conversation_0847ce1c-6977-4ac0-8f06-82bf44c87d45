{"java.configuration.updateBuildConfiguration": "automatic", "java.import.gradle.enabled": true, "java.import.gradle.wrapper.enabled": true, "java.import.gradle.java.home": null, "java.compile.nullAnalysis.mode": "automatic", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.gradle": true}, "cmake.ignoreCMakeListsMissing": true}