import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService extends StateNotifier<Locale> {
  final SharedPreferences _prefs;
  static const String _languageKey = 'language';

  LanguageService(this._prefs) : super(const Locale('ar')) {
    final savedLanguage = _prefs.getString(_languageKey);
    if (savedLanguage != null) {
      state = Locale(savedLanguage);
    }
  }

  Future<void> setLanguage(String languageCode) async {
    await _prefs.setString(_languageKey, languageCode);
    state = Locale(languageCode);
  }

  String get currentLanguage => state.languageCode;
}

final sharedPreferencesProvider = FutureProvider<SharedPreferences>((ref) {
  return SharedPreferences.getInstance();
});

final languageServiceProvider = StateNotifierProvider<LanguageService, Locale>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider).value;
  if (prefs == null) {
    throw Exception('SharedPreferences not initialized');
  }
  return LanguageService(prefs);
});
