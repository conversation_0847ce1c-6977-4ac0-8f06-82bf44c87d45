-- 🔒 نظام البصمة الرقمية للأجهزة
-- Device Fingerprint Security System
-- Date: 2025-01-15

-- ===== 🔧 دالة فحص وتسجيل الجهاز =====
CREATE OR REPLACE FUNCTION check_and_register_device(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL,
    p_hardware_info TEXT DEFAULT NULL,
    p_build_fingerprint TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_device_id UUID;
    v_user_device_count INTEGER;
    v_max_devices INTEGER;
    v_existing_device_user_id UUID;
    v_result JSON;
BEGIN
    -- فحص إذا كان الجهاز مسجل لمستخدم آخر
    SELECT user_id INTO v_existing_device_user_id
    FROM public.devices 
    WHERE device_fingerprint = p_device_fingerprint 
    AND user_id != p_user_id
    LIMIT 1;
    
    -- إذا كان الجهاز مسجل لمستخدم آخر
    IF v_existing_device_user_id IS NOT NULL THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'DEVICE_ALREADY_REGISTERED',
            'message', 'هذا الجهاز مسجل لمستخدم آخر',
            'message_en', 'This device is already registered to another user'
        );
    END IF;
    
    -- فحص إذا كان الجهاز موجود للمستخدم الحالي
    SELECT id INTO v_device_id
    FROM public.devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    -- إذا كان الجهاز موجود، تحديث المعلومات
    IF v_device_id IS NOT NULL THEN
        UPDATE public.devices
        SET
            last_login = NOW(),
            last_verified_at = NOW(),
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            hardware_info = COALESCE(p_hardware_info, hardware_info),
            build_fingerprint = COALESCE(p_build_fingerprint, build_fingerprint),
            trust_level = 'high',
            is_active = true,
            updated_at = NOW()
        WHERE id = v_device_id;
        
        RETURN json_build_object(
            'success', true,
            'device_id', v_device_id,
            'status', 'existing_device_updated',
            'message', 'تم تحديث معلومات الجهاز المسجل',
            'message_en', 'Existing device information updated'
        );
    END IF;
    
    -- فحص عدد الأجهزة المسموح بها للمستخدم
    SELECT max_devices INTO v_max_devices
    FROM public.users 
    WHERE id = p_user_id;
    
    SELECT COUNT(*) INTO v_user_device_count
    FROM public.devices 
    WHERE user_id = p_user_id 
    AND is_active = true;
    
    -- إذا تجاوز المستخدم الحد المسموح
    IF v_user_device_count >= COALESCE(v_max_devices, 3) THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'MAX_DEVICES_EXCEEDED',
            'message', 'تم تجاوز الحد الأقصى للأجهزة المسموح بها',
            'message_en', 'Maximum number of devices exceeded',
            'max_devices', v_max_devices,
            'current_devices', v_user_device_count
        );
    END IF;
    
    -- تسجيل جهاز جديد
    INSERT INTO public.devices (
        user_id,
        device_fingerprint,
        android_id,
        device_name,
        device_model,
        device_brand,
        hardware_info,
        build_fingerprint,
        confidence_score,
        trust_level,
        is_active,
        last_verified_at,
        last_login
    ) VALUES (
        p_user_id,
        p_device_fingerprint,
        p_android_id,
        p_device_name,
        p_device_model,
        p_device_brand,
        p_hardware_info,
        p_build_fingerprint,
        85.0,
        'medium',
        true,
        NOW(),
        NOW()
    ) RETURNING id INTO v_device_id;
    
    RETURN json_build_object(
        'success', true,
        'device_id', v_device_id,
        'status', 'new_device_registered',
        'message', 'تم تسجيل الجهاز الجديد بنجاح',
        'message_en', 'New device registered successfully'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error_code', 'SYSTEM_ERROR',
        'message', 'خطأ في النظام: ' || SQLERRM,
        'message_en', 'System error: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 🔍 دالة فحص صلاحية الجهاز للدخول =====
CREATE OR REPLACE FUNCTION verify_device_access(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT
)
RETURNS JSON AS $$
DECLARE
    v_device_record RECORD;
    v_result JSON;
BEGIN
    -- البحث عن الجهاز
    SELECT 
        id,
        user_id,
        trust_level,
        is_active,
        is_blocked,
        blocked_until,
        auth_attempts
    INTO v_device_record
    FROM public.devices 
    WHERE (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    -- إذا لم يتم العثور على الجهاز
    IF v_device_record.id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'DEVICE_NOT_FOUND',
            'message', 'هذا الجهاز غير مسجل في النظام',
            'message_en', 'This device is not registered in the system'
        );
    END IF;
    
    -- إذا كان الجهاز مسجل لمستخدم آخر
    IF v_device_record.user_id != p_user_id THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'DEVICE_BELONGS_TO_OTHER_USER',
            'message', 'هذا الجهاز مسجل لمستخدم آخر',
            'message_en', 'This device belongs to another user'
        );
    END IF;
    
    -- إذا كان الجهاز محظور
    IF v_device_record.is_blocked = true THEN
        IF v_device_record.blocked_until IS NOT NULL AND v_device_record.blocked_until > NOW() THEN
            RETURN json_build_object(
                'success', false,
                'error_code', 'DEVICE_TEMPORARILY_BLOCKED',
                'message', 'الجهاز محظور مؤقتاً',
                'message_en', 'Device is temporarily blocked',
                'blocked_until', v_device_record.blocked_until
            );
        ELSIF v_device_record.blocked_until IS NULL THEN
            RETURN json_build_object(
                'success', false,
                'error_code', 'DEVICE_PERMANENTLY_BLOCKED',
                'message', 'الجهاز محظور نهائياً',
                'message_en', 'Device is permanently blocked'
            );
        END IF;
    END IF;
    
    -- إذا كان الجهاز غير نشط
    IF v_device_record.is_active = false THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'DEVICE_INACTIVE',
            'message', 'الجهاز غير نشط',
            'message_en', 'Device is inactive'
        );
    END IF;
    
    -- إذا كان مستوى الثقة منخفض جداً
    IF v_device_record.trust_level = 'blocked' THEN
        RETURN json_build_object(
            'success', false,
            'error_code', 'DEVICE_TRUST_LEVEL_BLOCKED',
            'message', 'مستوى ثقة الجهاز منخفض جداً',
            'message_en', 'Device trust level is too low'
        );
    END IF;
    
    -- تحديث آخر دخول
    UPDATE public.devices 
    SET 
        last_login = NOW(),
        last_verified_at = NOW(),
        auth_attempts = 0,
        updated_at = NOW()
    WHERE id = v_device_record.id;
    
    RETURN json_build_object(
        'success', true,
        'device_id', v_device_record.id,
        'trust_level', v_device_record.trust_level,
        'message', 'تم التحقق من الجهاز بنجاح',
        'message_en', 'Device verified successfully'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error_code', 'SYSTEM_ERROR',
        'message', 'خطأ في النظام: ' || SQLERRM,
        'message_en', 'System error: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 🚫 دالة حظر الجهاز =====
CREATE OR REPLACE FUNCTION block_device(
    p_device_id UUID,
    p_admin_id UUID,
    p_reason TEXT DEFAULT NULL,
    p_duration_hours INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_blocked_until TIMESTAMP WITH TIME ZONE;
BEGIN
    -- تحديد مدة الحظر
    IF p_duration_hours IS NOT NULL THEN
        v_blocked_until := NOW() + (p_duration_hours || ' hours')::INTERVAL;
    ELSE
        v_blocked_until := NULL; -- حظر دائم
    END IF;
    
    -- تحديث الجهاز
    UPDATE public.devices 
    SET 
        is_blocked = true,
        blocked_until = v_blocked_until,
        trust_level = 'blocked',
        updated_at = NOW()
    WHERE id = p_device_id;
    
    -- تسجيل العملية في سجل الإدارة
    INSERT INTO public.admin_logs (
        admin_id,
        action,
        entity_type,
        entity_id,
        description
    ) VALUES (
        p_admin_id,
        'block_device',
        'device',
        p_device_id,
        COALESCE(p_reason, 'تم حظر الجهاز')
    );
    
    RETURN json_build_object(
        'success', true,
        'message', 'تم حظر الجهاز بنجاح',
        'blocked_until', v_blocked_until
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== ✅ اختبار الدوال =====

-- اختبار دالة فحص الجهاز
SELECT 'اختبار دوال البصمة الرقمية:' as test_info;

-- عرض الأجهزة الحالية
SELECT 
    u.full_name,
    d.device_name,
    d.device_fingerprint,
    d.trust_level,
    d.is_active,
    d.is_blocked
FROM public.devices d
JOIN public.users u ON d.user_id = u.id
ORDER BY d.created_at DESC;

SELECT 'تم إنشاء نظام البصمة الرقمية! ✅' as status;
SELECT 'الآن يمكن للتطبيق استخدام هذه الدوال للتحقق من الأجهزة! 🔒' as usage_info;
