import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import '../utils/logger.dart';
import '../config/supabase_config.dart';
import 'background_location_service.dart';
import 'battery_optimization_service.dart';

/// خدمة تتبع جلسات المستخدمين للمستخدمين المتصلين
class SessionService {
  static final SessionService _instance = SessionService._internal();
  factory SessionService() => _instance;
  SessionService._internal();

  String? _currentSessionId;
  Timer? _heartbeatTimer;
  final _supabase = Supabase.instance.client;
  final _logger = getLogger();
  final _backgroundService = BackgroundLocationService();
  final _batteryService = BatteryOptimizationService();

  /// بدء جلسة جديدة عند تسجيل الدخول
  Future<String?> startSession({
    required String userId,
    required String deviceId,
    String? appVersion,
  }) async {
    try {
      _logger.i('🚀 بدء جلسة جديدة للمستخدم: $userId');
      
      Position? position = await _getCurrentLocation();
      String? locationName = await _getLocationName(position);

      final response = await _supabase.rpc('start_user_session', params: {
        'p_user_id': userId,
        'p_device_id': deviceId,
        'p_app_version': appVersion ?? '3.0.2',
        'p_location_lat': position?.latitude,
        'p_location_lng': position?.longitude,
        'p_location_name': locationName,
      });

      _currentSessionId = response as String?;

      if (_currentSessionId != null) {
        // حفظ معلومات المستخدم للخدمة الخلفية
        await _saveUserInfoForBackground(userId, appVersion);

        // طلب إيقاف تحسين البطارية
        await _batteryService.requestIgnoreBatteryOptimizations();

        // بدء خدمة الخلفية للـ Live Tracking
        bool backgroundStarted = await _backgroundService.startBackgroundService();
        if (backgroundStarted) {
          _logger.i('🔄 تم بدء خدمة الخلفية للـ Live Tracking');
        }

        // بدء heartbeat كل 30 ثانية (محسن)
        _startHeartbeat(userId);
        _logger.i('✅ تم بدء الجلسة بنجاح: $_currentSessionId');
      }

      return _currentSessionId;
    } catch (e) {
      _logger.e('❌ خطأ في بدء الجلسة: $e');
      return null;
    }
  }

  /// تحديث آخر نشاط (heartbeat)
  Future<void> updateActivity(String userId) async {
    if (_currentSessionId == null) return;

    try {
      Position? position = await _getCurrentLocation();
      String? locationName = await _getLocationName(position);

      await _supabase.rpc('update_user_last_activity', params: {
        'p_user_id': userId,
        'p_session_id': _currentSessionId,
        'p_location_lat': position?.latitude,
        'p_location_lng': position?.longitude,
        'p_location_name': locationName,
      });

      _logger.d('🔄 تم تحديث النشاط');
    } catch (e) {
      _logger.w('❌ خطأ في تحديث النشاط: $e');
    }
  }

  /// إنهاء الجلسة عند تسجيل الخروج
  Future<void> endSession() async {
    if (_currentSessionId == null) return;

    try {
      // إيقاف خدمة الخلفية
      await _backgroundService.stopBackgroundService();
      _logger.i('🛑 تم إيقاف خدمة الخلفية');

      await _supabase.rpc('end_user_session', params: {
        'p_session_id': _currentSessionId,
      });

      _currentSessionId = null;
      _heartbeatTimer?.cancel();

      _logger.i('✅ تم إنهاء الجلسة');
    } catch (e) {
      _logger.e('❌ خطأ في إنهاء الجلسة: $e');
    }
  }

  /// تسجيل نشاط معين (تصوير، فيديو، إلخ)
  Future<void> logActivity({
    required String userId,
    required String activityType, // 'photo_taken', 'video_recorded', etc.
    Map<String, dynamic>? activityData,
    int? fileSizeBytes,
    int? durationSeconds,
  }) async {
    try {
      // قائمة الأنشطة المسموحة
      const allowedActivities = [
        'login', 'logout', 'photo_taken', 'video_recorded', 'heartbeat',
        'app_opened', 'app_closed', 'app_resumed', 'app_paused',
        'location_updated', 'upload_started', 'upload_completed', 'upload_failed'
      ];

      // التحقق من نوع النشاط
      String validActivityType = activityType;
      if (!allowedActivities.contains(activityType)) {
        _logger.w('⚠️ نوع نشاط غير مسموح: $activityType، سيتم استبداله بـ heartbeat');
        validActivityType = 'heartbeat';
      }

      Position? position = await _getCurrentLocation();

      await _supabase.from('user_activity_log').insert({
        'user_id': userId,
        'session_id': _currentSessionId,
        'activity_type': validActivityType,
        'activity_data': activityData ?? {},
        'location_lat': position?.latitude,
        'location_lng': position?.longitude,
        'file_size_bytes': fileSizeBytes,
        'duration_seconds': durationSeconds,
      });

      _logger.i('📝 تم تسجيل النشاط: $validActivityType');
    } catch (e) {
      _logger.w('❌ خطأ في تسجيل النشاط: $e');
    }
  }

  /// بدء heartbeat كل 30 ثانية (محسن للـ Live Tracking)
  void _startHeartbeat(String userId) {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      updateActivity(userId);
      logActivity(userId: userId, activityType: 'heartbeat');
    });
    _logger.i('💓 تم بدء heartbeat كل 30 ثانية');
  }

  /// الحصول على الموقع الحالي
  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _logger.w('خدمة الموقع غير مفعلة');
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _logger.w('تم رفض إذن الموقع');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _logger.w('تم رفض إذن الموقع نهائياً');
        return null;
      }

      return await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
          timeLimit: Duration(seconds: 10),
        ),
      );
    } catch (e) {
      _logger.w('❌ خطأ في الحصول على الموقع: $e');
      return null;
    }
  }

  /// الحصول على اسم الموقع
  Future<String?> _getLocationName(Position? position) async {
    if (position == null) return null;
    
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      
      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        return '${place.locality ?? ''} ${place.country ?? ''}'.trim();
      }
    } catch (e) {
      _logger.w('خطأ في الحصول على اسم الموقع: $e');
    }
    
    // fallback إلى الإحداثيات
    return 'الموقع الحالي (${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)})';
  }

  /// الحصول على معرف الجلسة الحالية
  String? get currentSessionId => _currentSessionId;

  /// فحص إذا كانت الجلسة نشطة
  bool get isSessionActive => _currentSessionId != null;

  /// حفظ معلومات المستخدم للخدمة الخلفية
  Future<void> _saveUserInfoForBackground(String userId, String? appVersion) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user_id', userId);

      // حفظ إعدادات Supabase للخدمة الخلفية
      await prefs.setString('supabase_url', SupabaseConfig.supabaseUrl);
      await prefs.setString('supabase_key', SupabaseConfig.supabaseAnonKey);

      // حفظ اسم المستخدم إذا كان متوفر
      final username = _supabase.auth.currentUser?.userMetadata?['full_name'];
      if (username != null) {
        await prefs.setString('current_username', username);
      }

      _logger.i('💾 تم حفظ معلومات المستخدم للخدمة الخلفية');
    } catch (e) {
      _logger.e('❌ فشل في حفظ معلومات المستخدم: $e');
    }
  }

  /// إيقاف الخدمة
  void dispose() {
    _heartbeatTimer?.cancel();
    _backgroundService.stopBackgroundService();
    _currentSessionId = null;
    _logger.i('🛑 تم إيقاف SessionService');
  }
}
