-- 🔧 إصلاح أخير لدالة تحديث الإحصائيات
-- Final Fix for Update Statistics Function
-- Date: 2025-01-17

-- منح صلاحيات إضافية لدالة التحديث
GRANT UPDATE ON public.locations TO anon, authenticated;

-- إعادة إنشاء دالة تحديث الإحصائيات مع صلاحيات محسنة
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT 
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR location_rec IN SELECT location_code FROM public.locations WHERE is_active = true
    LOOP
        UPDATE public.locations 
        SET 
            total_photos = (
                SELECT COUNT(*) FROM public.photos 
                WHERE full_location_code = location_rec.location_code AND status = 'active'
            ),
            total_videos = (
                SELECT COUNT(*) FROM public.videos 
                WHERE full_location_code = location_rec.location_code AND status = 'active'
            ),
            updated_at = NOW()
        WHERE location_code = location_rec.location_code;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع';
END;
$$ LANGUAGE plpgsql;

-- منح صلاحيات تنفيذ الدالة
GRANT EXECUTE ON FUNCTION update_all_locations_statistics() TO anon, authenticated;

-- اختبار الدالة
SELECT update_all_locations_statistics() as result;

SELECT 'تم إصلاح جميع المشاكل بنجاح! 🎉' as final_status;
