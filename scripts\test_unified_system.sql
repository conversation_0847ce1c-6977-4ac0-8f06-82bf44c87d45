-- ===== 🧪 اختبار النظام الموحد الجديد =====
-- تاريخ الاختبار: 2025-07-20
-- الهدف: التأكد من عمل جميع مكونات النظام الجديد

-- 1. اختبار تسجيل الدخول
SELECT 
    '=== اختبار تسجيل الدخول ===' as test_name,
    authenticate_user(
        '0000000000',  -- national_id
        '0000000000',  -- password (نفس الرقم الوطني)
        '***********'::inet,  -- ip_address
        'Test User Agent',  -- user_agent
        '{"test": true}'::jsonb  -- device_info
    ) as login_result;

-- 2. فحص الجداول الأساسية
SELECT 
    '=== فحص الجداول الأساسية ===' as test_name,
    'users' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as users_with_email,
    COUNT(CASE WHEN national_id IS NOT NULL THEN 1 END) as users_with_national_id
FROM public.users
UNION ALL
SELECT 
    '=== فحص الجداول الأساسية ===' as test_name,
    'auth_sessions' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_sessions,
    COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as valid_sessions,
    0 as users_with_national_id
FROM public.auth_sessions
UNION ALL
SELECT 
    '=== فحص الجداول الأساسية ===' as test_name,
    'photos' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as photos_with_user,
    0 as users_with_email,
    0 as users_with_national_id
FROM public.photos
UNION ALL
SELECT 
    '=== فحص الجداول الأساسية ===' as test_name,
    'videos' as table_name,
    COUNT(*) as record_count,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as videos_with_user,
    0 as users_with_email,
    0 as users_with_national_id
FROM public.videos;

-- 3. فحص العلاقات بين الجداول
SELECT 
    '=== فحص العلاقات ===' as test_name,
    'photos -> users' as relationship,
    COUNT(p.id) as total_photos,
    COUNT(u.id) as linked_photos,
    COUNT(p.id) - COUNT(u.id) as orphaned_photos
FROM public.photos p
LEFT JOIN public.users u ON p.user_id = u.id
UNION ALL
SELECT 
    '=== فحص العلاقات ===' as test_name,
    'videos -> users' as relationship,
    COUNT(v.id) as total_videos,
    COUNT(u.id) as linked_videos,
    COUNT(v.id) - COUNT(u.id) as orphaned_videos
FROM public.videos v
LEFT JOIN public.users u ON v.user_id = u.id
UNION ALL
SELECT 
    '=== فحص العلاقات ===' as test_name,
    'devices -> users' as relationship,
    COUNT(d.id) as total_devices,
    COUNT(u.id) as linked_devices,
    COUNT(d.id) - COUNT(u.id) as orphaned_devices
FROM public.devices d
LEFT JOIN public.users u ON d.user_id = u.id;

-- 4. فحص الدوال المساعدة
SELECT 
    '=== فحص الدوال ===' as test_name,
    'hash_password' as function_name,
    hash_password('test123') as test_result,
    LENGTH(hash_password('test123')) as hash_length;

SELECT 
    '=== فحص الدوال ===' as test_name,
    'verify_password' as function_name,
    verify_password('test123', hash_password('test123')) as correct_password,
    verify_password('wrong', hash_password('test123')) as wrong_password;

-- 5. فحص الفهارس
SELECT 
    '=== فحص الفهارس ===' as test_name,
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
  AND tablename IN ('users', 'auth_sessions', 'auth_login_attempts')
ORDER BY tablename, indexname;

-- 6. فحص الصلاحيات
SELECT 
    '=== فحص الصلاحيات ===' as test_name,
    table_schema,
    table_name,
    privilege_type,
    grantee
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
  AND table_name IN ('users', 'auth_sessions', 'photos', 'videos')
  AND grantee != 'postgres'
ORDER BY table_name, privilege_type;

-- 7. اختبار إنشاء جلسة جديدة
SELECT 
    '=== اختبار إنشاء جلسة ===' as test_name,
    create_auth_session(
        (SELECT id FROM public.users WHERE email = '<EMAIL>' LIMIT 1),
        '***********00'::inet,
        'Test Mobile App',
        '{"platform": "test", "version": "1.0.0"}'::jsonb
    ) as session_result;

-- 8. فحص آخر الجلسات النشطة
SELECT 
    '=== الجلسات النشطة ===' as test_name,
    s.id,
    u.email,
    u.full_name,
    s.created_at,
    s.expires_at,
    s.is_active,
    CASE 
        WHEN s.expires_at > NOW() THEN 'صالحة'
        ELSE 'منتهية'
    END as session_status
FROM public.auth_sessions s
JOIN public.users u ON s.user_id = u.id
ORDER BY s.created_at DESC
LIMIT 5;

-- 9. إحصائيات شاملة
SELECT 
    '=== إحصائيات شاملة ===' as summary,
    (SELECT COUNT(*) FROM public.users) as total_users,
    (SELECT COUNT(*) FROM public.users WHERE is_active = true) as active_users,
    (SELECT COUNT(*) FROM public.auth_sessions) as total_sessions,
    (SELECT COUNT(*) FROM public.auth_sessions WHERE is_active = true AND expires_at > NOW()) as active_sessions,
    (SELECT COUNT(*) FROM public.photos) as total_photos,
    (SELECT COUNT(*) FROM public.videos) as total_videos,
    (SELECT COUNT(*) FROM public.devices) as total_devices;

-- 10. فحص أداء الاستعلامات
EXPLAIN (ANALYZE, BUFFERS) 
SELECT u.*, s.session_token, s.expires_at
FROM public.users u
LEFT JOIN public.auth_sessions s ON u.current_session_id = s.id
WHERE u.email = '<EMAIL>' 
  AND u.is_active = true;

-- 11. تنظيف الجلسات المنتهية (اختياري)
-- DELETE FROM public.auth_sessions WHERE expires_at < NOW() - INTERVAL '1 day';

-- 12. تقرير نهائي
SELECT 
    '=== تقرير نهائي ===' as final_report,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.users WHERE is_active = true) > 0 
         AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'auth_sessions') > 0
         AND (SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = 'authenticate_user') > 0
        THEN '✅ النظام جاهز ويعمل بشكل صحيح'
        ELSE '❌ هناك مشاكل في النظام تحتاج إصلاح'
    END as system_status,
    NOW() as test_completed_at;
