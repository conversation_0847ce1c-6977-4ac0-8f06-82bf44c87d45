# تعليمات إعداد قاعدة البيانات - Database Setup Instructions

## 🚨 حل مشكلة "relation does not exist"

إذا واجهت خطأ `relation "videos" does not exist` أو `relation "photos" does not exist`، فهذا يعني أن الجداول الأساسية غير موجودة.

## 📋 خطوات الإعداد بالترتيب الصحيح

### الخطوة 1: إنشاء الجداول الأساسية
```sql
-- في Supabase Dashboard > SQL Editor
-- نسخ ولصق محتوى الملف التالي:
```
📁 `database/migrations/001_create_basic_tables.sql`

هذا الملف سيقوم بـ:
- ✅ إنشاء جدول `users`
- ✅ إنشاء جدول `devices` 
- ✅ إنشاء جدول `photos`
- ✅ إنشاء جدول `videos`
- ✅ إعداد Row Level Security
- ✅ إنشاء Storage buckets
- ✅ إعداد سياسات الأمان

### الخطوة 2: تطبيق نظام المواقع الجديد
```sql
-- بعد نجاح الخطوة الأولى، نسخ ولصق محتوى الملف التالي:
```
📁 `database/migrations/002_update_photos_videos_locations.sql`

هذا الملف سيقوم بـ:
- ✅ إضافة حقول نظام المواقع الجديد
- ✅ إنشاء فهارس للأداء
- ✅ إضافة دوال الترقيم التلقائي
- ✅ ترحيل البيانات الموجودة

## 🔍 التحقق من نجاح الإعداد

### فحص الجداول
```sql
-- التحقق من وجود جميع الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'devices', 'photos', 'videos');
```

### فحص حقول جدول photos
```sql
-- التحقق من حقول جدول الصور
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'photos' 
ORDER BY ordinal_position;
```

### فحص حقول جدول videos
```sql
-- التحقق من حقول جدول الفيديو
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'videos' 
ORDER BY ordinal_position;
```

## 🧪 اختبار النظام

### إدراج بيانات تجريبية
```sql
-- إدراج مستخدم تجريبي (استبدل UUID بمعرف حقيقي)
INSERT INTO users (id, full_name, email) 
VALUES ('your-user-id-here', 'Test User', '<EMAIL>')
ON CONFLICT (id) DO NOTHING;

-- إدراج صورة تجريبية
INSERT INTO photos (
    user_id, 
    file_name, 
    location, 
    location_type, 
    location_number,
    username
) VALUES (
    'your-user-id-here',
    'test_photo.jpg',
    'U101',
    'U',
    '101',
    'Test User'
);
```

### التحقق من الترتيب
```sql
-- فحص ترتيب الصور حسب النظام الجديد
SELECT 
    location_type,
    location_number,
    full_location_code,
    username,
    capture_timestamp,
    sort_order
FROM photos 
ORDER BY location_type, location_number, capture_timestamp;
```

## ⚠️ مشاكل شائعة وحلولها

### المشكلة: "permission denied for table"
**الحل**: تأكد من تفعيل Row Level Security وإنشاء السياسات

### المشكلة: "column does not exist"
**الحل**: تأكد من تطبيق migration 002 بعد 001

### المشكلة: "bucket does not exist"
**الحل**: تأكد من إنشاء Storage buckets في migration 001

### المشكلة: "function does not exist"
**الحل**: تأكد من إنشاء الدوال في migration 002

## 📊 هيكل قاعدة البيانات النهائي

### جدول users
- `id` (UUID, Primary Key)
- `full_name` (TEXT)
- `email` (TEXT)
- `created_at` (TIMESTAMP)

### جدول photos
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `file_name` (TEXT)
- `location` (TEXT) - للتوافق القديم
- `location_type` (TEXT) - U أو C
- `location_number` (TEXT) - 101, 102, ...
- `full_location_code` (TEXT) - U101, C145, ...
- `username` (TEXT)
- `capture_timestamp` (TIMESTAMP)
- `upload_timestamp` (TIMESTAMP)
- `sort_order` (INTEGER)

### جدول videos
- نفس حقول photos +
- `duration_seconds` (INTEGER)
- `file_size_mb` (DECIMAL)

## 🎯 الخطوات التالية

1. ✅ تطبيق migration 001
2. ✅ تطبيق migration 002  
3. ✅ اختبار إدراج البيانات
4. ✅ اختبار التطبيق
5. ✅ مراقبة الأداء

---

**ملاحظة مهمة**: تأكد من تطبيق الملفات بالترتيب الصحيح (001 ثم 002) لتجنب الأخطاء.
