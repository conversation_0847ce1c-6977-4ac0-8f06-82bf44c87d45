-- إنشاء الجداول الأساسية فقط - بدون تعقيدات
-- Basic Tables Only - No Complications
-- Date: 2025-01-15

-- ===== إنشاء جدول المستخدمين =====
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user',
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    department TEXT,
    position TEXT,
    notes TEXT,
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID
);

-- ===== إنشاء جدول الأجهزة =====
CREATE TABLE IF NOT EXISTS devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_fingerprint TEXT NOT NULL UNIQUE,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    confidence_score DECIMAL(5,2) DEFAULT 0,
    trust_level TEXT DEFAULT 'untrusted',
    auth_attempts INTEGER DEFAULT 0,
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    last_verified_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الصور =====
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الفيديو =====
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    resolution TEXT,
    fps INTEGER,
    codec TEXT,
    bitrate INTEGER,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    thumbnail_url TEXT,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الجلسات =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE,
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== إنشاء جدول سجلات الإدارة =====
CREATE TABLE IF NOT EXISTS admin_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) NOT NULL,
    target_user_id UUID REFERENCES users(id),
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id UUID,
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الإحصائيات =====
CREATE TABLE IF NOT EXISTS system_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_date DATE DEFAULT CURRENT_DATE,
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users_today INTEGER DEFAULT 0,
    total_devices INTEGER DEFAULT 0,
    active_devices INTEGER DEFAULT 0,
    blocked_devices INTEGER DEFAULT 0,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    photos_uploaded_today INTEGER DEFAULT 0,
    videos_uploaded_today INTEGER DEFAULT 0,
    total_storage_used_mb BIGINT DEFAULT 0,
    photos_storage_mb BIGINT DEFAULT 0,
    videos_storage_mb BIGINT DEFAULT 0,
    u_locations_used INTEGER DEFAULT 0,
    c_locations_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(stat_date)
);

-- ===== إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- ===== إضافة القيود البسيطة =====
DO $$
BEGIN
    -- إضافة قيود للمستخدمين
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'users_account_type_check') THEN
        ALTER TABLE users ADD CONSTRAINT users_account_type_check
        CHECK (account_type IN ('admin', 'user', 'supervisor'));
    END IF;

    -- إضافة قيود للأجهزة
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'devices_trust_level_check') THEN
        ALTER TABLE devices ADD CONSTRAINT devices_trust_level_check
        CHECK (trust_level IN ('high', 'medium', 'low', 'untrusted', 'suspicious', 'blocked'));
    END IF;

    -- إضافة قيود للصور
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'photos_location_type_check') THEN
        ALTER TABLE photos ADD CONSTRAINT photos_location_type_check
        CHECK (location_type IN ('U', 'C') OR location_type IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'photos_status_check') THEN
        ALTER TABLE photos ADD CONSTRAINT photos_status_check
        CHECK (status IN ('active', 'archived', 'deleted'));
    END IF;

    -- إضافة قيود للفيديو
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'videos_location_type_check') THEN
        ALTER TABLE videos ADD CONSTRAINT videos_location_type_check
        CHECK (location_type IN ('U', 'C') OR location_type IS NULL);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'videos_status_check') THEN
        ALTER TABLE videos ADD CONSTRAINT videos_status_check
        CHECK (status IN ('active', 'archived', 'deleted'));
    END IF;
END $$;

-- ===== إضافة الفهارس الأساسية =====
CREATE INDEX IF NOT EXISTS idx_users_national_id ON users(national_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);

CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);

CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);

CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);

-- ===== رسائل النجاح =====
SELECT 'تم إنشاء الجداول الأساسية بنجاح! ✅' as status;

-- عرض الجداول المُنشأة
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'devices', 'photos', 'videos', 'user_sessions', 'admin_logs', 'system_stats')
ORDER BY table_name;

SELECT 'الآن يمكن تطبيق ملف السياسات والدوال' as next_step;
