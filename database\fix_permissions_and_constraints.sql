-- ===== إصلاح أذونات وقيود قاعدة البيانات =====

-- 1. إصلاح أذونات جدول upload_queue
DO $$
BEGIN
    -- التأكد من وجود الجدول أولاً
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'upload_queue') THEN
        -- منح أذونات كاملة للمستخدمين المصادق عليهم
        GRANT ALL ON upload_queue TO authenticated;
        GRANT ALL ON upload_queue TO anon;
        
        -- منح أذونات على sequence إذا كان موجود
        IF EXISTS (SELECT 1 FROM information_schema.sequences WHERE sequence_name = 'upload_queue_id_seq') THEN
            GRANT ALL ON SEQUENCE upload_queue_id_seq TO authenticated;
            GRANT ALL ON SEQUENCE upload_queue_id_seq TO anon;
        END IF;
        
        RAISE NOTICE 'تم إصلاح أذونات جدول upload_queue';
    ELSE
        RAISE NOTICE 'جدول upload_queue غير موجود - سيتم إنشاؤه';
    END IF;
END $$;

-- 2. إصلاح قيود جدول user_activity_log
DO $$
BEGIN
    -- حذف القيد القديم إذا كان موجود
    IF EXISTS (SELECT 1 FROM information_schema.check_constraints 
               WHERE constraint_name = 'user_activity_log_activity_type_check') THEN
        ALTER TABLE user_activity_log DROP CONSTRAINT user_activity_log_activity_type_check;
        RAISE NOTICE 'تم حذف القيد القديم user_activity_log_activity_type_check';
    END IF;
    
    -- إضافة قيد جديد محدث يتضمن جميع أنواع الأنشطة
    ALTER TABLE user_activity_log ADD CONSTRAINT user_activity_log_activity_type_check 
    CHECK (activity_type IN (
        'login', 'logout', 'photo_taken', 'video_recorded', 'heartbeat',
        'app_opened', 'app_closed', 'app_resumed', 'app_paused',
        'location_updated', 'upload_started', 'upload_completed', 'upload_failed'
    ));
    
    RAISE NOTICE 'تم إضافة قيد جديد محدث لأنواع الأنشطة';
END $$;

-- 3. إنشاء جدول upload_queue إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS upload_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL CHECK (file_type IN ('photo', 'video')),
    file_size_bytes BIGINT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    user_id UUID,
    
    -- حالة الرفع
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'uploading', 'uploaded', 'failed', 'cancelled')),
    upload_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    
    -- أوقات مهمة
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    uploaded_at TIMESTAMP WITH TIME ZONE,
    
    -- معلومات الخطأ
    error_message TEXT,
    error_details JSONB,
    
    -- معلومات إضافية
    metadata JSONB,
    
    -- فهارس
    UNIQUE(file_name, user_id)
);

-- 4. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_upload_queue_status ON upload_queue(status);
CREATE INDEX IF NOT EXISTS idx_upload_queue_user_id ON upload_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_upload_queue_created_at ON upload_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_upload_queue_file_type ON upload_queue(file_type);

-- 5. منح أذونات على الجدول الجديد
GRANT ALL ON upload_queue TO authenticated;
GRANT ALL ON upload_queue TO anon;

-- 6. إنشاء RLS policies للأمان
ALTER TABLE upload_queue ENABLE ROW LEVEL SECURITY;

-- Policy للقراءة - المستخدم يرى ملفاته فقط
CREATE POLICY "Users can view own uploads" ON upload_queue
    FOR SELECT USING (auth.uid() = user_id);

-- Policy للإدراج - المستخدم يضيف ملفاته فقط
CREATE POLICY "Users can insert own uploads" ON upload_queue
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy للتحديث - المستخدم يحدث ملفاته فقط
CREATE POLICY "Users can update own uploads" ON upload_queue
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy للحذف - المستخدم يحذف ملفاته فقط
CREATE POLICY "Users can delete own uploads" ON upload_queue
    FOR DELETE USING (auth.uid() = user_id);

-- 7. إنشاء الدوال المطلوبة
CREATE OR REPLACE FUNCTION add_to_upload_queue(
    p_file_name TEXT,
    p_file_path TEXT,
    p_file_type TEXT,
    p_file_size_bytes BIGINT DEFAULT NULL,
    p_location TEXT DEFAULT NULL,
    p_username TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_queue_id UUID;
    v_location_data RECORD;
    v_user_id UUID;
BEGIN
    -- استخدام المستخدم الحالي إذا لم يتم تمرير user_id
    v_user_id := COALESCE(p_user_id, auth.uid());
    
    -- تحليل كود الموقع
    SELECT 
        CASE 
            WHEN p_location ~ '^[UC]\d+$' THEN LEFT(p_location, 1)
            ELSE NULL 
        END as location_type,
        CASE 
            WHEN p_location ~ '^[UC]\d+$' THEN SUBSTRING(p_location FROM 2)
            ELSE NULL 
        END as location_number
    INTO v_location_data;
    
    -- إدراج في قائمة الرفع
    INSERT INTO upload_queue (
        file_name, file_path, file_type, file_size_bytes,
        location, location_type, location_number,
        username, user_id, metadata
    ) VALUES (
        p_file_name, p_file_path, p_file_type, p_file_size_bytes,
        p_location, v_location_data.location_type, v_location_data.location_number,
        p_username, v_user_id, p_metadata
    ) 
    ON CONFLICT (file_name, user_id) 
    DO UPDATE SET
        file_path = EXCLUDED.file_path,
        status = 'pending',
        upload_attempts = 0,
        error_message = NULL,
        created_at = NOW()
    RETURNING id INTO v_queue_id;
    
    RETURN v_queue_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة تحديث حالة الرفع
CREATE OR REPLACE FUNCTION update_upload_status(
    p_queue_id UUID,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL,
    p_error_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE upload_queue 
    SET 
        status = p_status,
        upload_attempts = CASE 
            WHEN p_status = 'uploading' THEN upload_attempts + 1
            ELSE upload_attempts
        END,
        last_attempt_at = CASE 
            WHEN p_status IN ('uploading', 'failed') THEN NOW()
            ELSE last_attempt_at
        END,
        uploaded_at = CASE 
            WHEN p_status = 'uploaded' THEN NOW()
            ELSE uploaded_at
        END,
        error_message = p_error_message,
        error_details = p_error_details
    WHERE id = p_queue_id AND user_id = auth.uid();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. دالة الحصول على الملفات المعلقة
CREATE OR REPLACE FUNCTION get_pending_uploads(
    p_user_id UUID DEFAULT NULL,
    p_file_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
    id UUID,
    file_name TEXT,
    file_path TEXT,
    file_type TEXT,
    file_size_bytes BIGINT,
    location TEXT,
    username TEXT,
    upload_attempts INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB
) AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- استخدام المستخدم الحالي إذا لم يتم تمرير user_id
    v_user_id := COALESCE(p_user_id, auth.uid());
    
    RETURN QUERY
    SELECT 
        uq.id, uq.file_name, uq.file_path, uq.file_type, uq.file_size_bytes,
        uq.location, uq.username, uq.upload_attempts, uq.created_at, uq.metadata
    FROM upload_queue uq
    WHERE 
        uq.status = 'pending'
        AND uq.user_id = v_user_id
        AND (p_file_type IS NULL OR uq.file_type = p_file_type)
        AND uq.upload_attempts < uq.max_attempts
    ORDER BY uq.created_at ASC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. دالة تنظيف قائمة الرفع
CREATE OR REPLACE FUNCTION cleanup_upload_queue(
    p_days_old INTEGER DEFAULT 7
) RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف الملفات المرفوعة بنجاح والقديمة
    DELETE FROM upload_queue 
    WHERE 
        status = 'uploaded' 
        AND uploaded_at < NOW() - INTERVAL '1 day' * p_days_old
        AND user_id = auth.uid();
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    -- حذف الملفات الفاشلة والقديمة جداً
    DELETE FROM upload_queue 
    WHERE 
        status = 'failed' 
        AND created_at < NOW() - INTERVAL '1 day' * (p_days_old * 2)
        AND upload_attempts >= max_attempts
        AND user_id = auth.uid();
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. إنشاء view للإحصائيات
CREATE OR REPLACE VIEW upload_stats AS
SELECT 
    file_type,
    status,
    COUNT(*) as count,
    AVG(file_size_bytes) as avg_file_size,
    SUM(file_size_bytes) as total_size,
    MIN(created_at) as oldest_file,
    MAX(created_at) as newest_file
FROM upload_queue
WHERE user_id = auth.uid()
GROUP BY file_type, status;

-- منح أذونات على الدوال والـ view
GRANT EXECUTE ON FUNCTION add_to_upload_queue TO authenticated;
GRANT EXECUTE ON FUNCTION update_upload_status TO authenticated;
GRANT EXECUTE ON FUNCTION get_pending_uploads TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_upload_queue TO authenticated;
GRANT SELECT ON upload_stats TO authenticated;

SELECT 'تم تطبيق جميع الإصلاحات بنجاح!' as result;
