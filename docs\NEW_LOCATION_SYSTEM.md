# نظام المواقع الجديد - New Location System

## 📋 نظرة عامة - Overview

تم تحديث نظام المواقع في تطبيق Moon Memory ليدعم نظاماً هرمياً جديداً:
- **مواقع U**: من U101 إلى U125 (25 موقع)
- **مواقع C**: من C101 إلى C145 (45 موقع)

## 🔄 التغييرات المطبقة - Applied Changes

### 1. صفحة الترحيب - Welcome Screen
- ✅ استبدال قائمة المواقع الثابتة بنظام اختيار متدرج
- ✅ خيار أول: اختيار نوع الموقع (U أو C)
- ✅ خيار ثاني: اختيار رقم الموقع حسب النوع المختار
- ✅ تحديث زر الكاميرا ليظهر الموقع المختار
- ✅ منع فتح الكاميرا بدون اختيار موقع

### 2. قاعدة البيانات - Database
- ✅ إضافة حقول جديدة لجدول `photos`:
  - `location_type`: نوع الموقع (U/C)
  - `location_number`: رقم الموقع
  - `full_location_code`: كود الموقع الكامل (مُحسوب تلقائياً)
  - `username`: اسم المستخدم
  - `capture_timestamp`: وقت التقاط الصورة
  - `upload_timestamp`: وقت رفع الصورة
  - `sort_order`: ترتيب الصورة

- ✅ إضافة حقول جديدة لجدول `videos`:
  - نفس الحقول المضافة للصور
  - `duration_seconds`: مدة الفيديو
  - `file_size_mb`: حجم الملف

- ✅ إنشاء فهارس للبحث السريع
- ✅ دوال تلقائية لترقيم الفرز
- ✅ دوال ترحيل البيانات الموجودة

### 3. خدمات الرفع - Upload Services
- ✅ تحديث `PhotosService` لدعم النظام الجديد
- ✅ تحديث `AutoUploadService` لدعم النظام الجديد
- ✅ إضافة دوال تحليل كود الموقع
- ✅ حفظ البيانات الجديدة في قاعدة البيانات

### 4. واجهة الكاميرا - Camera Interface
- ✅ تمرير الموقع المختار بدلاً من الموقع الجغرافي
- ✅ تحديث `PhotoPreviewScreen` لدعم معاملين للموقع
- ✅ دعم `VideoPreviewScreen` للنظام الجديد

## 🧪 الاختبارات - Tests
- ✅ إنشاء اختبارات شاملة للنظام الجديد
- ✅ اختبار واجهة اختيار المواقع
- ✅ اختبار دوال تحليل الموقع
- ✅ اختبار نطاقات المواقع

## 📊 ترتيب البيانات - Data Sorting

البيانات ستُرتب حسب:
1. **نوع الموقع** (U أو C)
2. **رقم الموقع** (101، 102، ...)
3. **وقت التقاط/التسجيل**
4. **اسم المستخدم**

## 🚀 خطوات التطبيق - Implementation Steps

### 1. تطبيق تحديثات قاعدة البيانات
```sql
-- تشغيل ملف SQL في Supabase Dashboard
-- Run SQL file in Supabase Dashboard
\i database/migrations/002_update_photos_videos_locations.sql
```

### 2. اختبار النظام
```bash
# تشغيل الاختبارات
flutter test test/location_system_test.dart

# تشغيل التطبيق
flutter run
```

### 3. التحقق من الوظائف
- [ ] اختيار نوع الموقع (U/C)
- [ ] اختيار رقم الموقع
- [ ] التقاط صورة مع الموقع الجديد
- [ ] تسجيل فيديو مع الموقع الجديد
- [ ] رفع البيانات إلى قاعدة البيانات
- [ ] التحقق من الترتيب الصحيح

## 🔧 استكشاف الأخطاء - Troubleshooting

### مشكلة: لا تظهر أرقام المواقع
**الحل**: تأكد من اختيار نوع الموقع أولاً

### مشكلة: لا يعمل زر الكاميرا
**الحل**: تأكد من اختيار الموقع الكامل (النوع + الرقم)

### مشكلة: خطأ في قاعدة البيانات
**الحل**: تأكد من تطبيق migration file بشكل صحيح

## 📝 ملاحظات مهمة - Important Notes

1. **التوافق مع النظام القديم**: الحقل `location` القديم محفوظ للتوافق
2. **البيانات الموجودة**: سيتم ترحيلها تلقائياً عند تطبيق migration
3. **الفهارس**: تم إنشاء فهارس للبحث السريع والأداء المحسن
4. **الترقيم التلقائي**: يتم ترقيم الصور/الفيديو تلقائياً حسب الموقع والمستخدم

## 🎯 الخطوات التالية - Next Steps

1. تطبيق migration في قاعدة البيانات
2. اختبار النظام بالكامل
3. مراقبة الأداء والأخطاء
4. تحسينات إضافية حسب الحاجة

---

**تاريخ التحديث**: 2025-01-15  
**الإصدار**: 2.0.0  
**المطور**: Moon Memory Team
