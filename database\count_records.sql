-- عدد السجلات في الجداول
SELECT 
    'users' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') 
        THEN (SELECT COUNT(*)::text FROM public.users)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT 
    'photos' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'photos') 
        THEN (SELECT COUNT(*)::text FROM public.photos)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT 
    'videos' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'videos') 
        THEN (SELECT COUNT(*)::text FROM public.videos)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT 
    'devices' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'devices') 
        THEN (SELECT COUNT(*)::text FROM public.devices)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT 
    'user_sessions' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_sessions') 
        THEN (SELECT COUNT(*)::text FROM public.user_sessions)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT 
    'upload_queue' as "الجدول",
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'upload_queue') 
        THEN (SELECT COUNT(*)::text FROM public.upload_queue)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات";
