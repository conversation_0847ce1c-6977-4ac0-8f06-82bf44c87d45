-- ===== فحص شامل لقاعدة البيانات الحالية =====
-- نسخ ولصق هذا الكود في Supabase Dashboard > SQL Editor

-- ===== 1. فحص جميع الجداول الموجودة =====
SELECT '========== الجداول الموجودة ==========' as "معلومات";

SELECT
    table_schema as "المخطط",
    table_name as "اسم الجدول"
FROM information_schema.tables
WHERE table_schema IN ('public', 'auth', 'storage')
AND table_type = 'BASE TABLE'
ORDER BY table_schema, table_name;

-- ===== 2. فحص بنية جدول public.users =====
SELECT '========== بنية جدول public.users ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع",
    is_nullable as "يقبل null",
    column_default as "القيمة الافتراضية"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'users'
ORDER BY ordinal_position;

-- ===== 3. فحص بنية جدول auth.users =====
SELECT '========== بنية جدول auth.users ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع",
    is_nullable as "يقبل null"
FROM information_schema.columns
WHERE table_schema = 'auth'
AND table_name = 'users'
ORDER BY ordinal_position;

-- ===== 4. فحص جداول أخرى مهمة =====
SELECT '========== بنية جدول photos ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'photos'
ORDER BY ordinal_position;

SELECT '========== بنية جدول videos ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'videos'
ORDER BY ordinal_position;

SELECT '========== بنية جدول devices ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'devices'
ORDER BY ordinal_position;

SELECT '========== بنية جدول user_sessions ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'user_sessions'
ORDER BY ordinal_position;

SELECT '========== بنية جدول upload_queue ==========' as "معلومات";

SELECT
    column_name as "العمود",
    data_type as "النوع"
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'upload_queue'
ORDER BY ordinal_position;

-- ===== 5. فحص المفاتيح الخارجية =====
SELECT '========== المفاتيح الخارجية ==========' as "معلومات";

SELECT
    tc.table_name as "الجدول",
    kcu.column_name as "العمود",
    ccu.table_name as "يشير إلى جدول",
    ccu.column_name as "يشير إلى عمود"
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- ===== 6. فحص Storage Buckets =====
SELECT '========== Storage Buckets ==========' as "معلومات";

SELECT
    id as "اسم البكت",
    public as "عام",
    created_at as "تاريخ الإنشاء"
FROM storage.buckets
ORDER BY created_at;

-- ===== 7. عدد السجلات في كل جدول =====
SELECT '========== عدد السجلات ==========' as "معلومات";

-- فحص وجود الجداول وعد السجلات
SELECT
    'users' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users')
        THEN (SELECT COUNT(*)::text FROM public.users)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT
    'photos' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'photos')
        THEN (SELECT COUNT(*)::text FROM public.photos)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT
    'videos' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'videos')
        THEN (SELECT COUNT(*)::text FROM public.videos)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT
    'devices' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'devices')
        THEN (SELECT COUNT(*)::text FROM public.devices)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT
    'user_sessions' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_sessions')
        THEN (SELECT COUNT(*)::text FROM public.user_sessions)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات"
UNION ALL
SELECT
    'upload_queue' as "الجدول",
    CASE
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'upload_queue')
        THEN (SELECT COUNT(*)::text FROM public.upload_queue)
        ELSE 'الجدول غير موجود'
    END as "عدد السجلات";

-- ===== 8. فحص الدوال المخصصة =====
SELECT '========== الدوال المخصصة ==========' as "معلومات";

SELECT
    routine_name as "اسم الدالة",
    routine_type as "النوع"
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name NOT LIKE 'pg_%'
ORDER BY routine_name;

-- ===== 9. فحص سياسات RLS =====
SELECT '========== سياسات RLS ==========' as "معلومات";

SELECT
    tablename as "الجدول",
    policyname as "اسم السياسة",
    cmd as "النوع"
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

SELECT '========== انتهى الفحص الشامل ==========' as "النتيجة";
