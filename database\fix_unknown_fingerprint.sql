-- حل مشكلة البصمة المجهولة
-- Fix Unknown Fingerprint Issue

-- دالة محسنة للتحقق من الجهاز مع التعامل مع البصمات المجهولة
CREATE OR REPLACE FUNCTION verify_device_single(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    existing_device_id UUID;
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص عدد الأجهزة الحالية للمستخدم
    SELECT COUNT(*) INTO device_count
    FROM devices
    WHERE user_id = p_user_id AND is_active = true;

    -- البحث عن جهاز موجود
    IF device_count = 1 AND (p_device_fingerprint = 'unknown' OR p_android_id = 'unknown') THEN
        -- إذا كان المستخدم لديه جهاز واحد والبصمة مجهولة، استخدم الجهاز الموجود
        SELECT id INTO existing_device_id
        FROM devices
        WHERE user_id = p_user_id AND is_active = true
        LIMIT 1;
    ELSE
        -- البحث العادي بالبصمة أو Android ID
        SELECT id INTO existing_device_id
        FROM devices
        WHERE user_id = p_user_id 
        AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
        AND is_active = true
        LIMIT 1;
    END IF;

    IF existing_device_id IS NOT NULL THEN
        -- تحديث الجهاز الموجود
        UPDATE devices
        SET 
            device_fingerprint = CASE 
                WHEN p_device_fingerprint != 'unknown' THEN p_device_fingerprint 
                ELSE device_fingerprint 
            END,
            android_id = CASE 
                WHEN p_android_id != 'unknown' THEN p_android_id 
                ELSE android_id 
            END,
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            last_verified_at = NOW(),
            updated_at = NOW()
        WHERE id = existing_device_id;

        SELECT json_build_object(
            'success', true,
            'action', 'updated',
            'device_id', existing_device_id,
            'message', 'تم تحديث الجهاز الموجود'
        ) INTO result;

    ELSIF device_count = 0 THEN
        -- إنشاء جهاز جديد (أول جهاز للمستخدم)
        INSERT INTO devices (
            user_id,
            device_fingerprint,
            android_id,
            device_name,
            device_model,
            device_brand,
            trust_level,
            is_active,
            last_verified_at
        ) VALUES (
            p_user_id,
            COALESCE(NULLIF(p_device_fingerprint, 'unknown'), 'device_' || substr(p_user_id::text, 1, 8)),
            COALESCE(NULLIF(p_android_id, 'unknown'), 'android_' || substr(p_user_id::text, 1, 8)),
            p_device_name,
            p_device_model,
            p_device_brand,
            'high',
            true,
            NOW()
        ) RETURNING id INTO existing_device_id;

        SELECT json_build_object(
            'success', true,
            'action', 'created',
            'device_id', existing_device_id,
            'message', 'تم إنشاء جهاز جديد'
        ) INTO result;

    ELSE
        -- المستخدم لديه جهاز بالفعل - رفض الجهاز الجديد
        SELECT json_build_object(
            'success', false,
            'action', 'rejected',
            'error_code', 'DEVICE_LIMIT_REACHED',
            'message', 'هذا الحساب مرتبط بجهاز آخر. لا يمكن استخدامه على هذا الجهاز.'
        ) INTO result;
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في التحقق من الجهاز: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
