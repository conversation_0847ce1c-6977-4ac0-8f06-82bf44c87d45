-- 🔧 إصلاح صلاحيات جدول المواقع
-- Fix Locations Table Permissions
-- Date: 2025-01-17

-- إيقاف RLS مؤقتاً لإصلاح الصلاحيات
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

-- حذف جميع السياسات القديمة
DROP POLICY IF EXISTS locations_policy ON public.locations;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.locations;
DROP POLICY IF EXISTS users_policy ON public.users;
DROP POLICY IF EXISTS "Enable all for authenticated users only" ON public.users;
DROP POLICY IF EXISTS photos_policy ON public.photos;
DROP POLICY IF EXISTS "Users can view own photos" ON public.photos;
DROP POLICY IF EXISTS "Users can insert own photos" ON public.photos;
DROP POLICY IF EXISTS "Users can update own photos" ON public.photos;
DROP POLICY IF EXISTS "Users can delete own photos" ON public.photos;
DROP POLICY IF EXISTS videos_policy ON public.videos;
DROP POLICY IF EXISTS "Users can view own videos" ON public.videos;
DROP POLICY IF EXISTS "Users can insert own videos" ON public.videos;
DROP POLICY IF EXISTS "Users can update own videos" ON public.videos;
DROP POLICY IF EXISTS "Users can delete own videos" ON public.videos;

-- إعادة تفعيل RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات محسنة جديدة
CREATE POLICY "locations_read_policy" ON public.locations
    FOR SELECT USING (true);

CREATE POLICY "users_all_policy" ON public.users
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "photos_select_policy" ON public.photos
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "photos_insert_policy" ON public.photos
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "photos_update_policy" ON public.photos
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "photos_delete_policy" ON public.photos
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "videos_select_policy" ON public.videos
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "videos_insert_policy" ON public.videos
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "videos_update_policy" ON public.videos
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "videos_delete_policy" ON public.videos
    FOR DELETE USING (auth.uid() = user_id);

-- منح صلاحيات إضافية للدوال
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON public.locations TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_locations_with_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_all_locations_statistics() TO anon, authenticated;

-- اختبار الصلاحيات
SELECT 'تم إصلاح الصلاحيات بنجاح!' as status;
SELECT COUNT(*) as total_locations FROM public.locations;
SELECT location_code, location_type, location_name_ar 
FROM public.locations 
WHERE location_type = 'U' 
LIMIT 5;
