-- ===== 🔍 فحص وإعداد جدول المستخدمين =====
-- التحقق من وجود جدول users وإضافة الحقول المطلوبة

-- ===== 📋 إنشاء جدول المستخدمين إذا لم يكن موجود =====
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT,
    location_code TEXT,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== 🔧 إضافة الحقول المفقودة إذا لم تكن موجودة =====

-- إضافة حقل is_admin إذا لم يكن موجود
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'is_admin'
    ) THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- إضافة حقل full_name إذا لم يكن موجود
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'full_name'
    ) THEN
        ALTER TABLE users ADD COLUMN full_name TEXT;
    END IF;
END $$;

-- إضافة حقل location_code إذا لم يكن موجود
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'location_code'
    ) THEN
        ALTER TABLE users ADD COLUMN location_code TEXT;
    END IF;
END $$;

-- إضافة حقل last_login إذا لم يكن موجود
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'last_login'
    ) THEN
        ALTER TABLE users ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- ===== 📊 التحقق من هيكل الجدول =====
SELECT 
    'جدول المستخدمين:' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- ===== 🎯 إنشاء مستخدم تجريبي للاختبار (اختياري) =====
-- قم بإلغاء التعليق إذا كنت تريد إنشاء مستخدم تجريبي

/*
INSERT INTO users (username, full_name, location_code, is_admin)
VALUES ('test_user', 'مستخدم تجريبي', 'C101', FALSE)
ON CONFLICT (username) DO NOTHING;
*/

-- ===== ✅ رسالة النجاح =====
SELECT 
    '✅ تم فحص وإعداد جدول المستخدمين بنجاح!' as status,
    'الجدول جاهز لاستقبال الجلسات' as ready;
