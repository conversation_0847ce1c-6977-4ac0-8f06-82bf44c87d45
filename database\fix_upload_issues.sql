-- ===== إصلاح مشاكل الرفع في قاعدة البيانات =====

-- 1. توحيد أسماء الحقول في جدول photos
DO $$
BEGIN
    -- إضافة حقل image_url إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'image_url') THEN
        ALTER TABLE photos ADD COLUMN image_url TEXT;
    END IF;
    
    -- نسخ البيانات من url إلى image_url
    UPDATE photos SET image_url = url WHERE image_url IS NULL AND url IS NOT NULL;
    
    -- إضافة حقل file_size_bytes إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'file_size_bytes') THEN
        ALTER TABLE photos ADD COLUMN file_size_bytes BIGINT;
    END IF;
    
    -- إضا<PERSON>ة حقل upload_status إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'upload_status') THEN
        ALTER TABLE photos ADD COLUMN upload_status TEXT DEFAULT 'uploaded';
    END IF;
    
    -- إضافة حقل storage_path إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'photos' AND column_name = 'storage_path') THEN
        ALTER TABLE photos ADD COLUMN storage_path TEXT;
    END IF;
END $$;

-- 2. توحيد أسماء الحقول في جدول videos
DO $$
BEGIN
    -- إضافة حقل file_size_bytes إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'videos' AND column_name = 'file_size_bytes') THEN
        ALTER TABLE videos ADD COLUMN file_size_bytes BIGINT;
    END IF;
    
    -- إضافة حقل duration_seconds إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'videos' AND column_name = 'duration_seconds') THEN
        ALTER TABLE videos ADD COLUMN duration_seconds INTEGER;
    END IF;
    
    -- إضافة حقل upload_status إذا لم يكن موجود
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'videos' AND column_name = 'upload_status') THEN
        ALTER TABLE videos ADD COLUMN upload_status TEXT DEFAULT 'uploaded';
    END IF;
    
    -- تأكد من وجود storage_path
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'videos' AND column_name = 'storage_path') THEN
        ALTER TABLE videos ADD COLUMN storage_path TEXT;
    END IF;
END $$;

-- 3. إنشاء جدول upload_queue لتتبع حالة الرفع
CREATE TABLE IF NOT EXISTS upload_queue (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL CHECK (file_type IN ('photo', 'video')),
    file_size_bytes BIGINT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    user_id UUID,
    
    -- حالة الرفع
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'uploading', 'uploaded', 'failed', 'cancelled')),
    upload_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    
    -- أوقات مهمة
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    uploaded_at TIMESTAMP WITH TIME ZONE,
    
    -- معلومات الخطأ
    error_message TEXT,
    error_details JSONB,
    
    -- معلومات إضافية
    metadata JSONB,
    
    -- فهارس
    UNIQUE(file_name, user_id)
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_upload_queue_status ON upload_queue(status);
CREATE INDEX IF NOT EXISTS idx_upload_queue_user_id ON upload_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_upload_queue_created_at ON upload_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_upload_queue_file_type ON upload_queue(file_type);

-- 4. دالة لإضافة ملف إلى قائمة الرفع
CREATE OR REPLACE FUNCTION add_to_upload_queue(
    p_file_name TEXT,
    p_file_path TEXT,
    p_file_type TEXT,
    p_file_size_bytes BIGINT DEFAULT NULL,
    p_location TEXT DEFAULT NULL,
    p_username TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_queue_id UUID;
    v_location_data RECORD;
BEGIN
    -- تحليل كود الموقع
    SELECT 
        CASE 
            WHEN p_location ~ '^[UC]\d+$' THEN LEFT(p_location, 1)
            ELSE NULL 
        END as location_type,
        CASE 
            WHEN p_location ~ '^[UC]\d+$' THEN SUBSTRING(p_location FROM 2)
            ELSE NULL 
        END as location_number
    INTO v_location_data;
    
    -- إدراج في قائمة الرفع
    INSERT INTO upload_queue (
        file_name, file_path, file_type, file_size_bytes,
        location, location_type, location_number,
        username, user_id, metadata
    ) VALUES (
        p_file_name, p_file_path, p_file_type, p_file_size_bytes,
        p_location, v_location_data.location_type, v_location_data.location_number,
        p_username, p_user_id, p_metadata
    ) 
    ON CONFLICT (file_name, user_id) 
    DO UPDATE SET
        file_path = EXCLUDED.file_path,
        status = 'pending',
        upload_attempts = 0,
        error_message = NULL,
        created_at = NOW()
    RETURNING id INTO v_queue_id;
    
    RETURN v_queue_id;
END;
$$ LANGUAGE plpgsql;

-- 5. دالة لتحديث حالة الرفع
CREATE OR REPLACE FUNCTION update_upload_status(
    p_queue_id UUID,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL,
    p_error_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE upload_queue 
    SET 
        status = p_status,
        upload_attempts = CASE 
            WHEN p_status = 'uploading' THEN upload_attempts + 1
            ELSE upload_attempts
        END,
        last_attempt_at = CASE 
            WHEN p_status IN ('uploading', 'failed') THEN NOW()
            ELSE last_attempt_at
        END,
        uploaded_at = CASE 
            WHEN p_status = 'uploaded' THEN NOW()
            ELSE uploaded_at
        END,
        error_message = p_error_message,
        error_details = p_error_details
    WHERE id = p_queue_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 6. دالة للحصول على الملفات المعلقة
CREATE OR REPLACE FUNCTION get_pending_uploads(
    p_user_id UUID DEFAULT NULL,
    p_file_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
    id UUID,
    file_name TEXT,
    file_path TEXT,
    file_type TEXT,
    file_size_bytes BIGINT,
    location TEXT,
    username TEXT,
    upload_attempts INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        uq.id, uq.file_name, uq.file_path, uq.file_type, uq.file_size_bytes,
        uq.location, uq.username, uq.upload_attempts, uq.created_at, uq.metadata
    FROM upload_queue uq
    WHERE 
        uq.status = 'pending'
        AND (p_user_id IS NULL OR uq.user_id = p_user_id)
        AND (p_file_type IS NULL OR uq.file_type = p_file_type)
        AND uq.upload_attempts < uq.max_attempts
    ORDER BY uq.created_at ASC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 7. دالة لتنظيف قائمة الرفع
CREATE OR REPLACE FUNCTION cleanup_upload_queue(
    p_days_old INTEGER DEFAULT 7
) RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف الملفات المرفوعة بنجاح والقديمة
    DELETE FROM upload_queue 
    WHERE 
        status = 'uploaded' 
        AND uploaded_at < NOW() - INTERVAL '1 day' * p_days_old;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    -- حذف الملفات الفاشلة والقديمة جداً
    DELETE FROM upload_queue 
    WHERE 
        status = 'failed' 
        AND created_at < NOW() - INTERVAL '1 day' * (p_days_old * 2)
        AND upload_attempts >= max_attempts;
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 8. إنشاء view للإحصائيات
CREATE OR REPLACE VIEW upload_stats AS
SELECT 
    file_type,
    status,
    COUNT(*) as count,
    AVG(file_size_bytes) as avg_file_size,
    SUM(file_size_bytes) as total_size,
    MIN(created_at) as oldest_file,
    MAX(created_at) as newest_file
FROM upload_queue
GROUP BY file_type, status;

-- تطبيق الإصلاحات
SELECT 'تم تطبيق إصلاحات قاعدة البيانات بنجاح!' as result;
