-- 🔧 تطبيق التحسينات خطوة بخطوة
-- Step by Step Application
-- Date: 2025-01-17

-- ===== الخطوة 1: إيقاف RLS مؤقتاً =====
SELECT 'الخطوة 1: إيقاف Row Level Security مؤقتاً...' as step;

-- إيق<PERSON><PERSON> RLS على الجداول الموجودة
DO $$
BEGIN
    -- التحقق من وجود الجداول قبل إيقاف RLS
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
        RAISE NOTICE 'تم إيقاف RLS على جدول users';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'photos' AND table_schema = 'public') THEN
        ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
        RAISE NOTICE 'تم إيقاف RLS على جدول photos';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos' AND table_schema = 'public') THEN
        ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;
        RAISE NOTICE 'تم إيقاف RLS على جدول videos';
    END IF;
END $$;

SELECT '✅ تم إيقاف RLS مؤقتاً' as status;

-- ===== الخطوة 2: إنشاء جدول المواقع =====
SELECT 'الخطوة 2: إنشاء جدول المواقع...' as step;

CREATE TABLE IF NOT EXISTS public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    location_code TEXT UNIQUE NOT NULL,
    location_type TEXT NOT NULL CHECK (location_type IN ('U', 'C')),
    location_number TEXT NOT NULL,
    location_name_ar TEXT,
    location_name_en TEXT,
    sort_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إيقاف RLS على جدول المواقع
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

SELECT '✅ تم إنشاء جدول المواقع' as status;

-- ===== الخطوة 3: إضافة المواقع الـ 70 =====
SELECT 'الخطوة 3: إضافة المواقع الـ 70...' as step;

-- حذف المواقع الموجودة
DELETE FROM public.locations;

-- إضافة مواقع U (U101 - U125)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'U' || LPAD(i::text, 3, '0'),
    'U',
    LPAD(i::text, 3, '0'),
    'موقع U' || LPAD(i::text, 3, '0'),
    'Location U' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 125) as i
ON CONFLICT (location_code) DO NOTHING;

-- إضافة مواقع C (C101 - C145)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'C' || LPAD(i::text, 3, '0'),
    'C',
    LPAD(i::text, 3, '0'),
    'موقع C' || LPAD(i::text, 3, '0'),
    'Location C' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 145) as i
ON CONFLICT (location_code) DO NOTHING;

SELECT '✅ تم إضافة ' || COUNT(*) || ' موقع' as locations_added FROM public.locations;

-- ===== الخطوة 4: إضافة الحقول الجديدة =====
SELECT 'الخطوة 4: إضافة الحقول الجديدة...' as step;

-- للصور
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'photos' AND table_schema = 'public') THEN
        ALTER TABLE public.photos 
        ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
        ADD COLUMN IF NOT EXISTS location_number TEXT,
        ADD COLUMN IF NOT EXISTS full_location_code TEXT,
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
        ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0;
        RAISE NOTICE 'تم تحديث جدول photos';
    END IF;
END $$;

-- للفيديوهات
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos' AND table_schema = 'public') THEN
        ALTER TABLE public.videos 
        ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
        ADD COLUMN IF NOT EXISTS location_number TEXT,
        ADD COLUMN IF NOT EXISTS full_location_code TEXT,
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
        ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0,
        ADD COLUMN IF NOT EXISTS duration_seconds INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS resolution TEXT;
        RAISE NOTICE 'تم تحديث جدول videos';
    END IF;
END $$;

SELECT '✅ تم إضافة الحقول الجديدة' as status;

-- ===== الخطوة 5: إنشاء الفهارس =====
SELECT 'الخطوة 5: إنشاء الفهارس المحسنة...' as step;

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_active ON public.photos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_location_date ON public.photos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_upload_date ON public.photos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس الفيديوهات
CREATE INDEX IF NOT EXISTS idx_videos_user_active ON public.videos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_location_date ON public.videos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_upload_date ON public.videos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس المواقع
CREATE INDEX IF NOT EXISTS idx_locations_type_sort ON public.locations(location_type, sort_order);
CREATE INDEX IF NOT EXISTS idx_locations_active ON public.locations(is_active) WHERE is_active = true;

SELECT '✅ تم إنشاء الفهارس المحسنة' as status;

-- ===== الخطوة 6: إنشاء الدوال =====
SELECT 'الخطوة 6: إنشاء الدوال المحسنة...' as step;

-- دالة تحديث إحصائيات موقع واحد
CREATE OR REPLACE FUNCTION update_location_statistics(location_code_param TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE public.locations 
    SET 
        total_photos = (
            SELECT COUNT(*) FROM public.photos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        total_videos = (
            SELECT COUNT(*) FROM public.videos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        last_used_at = GREATEST(
            (SELECT MAX(capture_timestamp) FROM public.photos WHERE full_location_code = location_code_param),
            (SELECT MAX(capture_timestamp) FROM public.videos WHERE full_location_code = location_code_param)
        ),
        updated_at = NOW()
    WHERE location_code = location_code_param;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث جميع الإحصائيات
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR location_rec IN SELECT location_code FROM public.locations WHERE is_active = true
    LOOP
        PERFORM update_location_statistics(location_rec.location_code);
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع';
END;
$$ LANGUAGE plpgsql;

-- دالة الحصول على المواقع مع الإحصائيات
CREATE OR REPLACE FUNCTION get_locations_with_stats()
RETURNS TABLE(
    location_code TEXT,
    location_type TEXT,
    location_name_ar TEXT,
    location_name_en TEXT,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.location_code,
        l.location_type,
        l.location_name_ar,
        l.location_name_en,
        l.total_photos,
        l.total_videos,
        (l.total_photos + l.total_videos) as total_files,
        l.last_used_at,
        l.is_active
    FROM public.locations l
    WHERE l.is_active = true
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

SELECT '✅ تم إنشاء الدوال المحسنة' as status;

-- ===== الخطوة 7: تحديث البيانات الموجودة =====
SELECT 'الخطوة 7: تحديث البيانات الموجودة...' as step;

-- تحديث full_location_code للصور
UPDATE public.photos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL AND full_location_code IS NULL;

-- تحديث full_location_code للفيديوهات
UPDATE public.videos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL AND full_location_code IS NULL;

SELECT '✅ تم تحديث البيانات الموجودة' as status;

-- ===== الخطوة 8: إعادة تفعيل RLS =====
SELECT 'الخطوة 8: إعادة تفعيل Row Level Security...' as step;

-- إعادة تفعيل RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- إنشاء السياسات الآمنة
DROP POLICY IF EXISTS users_secure_policy ON public.users;
CREATE POLICY users_secure_policy ON public.users FOR ALL USING (auth.uid() = id);

DROP POLICY IF EXISTS photos_secure_policy ON public.photos;
CREATE POLICY photos_secure_policy ON public.photos FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS videos_secure_policy ON public.videos;
CREATE POLICY videos_secure_policy ON public.videos FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS locations_read_policy ON public.locations;
CREATE POLICY locations_read_policy ON public.locations FOR SELECT USING (true);

SELECT '✅ تم إعادة تفعيل RLS الآمن' as status;

-- ===== تقرير النتائج النهائي =====
SELECT '🎉 تم تطبيق جميع التحسينات بنجاح!' as final_status;
SELECT COUNT(*) as total_locations FROM public.locations;
SELECT COUNT(*) as total_photos FROM public.photos;
SELECT COUNT(*) as total_videos FROM public.videos;
