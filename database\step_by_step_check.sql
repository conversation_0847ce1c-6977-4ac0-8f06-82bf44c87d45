-- ===== فحص قاعدة البيانات خطوة بخطوة =====
-- شغل كل استعلام منفصل في Supabase Dashboard

-- الخطوة 1: فحص جميع الجداول الموجودة (كل المخططات)
SELECT
    table_schema as "المخطط",
    table_name as "اسم الجدول",
    table_type as "نوع الجدول"
FROM information_schema.tables
WHERE table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
ORDER BY table_schema, table_name;

-- الخطوة 2: فحص عدد السجلات في كل جدول رئيسي
SELECT 'users' as "الجدول", COUNT(*) as "عدد السجلات" FROM public.users
UNION ALL
SELECT 'photos', COUNT(*) FROM public.photos
UNION ALL
SELECT 'videos', COUNT(*) FROM public.videos
UNION ALL
SELECT 'devices', COUNT(*) FROM public.devices
UNION ALL
SELECT 'locations', COUNT(*) FROM public.locations
UNION ALL
SELECT 'user_sessions', COUNT(*) FROM public.user_sessions
UNION ALL
SELECT 'user_activity_log', COUNT(*) FROM public.user_activity_log
UNION ALL
SELECT 'upload_queue', COUNT(*) FROM public.upload_queue
UNION ALL
SELECT 'auth_users', COUNT(*) FROM auth.users
UNION ALL
SELECT 'auth_sessions', COUNT(*) FROM auth.sessions
UNION ALL
SELECT 'storage_objects', COUNT(*) FROM storage.objects
UNION ALL
SELECT 'storage_buckets', COUNT(*) FROM storage.buckets
ORDER BY "الجدول";

-- الخطوة 3: فحص تفاصيل المستخدمين
SELECT
    id,
    username as "اسم المستخدم",
    email as "البريد الإلكتروني",
    created_at as "تاريخ الإنشاء",
    is_active as "نشط"
FROM public.users
ORDER BY created_at DESC;

-- الخطوة 4: فحص الصور مع تفاصيلها
SELECT
    p.id,
    p.user_id,
    u.username as "المستخدم",
    p.filename as "اسم الملف",
    p.file_size as "حجم الملف",
    p.upload_status as "حالة الرفع",
    p.created_at as "تاريخ الرفع",
    l.name as "الموقع"
FROM public.photos p
LEFT JOIN public.users u ON p.user_id = u.id
LEFT JOIN public.locations l ON p.location_id = l.id
ORDER BY p.created_at DESC;

-- الخطوة 5: فحص الفيديوهات مع تفاصيلها
SELECT
    v.id,
    v.user_id,
    u.username as "المستخدم",
    v.filename as "اسم الملف",
    v.file_size as "حجم الملف",
    v.duration as "المدة",
    v.upload_status as "حالة الرفع",
    v.created_at as "تاريخ الرفع",
    l.name as "الموقع"
FROM public.videos v
LEFT JOIN public.users u ON v.user_id = u.id
LEFT JOIN public.locations l ON v.location_id = l.id
ORDER BY v.created_at DESC;

-- الخطوة 6: فحص الأجهزة المسجلة
SELECT
    d.id,
    d.user_id,
    u.username as "المستخدم",
    d.device_id as "معرف الجهاز",
    d.device_model as "موديل الجهاز",
    d.os_version as "إصدار النظام",
    d.app_version as "إصدار التطبيق",
    d.is_active as "نشط",
    d.last_seen as "آخر ظهور",
    d.created_at as "تاريخ التسجيل"
FROM public.devices d
LEFT JOIN public.users u ON d.user_id = u.id
ORDER BY d.last_seen DESC;

-- الخطوة 7: فحص المواقع
SELECT
    id,
    name as "اسم الموقع",
    latitude as "خط العرض",
    longitude as "خط الطول",
    address as "العنوان",
    created_at as "تاريخ الإضافة"
FROM public.locations
ORDER BY created_at DESC
LIMIT 20;

-- الخطوة 8: فحص طابور الرفع
SELECT
    uq.id,
    uq.user_id,
    u.username as "المستخدم",
    uq.file_type as "نوع الملف",
    uq.file_path as "مسار الملف",
    uq.status as "الحالة",
    uq.retry_count as "عدد المحاولات",
    uq.created_at as "تاريخ الإضافة",
    uq.processed_at as "تاريخ المعالجة"
FROM public.upload_queue uq
LEFT JOIN public.users u ON uq.user_id = u.id
ORDER BY uq.created_at DESC;

-- الخطوة 9: فحص سجل النشاط
SELECT
    ual.id,
    ual.user_id,
    u.username as "المستخدم",
    ual.action as "الإجراء",
    ual.details as "التفاصيل",
    ual.ip_address as "عنوان IP",
    ual.user_agent as "متصفح/تطبيق",
    ual.created_at as "التاريخ"
FROM public.user_activity_log ual
LEFT JOIN public.users u ON ual.user_id = u.id
ORDER BY ual.created_at DESC
LIMIT 50;

-- الخطوة 10: فحص جلسات المستخدمين
SELECT
    us.id,
    us.user_id,
    u.username as "المستخدم",
    us.device_id,
    d.device_model as "الجهاز",
    us.session_token as "رمز الجلسة",
    us.is_active as "نشط",
    us.created_at as "بداية الجلسة",
    us.expires_at as "انتهاء الجلسة",
    us.last_activity as "آخر نشاط"
FROM public.user_sessions us
LEFT JOIN public.users u ON us.user_id = u.id
LEFT JOIN public.devices d ON us.device_id = d.id
ORDER BY us.last_activity DESC;

-- الخطوة 11: فحص ملفات التخزين
SELECT
    id,
    name as "اسم الملف",
    bucket_id as "المجلد",
    owner as "المالك",
    created_at as "تاريخ الرفع",
    updated_at as "تاريخ التحديث",
    last_accessed_at as "آخر وصول",
    metadata as "البيانات الوصفية"
FROM storage.objects
ORDER BY created_at DESC
LIMIT 20;

-- الخطوة 12: فحص مجلدات التخزين
SELECT
    id,
    name as "اسم المجلد",
    owner as "المالك",
    created_at as "تاريخ الإنشاء",
    updated_at as "تاريخ التحديث",
    public as "عام"
FROM storage.buckets
ORDER BY created_at DESC;
