-- 🔧 إصلاح شامل وآمن لجميع الصلاحيات - الإصدار المحدث
-- Complete Safe Permissions Fix - Updated Version
-- Date: 2025-01-19
-- يحذف جميع السياسات الموجودة ويعيد إنشاءها

-- ===== 🗑️ حذف جميع السياسات الموجودة بأمان =====

-- 1. حذف سياسات الجداول الرئيسية
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    -- حذف جميع سياسات جدول photos
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'photos' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.photos';
    END LOOP;
    
    -- حذ<PERSON> جميع سياسات جدول videos
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'videos' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.videos';
    END LOOP;
    
    -- حذف جميع سياسات جدول users
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'users' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.users';
    END LOOP;
    
    -- حذف جميع سياسات جدول devices
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'devices' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.devices';
    END LOOP;
    
    -- حذف جميع سياسات جدول locations
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'locations' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.locations';
    END LOOP;
END $$;

-- 2. حذف سياسات Storage
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    -- حذف جميع سياسات storage.objects
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON storage.objects';
    END LOOP;
    
    -- حذف جميع سياسات storage.buckets
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'buckets' AND schemaname = 'storage'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON storage.buckets';
    END LOOP;
END $$;

-- ===== ✅ إنشاء سياسات جديدة مرنة =====

-- 3. سياسات الجداول الرئيسية
CREATE POLICY "photos_full_access" ON public.photos
    FOR ALL
    USING (auth.uid() IS NOT NULL);

CREATE POLICY "videos_full_access" ON public.videos
    FOR ALL
    USING (auth.uid() IS NOT NULL);

CREATE POLICY "users_full_access" ON public.users
    FOR ALL
    USING (auth.uid() IS NOT NULL);

CREATE POLICY "devices_full_access" ON public.devices
    FOR ALL
    USING (auth.uid() IS NOT NULL);

CREATE POLICY "locations_read_access" ON public.locations
    FOR SELECT
    USING (true);

-- 4. سياسات Storage للصور
CREATE POLICY "photos_storage_upload" ON storage.objects
    FOR INSERT
    WITH CHECK (
        bucket_id = 'photos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "photos_storage_view" ON storage.objects
    FOR SELECT
    USING (
        bucket_id = 'photos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "photos_storage_update" ON storage.objects
    FOR UPDATE
    USING (
        bucket_id = 'photos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "photos_storage_delete" ON storage.objects
    FOR DELETE
    USING (
        bucket_id = 'photos' AND 
        auth.uid() IS NOT NULL
    );

-- 5. سياسات Storage للفيديو
CREATE POLICY "videos_storage_upload" ON storage.objects
    FOR INSERT
    WITH CHECK (
        bucket_id = 'videos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "videos_storage_view" ON storage.objects
    FOR SELECT
    USING (
        bucket_id = 'videos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "videos_storage_update" ON storage.objects
    FOR UPDATE
    USING (
        bucket_id = 'videos' AND 
        auth.uid() IS NOT NULL
    );

CREATE POLICY "videos_storage_delete" ON storage.objects
    FOR DELETE
    USING (
        bucket_id = 'videos' AND 
        auth.uid() IS NOT NULL
    );

-- ===== 🔧 إعدادات إضافية =====

-- 6. تفعيل RLS على جميع الجداول
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- 7. منح صلاحيات للمستخدمين المصادق عليهم
GRANT ALL ON public.photos TO authenticated;
GRANT ALL ON public.videos TO authenticated;
GRANT ALL ON public.users TO authenticated;
GRANT ALL ON public.devices TO authenticated;
GRANT SELECT ON public.locations TO authenticated;

-- 8. منح صلاحيات للمستخدمين المجهولين (للقراءة فقط)
GRANT SELECT ON public.locations TO anon;

-- ===== ✅ رسالة النجاح =====
DO $$ 
BEGIN
    RAISE NOTICE '🎉 تم تطبيق إصلاحات الصلاحيات بنجاح!';
    RAISE NOTICE '✅ جميع السياسات تم حذفها وإعادة إنشاءها';
    RAISE NOTICE '🔐 Row Level Security مفعل على جميع الجداول';
    RAISE NOTICE '🚀 التطبيق جاهز للاستخدام الآن';
END $$;
