import 'package:flutter_riverpod/flutter_riverpod.dart' hide Provider;
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:logger/logger.dart';
import '../errors/app_exceptions.dart';
import '../errors/error_handler.dart';
import 'device_service.dart';
import 'enhanced_device_auth_service.dart';
import 'persistent_auth_service.dart';
import 'session_manager.dart';

final authServiceProvider = StateProvider<AuthService>((ref) => AuthService());

class AuthService {
  final _supabaseClient = supabase.Supabase.instance.client;
  final _logger = Logger();
  final _sessionManager = SessionManager();
  // SessionService الآن مدمج في SessionManager

  final _enhancedDeviceAuth = EnhancedDeviceAuthService();



  Future<void> signIn({
    required String nationalId,
    required String password,
  }) async {
    try {
      final email = '$<EMAIL>';
      _logger.i('Attempting to sign in with email: $email');

      // Sign in with email and password first
      final authResponse = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      _logger.i('Auth response received: ${authResponse.user?.id}');

      if (authResponse.user == null) {
        throw const AuthException('فشل في تسجيل الدخول', code: 'login_failed');
      }

      // التحقق من الجهاز باستخدام النظام المحسن
      final userId = authResponse.user?.id;
      _logger.i('User ID: $userId');

      if (userId == null) {
        throw const AuthException('خطأ في معرف المستخدم', code: 'user_id_null');
      }

      _logger.i('بدء التحقق من الجهاز باستخدام النظام المحسن للمستخدم: $userId');

      // الحصول على بصمة الجهاز
      final deviceService = DeviceService();
      final deviceInfo = await deviceService.getDeviceInfo();

      _logger.i('Raw device info: $deviceInfo');

      final deviceFingerprint = deviceInfo['fingerprint'] ?? 'unknown';
      final androidId = deviceInfo['android_id'] ?? 'unknown';

      _logger.i('Device fingerprint: $deviceFingerprint');
      _logger.i('Android ID: $androidId');

      // التحقق من الجهاز في قاعدة البيانات أولاً
      final databaseCheckResult = await _verifyDeviceInDatabase(
        userId,
        deviceFingerprint,
        androidId,
        deviceInfo
      );

      if (databaseCheckResult['success'] != true) {
        final errorCode = databaseCheckResult['error_code'] ?? 'device_unauthorized';

        // إذا كان الجهاز غير موجود، حاول تسجيله تلقائياً
        if (errorCode == 'DEVICE_NOT_FOUND') {
          _logger.i('الجهاز غير مسجل - محاولة تسجيله تلقائياً...');

          final registerResult = await _registerDeviceInDatabase(
            userId,
            deviceFingerprint,
            androidId,
            deviceInfo
          );

          if (registerResult['success'] == true) {
            _logger.i('تم تسجيل الجهاز تلقائياً بنجاح');
          } else {
            await signOut();
            final message = registerResult['message'] ?? 'فشل في تسجيل الجهاز';
            _logger.e('فشل في تسجيل الجهاز تلقائياً: $message');
            throw AuthException(message, code: 'device_registration_failed');
          }
        } else {
          await signOut();
          final message = databaseCheckResult['message'] ?? 'هذا الجهاز غير مصرح له بالدخول';
          _logger.e('Database device verification failed: $errorCode - $message');
          throw AuthException(message, code: errorCode);
        }
      }

      _logger.i('تم التحقق من الجهاز بنجاح في قاعدة البيانات');

      // التحقق من الجهاز محلياً أيضاً (للتوافق مع النظام القديم)
      final deviceAuthResult = await _enhancedDeviceAuth.verifyDevice(userId);

      if (!deviceAuthResult.success) {
        if (deviceAuthResult.isNewDevice) {
          // جهاز جديد - محاولة ربطه محلياً
          _logger.i('جهاز جديد - محاولة ربطه بالحساب محلياً');
          final bindResult = await _enhancedDeviceAuth.bindAccountToDevice(userId);

          if (!bindResult.success) {
            _logger.w('فشل ربط الجهاز محلياً: ${bindResult.message}');
            // لا نرمي خطأ هنا لأن التحقق من قاعدة البيانات نجح
          } else {
            _logger.i('تم ربط الجهاز الجديد محلياً بنجاح - مستوى الثقة: ${bindResult.trustLevel}');
          }
        }
      }

      _logger.i('تم التحقق من الجهاز بنجاح - مستوى الثقة: ${deviceAuthResult.trustLevel}');

      // حفظ معلومات الجهاز في قاعدة البيانات (اختياري)
      await _saveDeviceToDatabase(userId, deviceAuthResult);

      // التأكد من وجود المستخدم في قاعدة البيانات
      await _ensureUserExists(userId, nationalId, email);

      // تحديث آخر تسجيل دخول للمستخدم
      await _updateUserLastLogin(userId);

      // حفظ بيانات المستخدم محلياً للعمل بدون إنترنت
      await _saveUserDataLocally(userId);

      // بدء جلسة جديدة لتتبع المستخدمين المتصلين
      final sessionId = await _sessionManager.startSession(
        userId: userId,
        deviceId: deviceAuthResult.fingerprint, // استخدام fingerprint كمعرف الجهاز
      );

      _logger.i('Login successful for user: $userId');
      _logger.i('Session started: $sessionId');
      _logger.i('Current user session: ${_supabaseClient.auth.currentUser?.id}');

      // ✅ SessionManager الآن يتولى بدء Live Tracking تلقائياً

      // 🔍 طباعة USER_ID للاختبار (احذف هذا لاحقاً)
      print('🆔 USER_ID للاختبار: $userId');
      print('📧 البريد الإلكتروني: $email');
      print('🆔 الرقم الوطني: $nationalId');
    } catch (e) {
      _logger.e('Login error: $e');

      // معالجة خاصة لمشكلة المفتاح
      if (e.toString().contains('Invalid API key') ||
          e.toString().contains('401') ||
          e.toString().contains('Unauthorized')) {
        _logger.e('⚠️ API key configuration issue detected');
        throw const AuthException(
          'خطأ في إعدادات النظام. يرجى التواصل مع المطور.',
          code: 'api_key_error'
        );
      }

      if (e is AuthException) {
        rethrow;
      }

      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  Future<void> signOut() async {
    try {
      _logger.i('بدء عملية تسجيل الخروج...');

      // إنهاء الجلسة الحالية (يتضمن إيقاف Live Tracking تلقائياً)
      await _sessionManager.endSession(reason: 'logout');

      // تسجيل الخروج من النظام المحلي (لكن احتفظ بمعرف الجهاز)
      final persistentAuth = PersistentAuthService();
      await persistentAuth.logout();

      // تسجيل الخروج من Supabase
      await _supabaseClient.auth.signOut();

      _logger.i('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _logger.e('خطأ في تسجيل الخروج: $e');

      // حتى لو فشل تسجيل الخروج من الخادم، امسح البيانات المحلية
      try {
        final persistentAuth = PersistentAuthService();
        await persistentAuth.logout();
      } catch (localError) {
        _logger.e('خطأ في مسح البيانات المحلية: $localError');
      }

      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  /// تسجيل الجهاز في قاعدة البيانات
  Future<Map<String, dynamic>> _registerDeviceInDatabase(
    String userId,
    String deviceFingerprint,
    String androidId,
    Map<String, dynamic> deviceInfo,
  ) async {
    try {
      _logger.i('تسجيل الجهاز في قاعدة البيانات...');

      final result = await _supabaseClient.rpc('check_and_register_device', params: {
        'p_user_id': userId,
        'p_device_fingerprint': deviceFingerprint,
        'p_android_id': androidId,
        'p_device_name': deviceInfo['deviceName'] ?? 'Unknown Device',
        'p_device_model': deviceInfo['deviceModel'] ?? 'Unknown Model',
        'p_device_brand': deviceInfo['deviceBrand'] ?? 'Unknown Brand',
        'p_hardware_info': deviceInfo['hardware'] ?? 'Unknown Hardware',
        'p_build_fingerprint': deviceInfo['build_fingerprint'] ?? 'Unknown Build',
      });

      _logger.i('نتيجة تسجيل الجهاز: $result');

      if (result != null && result['success'] == true) {
        return {'success': true, 'message': 'تم تسجيل الجهاز بنجاح'};
      } else {
        return {
          'success': false,
          'error_code': result?['error_code'] ?? 'REGISTRATION_FAILED',
          'message': result?['message'] ?? 'فشل في تسجيل الجهاز'
        };
      }
    } catch (e) {
      _logger.e('خطأ في تسجيل الجهاز: $e');
      return {
        'success': false,
        'error_code': 'REGISTRATION_ERROR',
        'message': 'خطأ في تسجيل الجهاز: $e'
      };
    }
  }

  /// التحقق من الجهاز في قاعدة البيانات
  Future<Map<String, dynamic>> _verifyDeviceInDatabase(
    String userId,
    String deviceFingerprint,
    String androidId,
    Map<String, dynamic> deviceInfo,
  ) async {
    try {
      _logger.i('التحقق من الجهاز في قاعدة البيانات...');

      // استدعاء دالة التحقق من الجهاز (جهاز واحد فقط)
      final result = await _supabaseClient.rpc('verify_device_single', params: {
        'p_user_id': userId,
        'p_device_fingerprint': deviceFingerprint,
        'p_android_id': androidId,
        'p_device_name': deviceInfo['deviceName'],
        'p_device_model': deviceInfo['deviceModel'],
        'p_device_brand': deviceInfo['deviceBrand'],
      });

      _logger.i('نتيجة التحقق من قاعدة البيانات: $result');

      if (result != null && result['success'] == true) {
        // الجهاز مسجل ومصرح له
        return {
          'success': true,
          'device_id': result['device_id'],
          'trust_level': result['trust_level'],
          'message': result['message'],
        };
      } else if (result != null && result['error_code'] == 'DEVICE_NOT_FOUND') {
        // الجهاز غير مسجل - محاولة تسجيله
        _logger.i('الجهاز غير مسجل - محاولة تسجيله...');

        final registerResult = await _supabaseClient.rpc('check_and_register_device', params: {
          'p_user_id': userId,
          'p_device_fingerprint': deviceFingerprint,
          'p_android_id': androidId,
          'p_device_name': deviceInfo['deviceName'] ?? 'Unknown Device',
          'p_device_model': deviceInfo['deviceModel'] ?? 'Unknown Model',
          'p_device_brand': deviceInfo['deviceBrand'] ?? 'Unknown Brand',
          'p_hardware_info': deviceInfo['hardware'] ?? 'Unknown Hardware',
          'p_build_fingerprint': deviceInfo['build_fingerprint'] ?? 'Unknown Build',
        });

        _logger.i('نتيجة تسجيل الجهاز: $registerResult');

        if (registerResult != null && registerResult['success'] == true) {
          return {
            'success': true,
            'device_id': registerResult['device_id'],
            'status': registerResult['status'],
            'message': registerResult['message'],
          };
        } else {
          return registerResult ?? {
            'success': false,
            'error_code': 'REGISTRATION_FAILED',
            'message': 'فشل في تسجيل الجهاز',
          };
        }
      } else {
        // خطأ في التحقق
        return result ?? {
          'success': false,
          'error_code': 'VERIFICATION_FAILED',
          'message': 'فشل في التحقق من الجهاز',
        };
      }
    } catch (e) {
      _logger.e('خطأ في التحقق من الجهاز في قاعدة البيانات: $e');
      return {
        'success': false,
        'error_code': 'DATABASE_ERROR',
        'message': 'خطأ في الاتصال بقاعدة البيانات: $e',
      };
    }
  }

  /// تحديث آخر تسجيل دخول للمستخدم
  Future<void> _updateUserLastLogin(String userId) async {
    try {
      _logger.i('تحديث آخر تسجيل دخول للمستخدم: $userId');

      await _supabaseClient
          .from('users')
          .update({
            'last_login': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      _logger.i('تم تحديث آخر تسجيل دخول بنجاح');
    } catch (e) {
      _logger.e('خطأ في تحديث آخر تسجيل دخول: $e');
      // لا نرمي خطأ هنا لأن هذا ليس ضروري لتسجيل الدخول
    }
  }

  /// التأكد من وجود المستخدم في قاعدة البيانات
  Future<void> _ensureUserExists(String userId, String nationalId, String email) async {
    try {
      final result = await _supabaseClient.rpc('create_user_if_not_exists', params: {
        'p_user_id': userId,
        'p_national_id': nationalId,
        'p_email': email,
      });

      _logger.i('User existence check result: $result');
    } catch (e) {
      _logger.e('Error ensuring user exists: $e');
      // لا نرمي خطأ هنا لأن تسجيل الدخول نجح
    }
  }

  /// حفظ بيانات المستخدم محلياً
  Future<void> _saveUserDataLocally(String userId) async {
    try {
      final response = await _supabaseClient
          .from('users')
          .select('full_name')
          .eq('id', userId)
          .single();

      final persistentAuth = PersistentAuthService();
      await persistentAuth.saveUserData({
        'id': userId,
        'full_name': response['full_name'],
        'login_time': DateTime.now().toIso8601String(),
      });

      _logger.i('User data saved locally for offline access');
    } catch (e) {
      _logger.e('Error saving user data locally: $e');
      // لا نرمي خطأ هنا لأن تسجيل الدخول نجح
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(email);
    } catch (e) {
      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }

  /// Get current user's devices
  Future<List<Map<String, dynamic>>> getCurrentUserDevices() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw const AuthException(
          'المستخدم غير مسجل الدخول',
          code: 'user_not_authenticated',
        );
      }

      final response = await _supabaseClient
          .from('devices')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      _logger.e('Error getting user devices: $e');
      final appError = ErrorHandler.handleError(e);
      throw appError;
    }
  }



  /// حفظ معلومات الجهاز في قاعدة البيانات باستخدام النظام الجديد
  Future<void> _saveDeviceToDatabase(String userId, DeviceAuthResult deviceResult) async {
    try {
      if (deviceResult.fingerprintData == null) return;

      final deviceData = deviceResult.fingerprintData!;

      // استخدام دالة التحقق من الجهاز (جهاز واحد فقط)
      final result = await _supabaseClient.rpc('verify_device_single', params: {
        'p_user_id': userId,
        'p_device_fingerprint': deviceResult.fingerprint,
        'p_android_id': deviceData.androidId,
        'p_device_name': '${deviceData.manufacturer} ${deviceData.deviceModel}',
        'p_device_model': deviceData.deviceModel,
        'p_device_brand': deviceData.manufacturer,
      });

      if (result != null && result['status'] == 'success') {
        final action = result['action'];
        final deviceId = result['device_id'];

        if (action == 'created') {
          _logger.i('تم حفظ الجهاز الجديد في قاعدة البيانات - ID: $deviceId');
        } else if (action == 'updated') {
          _logger.i('تم تحديث معلومات الجهاز في قاعدة البيانات - ID: $deviceId');
        }
      } else if (result != null && result['status'] == 'error') {
        final errorCode = result['code'];
        final message = result['message'];

        if (errorCode == 'DEVICE_LIMIT_REACHED') {
          _logger.w('تم الوصول للحد الأقصى من الأجهزة: $message');

          // منع تسجيل الدخول من جهاز مختلف
          await _supabaseClient.auth.signOut();
          throw const AuthException(
            'هذا الحساب مرتبط بجهاز آخر. لا يمكن استخدامه على هذا الجهاز.',
            code: 'device_not_authorized'
          );
        } else {
          _logger.e('خطأ في حفظ الجهاز: $message');

          // منع تسجيل الدخول في حالة أي خطأ في الجهاز
          await _supabaseClient.auth.signOut();
          throw const AuthException(
            'خطأ في التحقق من الجهاز. يرجى التواصل مع المطور.',
            code: 'device_verification_failed'
          );
        }
      }

    } catch (e) {
      _logger.e('خطأ في حفظ معلومات الجهاز: $e');
      // لا نرمي خطأ هنا لأن المصادقة نجحت
    }
  }



  /// الحصول على قائمة أجهزة المستخدم
  Future<Map<String, dynamic>?> getUserDevices() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        _logger.w('لا يوجد مستخدم مسجل دخول');
        return null;
      }

      final result = await _supabaseClient.rpc('get_user_devices', params: {
        'p_user_id': user.id,
      });

      if (result != null && result['status'] == 'success') {
        _logger.i('تم الحصول على قائمة الأجهزة بنجاح - عدد الأجهزة: ${result['device_count']}');
        return result;
      } else {
        _logger.e('فشل في الحصول على قائمة الأجهزة');
        return null;
      }
    } catch (e) {
      _logger.e('خطأ في الحصول على قائمة الأجهزة: $e');
      return null;
    }
  }

  /// حذف جهاز
  Future<bool> removeDevice(String deviceId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        _logger.w('لا يوجد مستخدم مسجل دخول');
        return false;
      }

      final result = await _supabaseClient.rpc('remove_device', params: {
        'p_user_id': user.id,
        'p_device_id': deviceId,
      });

      if (result != null && result['status'] == 'success') {
        _logger.i('تم حذف الجهاز بنجاح: ${result['message']}');
        return true;
      } else {
        final message = result?['message'] ?? 'فشل في حذف الجهاز';
        _logger.e('فشل في حذف الجهاز: $message');
        return false;
      }
    } catch (e) {
      _logger.e('خطأ في حذف الجهاز: $e');
      return false;
    }
  }

  /// تسجيل محاولة مصادقة فاشلة
  Future<void> recordFailedAuth(String deviceFingerprint, String androidId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) return;

      final result = await _supabaseClient.rpc('record_failed_auth', params: {
        'p_user_id': user.id,
        'p_device_fingerprint': deviceFingerprint,
        'p_android_id': androidId,
      });

      if (result != null) {
        final status = result['status'];
        final message = result['message'];

        if (status == 'blocked') {
          _logger.w('تم حجب الجهاز: $message');
        } else if (status == 'failed') {
          final attempts = result['attempts'];
          final remaining = result['remaining_attempts'];
          _logger.w('محاولة مصادقة فاشلة - المحاولات: $attempts، المتبقي: $remaining');
        }
      }
    } catch (e) {
      _logger.e('خطأ في تسجيل المحاولة الفاشلة: $e');
    }
  }
}
