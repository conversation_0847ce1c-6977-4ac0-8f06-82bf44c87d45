-- 🔧 الدوال والسياسات المثالية لتطبيق Moon Memory
-- Perfect Functions and Policies for Moon Memory App
-- Date: 2025-01-15
-- Version: 2.0 - Clean & Perfect

-- ===== 🔐 تفعيل Row Level Security =====
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_stats ENABLE ROW LEVEL SECURITY;

-- ===== 🛡️ إنشاء السياسات الأمنية =====

-- سياسات المستخدمين (يمكن للمستخدم رؤية بياناته والمشرف يرى الكل)
DROP POLICY IF EXISTS users_policy ON public.users;
CREATE POLICY users_policy ON public.users
    FOR ALL
    USING (
        auth.uid() = id OR 
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسات الأجهزة (نفس المنطق)
DROP POLICY IF EXISTS devices_policy ON public.devices;
CREATE POLICY devices_policy ON public.devices
    FOR ALL
    USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسات الصور (نفس المنطق)
DROP POLICY IF EXISTS photos_policy ON public.photos;
CREATE POLICY photos_policy ON public.photos
    FOR ALL
    USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسات الفيديو (نفس المنطق)
DROP POLICY IF EXISTS videos_policy ON public.videos;
CREATE POLICY videos_policy ON public.videos
    FOR ALL
    USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسات الجلسات (نفس المنطق)
DROP POLICY IF EXISTS sessions_policy ON public.user_sessions;
CREATE POLICY sessions_policy ON public.user_sessions
    FOR ALL
    USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسات سجلات الإدارة (للمشرفين فقط)
DROP POLICY IF EXISTS admin_logs_policy ON public.admin_logs;
CREATE POLICY admin_logs_policy ON public.admin_logs
    FOR ALL
    USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE));

-- سياسات الإحصائيات (للمشرفين فقط)
DROP POLICY IF EXISTS stats_policy ON public.system_stats;
CREATE POLICY stats_policy ON public.system_stats
    FOR ALL
    USING (EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE));

-- ===== 🔧 إنشاء الدوال المساعدة =====

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الحقل المُحسوب للصور
CREATE OR REPLACE FUNCTION update_photo_location_code()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث الحقل المُحسوب
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.full_location_code := NEW.location_type || NEW.location_number;
        
        -- تحديث ترقيم الفرز التلقائي
        IF NEW.sort_order IS NULL THEN
            NEW.sort_order := (
                SELECT COALESCE(MAX(sort_order), 0) + 1
                FROM public.photos 
                WHERE location_type = NEW.location_type 
                AND location_number = NEW.location_number
                AND user_id = NEW.user_id
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الحقل المُحسوب للفيديو
CREATE OR REPLACE FUNCTION update_video_location_code()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث الحقل المُحسوب
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.full_location_code := NEW.location_type || NEW.location_number;
        
        -- تحديث ترقيم الفرز التلقائي
        IF NEW.sort_order IS NULL THEN
            NEW.sort_order := (
                SELECT COALESCE(MAX(sort_order), 0) + 1
                FROM public.videos 
                WHERE location_type = NEW.location_type 
                AND location_number = NEW.location_number
                AND user_id = NEW.user_id
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الإحصائيات اليومية
CREATE OR REPLACE FUNCTION update_daily_stats()
RETURNS VOID AS $$
DECLARE
    today_date DATE := CURRENT_DATE;
BEGIN
    INSERT INTO public.system_stats (
        stat_date,
        total_users,
        active_users,
        new_users_today,
        total_devices,
        active_devices,
        blocked_devices,
        total_photos,
        total_videos,
        photos_uploaded_today,
        videos_uploaded_today,
        total_storage_used_mb,
        photos_storage_mb,
        videos_storage_mb,
        u_locations_used,
        c_locations_used
    ) VALUES (
        today_date,
        (SELECT COUNT(*) FROM public.users),
        (SELECT COUNT(*) FROM public.users WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM public.users WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM public.devices),
        (SELECT COUNT(*) FROM public.devices WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM public.devices WHERE is_blocked = TRUE),
        (SELECT COUNT(*) FROM public.photos WHERE status = 'active'),
        (SELECT COUNT(*) FROM public.videos WHERE status = 'active'),
        (SELECT COUNT(*) FROM public.photos WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM public.videos WHERE DATE(created_at) = today_date),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM public.photos WHERE status = 'active') +
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM public.videos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM public.photos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM public.videos WHERE status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM public.photos WHERE location_type = 'U' AND status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM public.photos WHERE location_type = 'C' AND status = 'active')
    )
    ON CONFLICT (stat_date) 
    DO UPDATE SET
        total_users = EXCLUDED.total_users,
        active_users = EXCLUDED.active_users,
        new_users_today = EXCLUDED.new_users_today,
        total_devices = EXCLUDED.total_devices,
        active_devices = EXCLUDED.active_devices,
        blocked_devices = EXCLUDED.blocked_devices,
        total_photos = EXCLUDED.total_photos,
        total_videos = EXCLUDED.total_videos,
        photos_uploaded_today = EXCLUDED.photos_uploaded_today,
        videos_uploaded_today = EXCLUDED.videos_uploaded_today,
        total_storage_used_mb = EXCLUDED.total_storage_used_mb,
        photos_storage_mb = EXCLUDED.photos_storage_mb,
        videos_storage_mb = EXCLUDED.videos_storage_mb,
        u_locations_used = EXCLUDED.u_locations_used,
        c_locations_used = EXCLUDED.c_locations_used,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات سريعة
CREATE OR REPLACE FUNCTION get_quick_stats()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'total_users', (SELECT COUNT(*) FROM public.users),
        'active_users', (SELECT COUNT(*) FROM public.users WHERE is_active = TRUE),
        'admin_users', (SELECT COUNT(*) FROM public.users WHERE is_admin = TRUE),
        'total_devices', (SELECT COUNT(*) FROM public.devices),
        'active_devices', (SELECT COUNT(*) FROM public.devices WHERE is_active = TRUE),
        'blocked_devices', (SELECT COUNT(*) FROM public.devices WHERE is_blocked = TRUE),
        'total_photos', (SELECT COUNT(*) FROM public.photos WHERE status = 'active'),
        'total_videos', (SELECT COUNT(*) FROM public.videos WHERE status = 'active'),
        'u_locations', (SELECT COUNT(DISTINCT location_number) FROM public.photos WHERE location_type = 'U'),
        'c_locations', (SELECT COUNT(DISTINCT location_number) FROM public.photos WHERE location_type = 'C'),
        'last_updated', NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- ===== ⚡ تطبيق المحفزات =====

-- محفزات تحديث updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON public.devices;
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON public.devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_photos_updated_at ON public.photos;
CREATE TRIGGER update_photos_updated_at 
    BEFORE UPDATE ON public.photos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_videos_updated_at ON public.videos;
CREATE TRIGGER update_videos_updated_at 
    BEFORE UPDATE ON public.videos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- محفزات تحديث الحقول المُحسوبة
DROP TRIGGER IF EXISTS trigger_update_photo_location_code ON public.photos;
CREATE TRIGGER trigger_update_photo_location_code
    BEFORE INSERT OR UPDATE ON public.photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_location_code();

DROP TRIGGER IF EXISTS trigger_update_video_location_code ON public.videos;
CREATE TRIGGER trigger_update_video_location_code
    BEFORE INSERT OR UPDATE ON public.videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_location_code();

-- ===== 📊 إنشاء الفهارس للأداء =====
CREATE INDEX IF NOT EXISTS idx_users_national_id ON public.users(national_id);
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON public.users(is_admin);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);

CREATE INDEX IF NOT EXISTS idx_devices_user_id ON public.devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_fingerprint ON public.devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON public.devices(android_id);
CREATE INDEX IF NOT EXISTS idx_devices_trust_level ON public.devices(trust_level);

CREATE INDEX IF NOT EXISTS idx_photos_user_id ON public.photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON public.photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON public.photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON public.photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON public.photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_username ON public.photos(username);

CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON public.videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON public.videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON public.videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON public.videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_username ON public.videos(username);

-- فهارس مركبة للترتيب المطلوب
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON public.photos(location_type, location_number, capture_timestamp, username);
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON public.videos(location_type, location_number, capture_timestamp, username);

-- تحديث الإحصائيات الأولية
SELECT update_daily_stats();

-- ===== ✅ رسائل النجاح =====
SELECT '🎉 تم إعداد جميع السياسات والدوال بنجاح!' as status;
SELECT '🚀 النظام مكتمل وجاهز للاستخدام!' as final_message;

-- اختبار الدوال
SELECT '📊 إحصائيات النظام الحالية:' as info;
SELECT get_quick_stats() as current_stats;
