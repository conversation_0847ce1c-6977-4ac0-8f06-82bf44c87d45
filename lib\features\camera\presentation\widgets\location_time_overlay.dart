import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';

class LocationTimeOverlay extends StatelessWidget {
  final Position? currentPosition;
  final String? address;

  const LocationTimeOverlay({
    super.key,
    this.currentPosition,
    this.address,
  });

  String _formatTime(DateTime time) {
    int hour12 = time.hour % 12;
    hour12 = hour12 == 0 ? 12 : hour12;
    
    String hour = hour12.toString().padLeft(2, '0');
    String minute = time.minute.toString().padLeft(2, '0');
    String period = time.hour < 12 ? 'ص' : 'م';
    String dayNightIndicator = (time.hour >= 18 || time.hour < 6) ? '🌙' : '☀️';
    
    return '$hour:$minute $period $dayNightIndicator';
  }

  String _formatDate(DateTime date) {
    final List<String> arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    String day = date.day.toString();
    String month = arabicMonths[date.month - 1];
    String year = date.year.toString();

    return '$day $month $year';
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (address != null) ...[
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.location_on,
                color: Colors.white,
                size: 16,
                shadows: [
                  Shadow(
                    offset: Offset(1, 1),
                    blurRadius: 3,
                    color: Colors.black,
                  ),
                ],
              ),
              const SizedBox(width: 8),
              Text(
                address ?? 'جاري تحديد الموقع...',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      offset: Offset(1, 1),
                      blurRadius: 3,
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        Text(
          '${_formatDate(now)} - ${_formatTime(now)}',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            shadows: const [
              Shadow(
                offset: Offset(1, 1),
                blurRadius: 3,
                color: Colors.black,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
