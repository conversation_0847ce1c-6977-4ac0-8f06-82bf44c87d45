-- 🚨 إصلاح طارئ سريع للصلاحيات
-- Emergency Quick Fix for Permissions
-- Date: 2025-01-19
-- للاستخدام عند وجود أخطاء في السياسات الموجودة

-- ===== 🔓 إلغاء تفعيل RLS مؤقتاً =====
ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

-- ===== 🗑️ حذف جميع السياسات بالقوة =====

-- حذف سياسات الجداول
DROP POLICY IF EXISTS photos_policy ON public.photos;
DROP POLICY IF EXISTS photos_user_policy ON public.photos;
DROP POLICY IF EXISTS photos_secure_policy ON public.photos;
DROP POLICY IF EXISTS photos_open_policy ON public.photos;
DROP POLICY IF EXISTS photos_flexible_policy ON public.photos;
DROP POLICY IF EXISTS photos_full_access ON public.photos;

DROP POLICY IF EXISTS videos_policy ON public.videos;
DROP POLICY IF EXISTS videos_user_policy ON public.videos;
DROP POLICY IF EXISTS videos_secure_policy ON public.videos;
DROP POLICY IF EXISTS videos_flexible_policy ON public.videos;
DROP POLICY IF EXISTS videos_full_access ON public.videos;

DROP POLICY IF EXISTS users_policy ON public.users;
DROP POLICY IF EXISTS users_flexible_policy ON public.users;
DROP POLICY IF EXISTS users_full_access ON public.users;

DROP POLICY IF EXISTS devices_policy ON public.devices;
DROP POLICY IF EXISTS devices_flexible_policy ON public.devices;
DROP POLICY IF EXISTS devices_full_access ON public.devices;

DROP POLICY IF EXISTS locations_read_access ON public.locations;

-- حذف سياسات Storage
DROP POLICY IF EXISTS "Users can upload photos" ON storage.objects;
DROP POLICY IF EXISTS "Users can view photos" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete photos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload photos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to view photos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update photos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete photos" ON storage.objects;
DROP POLICY IF EXISTS "photos_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_view_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_delete_policy" ON storage.objects;
DROP POLICY IF EXISTS "photos_storage_upload" ON storage.objects;
DROP POLICY IF EXISTS "photos_storage_view" ON storage.objects;
DROP POLICY IF EXISTS "photos_storage_update" ON storage.objects;
DROP POLICY IF EXISTS "photos_storage_delete" ON storage.objects;

DROP POLICY IF EXISTS "Users can upload videos" ON storage.objects;
DROP POLICY IF EXISTS "Users can view videos" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete videos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload videos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to view videos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update videos" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete videos" ON storage.objects;
DROP POLICY IF EXISTS "videos_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "videos_view_policy" ON storage.objects;
DROP POLICY IF EXISTS "videos_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "videos_delete_policy" ON storage.objects;
DROP POLICY IF EXISTS "videos_storage_upload" ON storage.objects;
DROP POLICY IF EXISTS "videos_storage_view" ON storage.objects;
DROP POLICY IF EXISTS "videos_storage_update" ON storage.objects;
DROP POLICY IF EXISTS "videos_storage_delete" ON storage.objects;

-- ===== 🔓 منح صلاحيات كاملة مؤقتاً =====
GRANT ALL ON public.photos TO authenticated;
GRANT ALL ON public.videos TO authenticated;
GRANT ALL ON public.users TO authenticated;
GRANT ALL ON public.devices TO authenticated;
GRANT ALL ON public.locations TO authenticated;

GRANT ALL ON public.photos TO anon;
GRANT ALL ON public.videos TO anon;
GRANT ALL ON public.users TO anon;
GRANT ALL ON public.devices TO anon;
GRANT ALL ON public.locations TO anon;

-- ===== ✅ رسالة النجاح =====
DO $$ 
BEGIN
    RAISE NOTICE '🚨 تم تطبيق الإصلاح الطارئ!';
    RAISE NOTICE '🔓 RLS تم إلغاء تفعيله مؤقتاً';
    RAISE NOTICE '🗑️ جميع السياسات تم حذفها';
    RAISE NOTICE '🔓 صلاحيات كاملة ممنوحة مؤقتاً';
    RAISE NOTICE '⚠️ يُنصح بتطبيق complete_safe_fix.sql لاحقاً';
END $$;
