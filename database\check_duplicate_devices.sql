-- فحص الأجهزة المكررة في قاعدة البيانات
-- Check Duplicate Devices in Database

-- 1. فحص المستخدمين الذين لديهم أكثر من جهاز
SELECT 
    u.full_name,
    u.national_id,
    COUNT(d.id) as device_count,
    array_agg(d.device_name) as device_names,
    array_agg(d.device_fingerprint) as fingerprints,
    array_agg(d.created_at) as creation_dates
FROM users u
LEFT JOIN devices d ON u.id = d.user_id
GROUP BY u.id, u.full_name, u.national_id
HAVING COUNT(d.id) > 1
ORDER BY device_count DESC;

-- 2. فحص تفصيلي للأجهزة المكررة
SELECT 
    u.full_name,
    u.national_id,
    d.id as device_id,
    d.device_fingerprint,
    d.android_id,
    d.device_name,
    d.device_model,
    d.trust_level,
    d.is_active,
    d.last_verified_at,
    d.created_at,
    ROW_NUMBER() OVER (PARTITION BY d.user_id ORDER BY d.created_at DESC) as device_rank
FROM devices d
JOIN users u ON d.user_id = u.id
WHERE d.user_id IN (
    SELECT user_id 
    FROM devices 
    GROUP BY user_id 
    HAVING COUNT(*) > 1
)
ORDER BY u.full_name, d.created_at DESC;

-- 3. فحص الأجهزة بنفس البصمة
SELECT 
    device_fingerprint,
    COUNT(*) as duplicate_count,
    array_agg(DISTINCT u.full_name) as users,
    array_agg(d.created_at) as creation_dates
FROM devices d
JOIN users u ON d.user_id = u.id
GROUP BY device_fingerprint
HAVING COUNT(*) > 1;

-- 4. فحص الأجهزة بنفس Android ID
SELECT 
    android_id,
    COUNT(*) as duplicate_count,
    array_agg(DISTINCT u.full_name) as users,
    array_agg(d.device_name) as device_names
FROM devices d
JOIN users u ON d.user_id = u.id
GROUP BY android_id
HAVING COUNT(*) > 1;

-- 5. إحصائيات عامة
SELECT 
    'إجمالي المستخدمين' as metric,
    COUNT(*) as value
FROM users
UNION ALL
SELECT 
    'إجمالي الأجهزة' as metric,
    COUNT(*) as value
FROM devices
UNION ALL
SELECT 
    'المستخدمين بأجهزة متعددة' as metric,
    COUNT(*) as value
FROM (
    SELECT user_id 
    FROM devices 
    GROUP BY user_id 
    HAVING COUNT(*) > 1
) multi_device_users
UNION ALL
SELECT 
    'الأجهزة النشطة' as metric,
    COUNT(*) as value
FROM devices
WHERE is_active = true;
