-- 🧹 تنظيف شامل للبيانات مع الحفاظ على الهيكل
-- COMPLETE DATA CLEANUP - KEEP STRUCTURE ONLY
-- ⚠️ تحذير: هذا السكريبت سيحذف جميع البيانات نهائياً!
-- Date: 2025-01-19

-- ===== 🛑 تأكيد الأمان =====
-- تأكد من أنك تريد حذف جميع البيانات قبل تشغيل هذا السكريبت!

BEGIN;

-- ===== 📊 عرض إحصائيات قبل الحذف =====
SELECT 
    '📊 إحصائيات قبل التنظيف:' as info,
    (SELECT COUNT(*) FROM public.photos) as photos_count,
    (SELECT COUNT(*) FROM public.videos) as videos_count,
    (SELECT COUNT(*) FROM public.users) as users_count,
    (SELECT COUNT(*) FROM public.devices) as devices_count,
    (SELECT COUNT(*) FROM public.locations) as locations_count,
    (SELECT COUNT(*) FROM storage.objects) as storage_files_count;

-- ===== 🗑️ حذف جميع الملفات من Storage =====
-- حذف جميع الملفات من bucket الصور
DELETE FROM storage.objects WHERE bucket_id = 'photos';

-- حذف جميع الملفات من bucket الفيديوهات
DELETE FROM storage.objects WHERE bucket_id = 'videos';

-- حذف أي ملفات من buckets أخرى (إن وجدت)
DELETE FROM storage.objects WHERE bucket_id NOT IN ('photos', 'videos');

-- ===== 🔧 إصلاح Storage Buckets والصلاحيات =====

-- إلغاء تفعيل RLS نهائياً على جميع جداول Storage
ALTER TABLE IF EXISTS storage.objects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS storage.buckets DISABLE ROW LEVEL SECURITY;

-- حذف جميع سياسات Storage
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT schemaname, tablename, policyname
        FROM pg_policies
        WHERE schemaname = 'storage'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) ||
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
END $$;

-- منح صلاحيات كاملة على Storage
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA storage TO authenticated, anon, service_role;

-- تحديث bucket الفيديوهات مع MIME types صحيحة
UPDATE storage.buckets
SET
    public = true,
    file_size_limit = 209715200, -- 200MB
    allowed_mime_types = ARRAY[
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo',
        'video/webm',
        'video/3gpp',
        'video/x-flv',
        'application/octet-stream' -- للملفات العامة
    ]
WHERE id = 'videos';

-- تحديث bucket الصور مع MIME types صحيحة
UPDATE storage.buckets
SET
    public = true,
    file_size_limit = 52428800, -- 50MB
    allowed_mime_types = ARRAY[
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'application/octet-stream' -- للملفات العامة
    ]
WHERE id = 'photos';

-- ===== 🗃️ حذف جميع البيانات من الجداول =====

-- حذف جميع الصور (مع الحفاظ على الجدول)
DELETE FROM public.photos;

-- حذف جميع الفيديوهات (مع الحفاظ على الجدول)
DELETE FROM public.videos;

-- حذف جميع الأجهزة (مع الحفاظ على الجدول)
DELETE FROM public.devices;

-- حذف جميع المستخدمين (مع الحفاظ على الجدول)
DELETE FROM public.users;

-- ⚠️ ملاحظة: جدول المواقع (locations) لن يتم حذفه لأنه يحتوي على بيانات مرجعية
-- إذا كنت تريد حذف المواقع أيضاً، قم بإلغاء التعليق عن السطر التالي:
-- DELETE FROM public.locations;

-- ===== 🔄 إعادة تعيين Auto-increment counters =====
-- إعادة تعيين العدادات التلقائية للجداول

-- إعادة تعيين عداد الصور
ALTER SEQUENCE IF EXISTS public.photos_id_seq RESTART WITH 1;

-- إعادة تعيين عداد الفيديوهات
ALTER SEQUENCE IF EXISTS public.videos_id_seq RESTART WITH 1;

-- إعادة تعيين عداد المستخدمين
ALTER SEQUENCE IF EXISTS public.users_id_seq RESTART WITH 1;

-- إعادة تعيين عداد الأجهزة
ALTER SEQUENCE IF EXISTS public.devices_id_seq RESTART WITH 1;

-- إعادة تعيين عداد المواقع (إذا تم حذفها)
-- ALTER SEQUENCE IF EXISTS public.locations_id_seq RESTART WITH 1;

-- ===== 🧹 تنظيف إضافي =====

-- حذف أي جلسات مصادقة قديمة من Supabase Auth
-- ملاحظة: هذا سيسجل خروج جميع المستخدمين
DELETE FROM auth.sessions;
DELETE FROM auth.refresh_tokens;

-- حذف المستخدمين من نظام المصادقة (اختياري - احذر!)
-- إذا كنت تريد حذف حسابات المستخدمين نهائياً، قم بإلغاء التعليق:
-- DELETE FROM auth.users;

-- ===== ✅ التحقق من النتائج =====
SELECT 
    '✅ إحصائيات بعد التنظيف:' as info,
    (SELECT COUNT(*) FROM public.photos) as photos_count,
    (SELECT COUNT(*) FROM public.videos) as videos_count,
    (SELECT COUNT(*) FROM public.users) as users_count,
    (SELECT COUNT(*) FROM public.devices) as devices_count,
    (SELECT COUNT(*) FROM public.locations) as locations_count,
    (SELECT COUNT(*) FROM storage.objects) as storage_files_count;

-- ===== 📋 التحقق من الهيكل (يجب أن يبقى كما هو) =====
SELECT 
    '📋 الجداول الموجودة (يجب أن تبقى):' as info,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('photos', 'videos', 'users', 'devices', 'locations')
ORDER BY table_name;

-- ===== 🎯 التحقق من Storage Buckets =====
SELECT 
    '🗂️ Storage Buckets (يجب أن تبقى):' as info,
    id as bucket_name,
    name,
    public,
    file_size_limit,
    array_length(allowed_mime_types, 1) as mime_types_count
FROM storage.buckets 
WHERE id IN ('photos', 'videos')
ORDER BY id;

-- ===== 🎉 رسالة النجاح =====
SELECT 
    '🎉 تم تنظيف البيانات بنجاح!' as status,
    'جميع الصور والفيديوهات: محذوفة ✅' as storage_status,
    'جميع بيانات الجداول: محذوفة ✅' as tables_status,
    'الهيكل والجداول: محفوظة ✅' as structure_status,
    'Storage Buckets: محفوظة ✅' as buckets_status,
    'العدادات التلقائية: تم إعادة تعيينها ✅' as sequences_status,
    'النظام جاهز للاستخدام من جديد! 🚀' as final_result;

-- ===== 🔧 إصلاحات إضافية للـ Storage =====

-- إنشاء سياسات مفتوحة للجميع (بدون قيود)
CREATE POLICY "Allow all operations on photos" ON storage.objects
    FOR ALL USING (bucket_id = 'photos');

CREATE POLICY "Allow all operations on videos" ON storage.objects
    FOR ALL USING (bucket_id = 'videos');

-- إعادة تفعيل RLS مع السياسات المفتوحة
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- تحديث إعدادات Buckets لتكون مفتوحة تماماً
UPDATE storage.buckets SET
    public = true,
    avif_autodetection = false,
    file_size_limit = NULL, -- بدون حد أقصى
    allowed_mime_types = NULL -- جميع أنواع الملفات مسموحة
WHERE id IN ('photos', 'videos');

-- إنشاء الـ buckets إذا لم تكن موجودة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
    ('photos', 'photos', true, NULL, NULL),
    ('videos', 'videos', true, NULL, NULL)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- ===== 🔧 إصلاح جداول التطبيق =====

-- إلغاء تفعيل RLS على جداول التطبيق مؤقتاً
ALTER TABLE IF EXISTS public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.videos DISABLE ROW LEVEL SECURITY;

-- حذف جميع السياسات من جداول التطبيق
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT schemaname, tablename, policyname
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename IN ('users', 'photos', 'videos')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) ||
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
END $$;

-- إنشاء سياسات مفتوحة للجميع
CREATE POLICY "Allow all operations on users" ON public.users FOR ALL USING (true);
CREATE POLICY "Allow all operations on photos" ON public.photos FOR ALL USING (true);
CREATE POLICY "Allow all operations on videos" ON public.videos FOR ALL USING (true);

-- إعادة تفعيل RLS مع السياسات المفتوحة
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;

COMMIT;

-- ===== 📝 ملاحظات مهمة =====
/*
✅ ما تم حذفه:
- جميع الصور من Storage و قاعدة البيانات
- جميع الفيديوهات من Storage و قاعدة البيانات
- جميع بيانات المستخدمين
- جميع بيانات الأجهزة
- جلسات المصادقة القديمة
- جميع سياسات RLS القديمة

✅ ما تم الحفاظ عليه:
- هيكل جميع الجداول
- Storage Buckets مع إعدادات محدثة
- جدول المواقع (locations) وبياناته
- جميع الفهارس والقيود

🔧 ما تم إصلاحه:
- Storage Buckets: إزالة قيود MIME types
- RLS Policies: سياسات مفتوحة للجميع
- صلاحيات Storage: وصول كامل بدون قيود
- إعدادات Buckets: public = true, بدون حدود حجم

🔄 الخطوات التالية:
1. يمكنك الآن إنشاء مستخدمين جدد
2. رفع صور وفيديوهات جديدة بدون مشاكل
3. النظام سيعمل بشكل طبيعي مع قاعدة بيانات نظيفة ومُصلحة
4. لن تواجه مشاكل RLS أو MIME types بعد الآن

⚠️ تحذير:
هذا الحذف نهائي ولا يمكن التراجع عنه!
تأكد من أخذ نسخة احتياطية إذا كنت تحتاج أي بيانات.
*/
