# 🛡️ دليل الإدارة المؤقت - كاميرا ذاكرة القمر

## 📋 نظرة عامة
هذا الدليل يوضح كيفية إدارة التطبيق مؤقتاً عبر قاعدة البيانات حتى يتم تطوير تطبيق الإدارة.

---

## 🔐 الوصول لقاعدة البيانات

### **Supabase Dashboard:**
- الرابط: https://xufiuvdtfusbaerwrkzb.supabase.co
- اذهب إلى: **SQL Editor**

---

## 👥 إدارة المستخدمين

### **1. إضافة مستخدم جديد:**
```sql
DO $$
DECLARE
    new_user_id UUID;
BEGIN
    -- إنشاء المستخدم في جدول المصادقة
    INSERT INTO auth.users (
        instance_id,
        id,
        aud,
        role,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at
    ) VALUES (
        '********-0000-0000-0000-************',
        gen_random_uuid(),
        'authenticated',
        'authenticated',
        '<EMAIL>',  -- استبدل NATIONAL_ID
        crypt('PASSWORD', gen_salt('bf')), -- استبدل PASSWORD
        NOW(),
        NOW(),
        NOW()
    ) RETURNING id INTO new_user_id;

    -- إنشاء المستخدم في جدول المستخدمين
    INSERT INTO public.users (
        id,
        national_id,
        full_name,
        email,
        phone,
        department,
        position,
        is_active,
        is_admin,
        account_type,
        max_devices,
        storage_quota_mb
    ) VALUES (
        new_user_id,
        'NATIONAL_ID',      -- الرقم الوطني
        'FULL_NAME',        -- الاسم الكامل
        '<EMAIL>',
        '+966XXXXXXXXX',    -- رقم الهاتف
        'DEPARTMENT',       -- القسم
        'POSITION',         -- المنصب
        true,
        false,
        'user',
        1,
        1000
    );
END $$;
```

### **2. عرض جميع المستخدمين:**
```sql
SELECT 
    u.national_id,
    u.full_name,
    u.department,
    u.position,
    u.is_active,
    COUNT(d.id) as device_count,
    u.created_at
FROM users u
LEFT JOIN devices d ON u.id = d.user_id AND d.is_active = true
GROUP BY u.id, u.national_id, u.full_name, u.department, u.position, u.is_active, u.created_at
ORDER BY u.created_at DESC;
```

### **3. تعطيل/تفعيل مستخدم:**
```sql
-- تعطيل مستخدم
UPDATE users 
SET is_active = false 
WHERE national_id = 'NATIONAL_ID';

-- تفعيل مستخدم
UPDATE users 
SET is_active = true 
WHERE national_id = 'NATIONAL_ID';
```

---

## 🔑 إدارة كلمات المرور

### **إعادة تعيين كلمة مرور:**
```sql
UPDATE auth.users 
SET encrypted_password = crypt('NEW_PASSWORD', gen_salt('bf'))
WHERE email = '<EMAIL>';
```

---

## 📱 إدارة الأجهزة

### **1. عرض الأجهزة المرتبطة:**
```sql
SELECT 
    u.full_name,
    u.national_id,
    d.device_name,
    d.device_fingerprint,
    d.android_id,
    d.last_verified_at,
    d.is_active
FROM users u
JOIN devices d ON u.id = d.user_id
WHERE d.is_active = true
ORDER BY d.last_verified_at DESC;
```

### **2. فك ربط جهاز (للطوارئ):**
```sql
-- فك ربط جهاز معين
DELETE FROM devices 
WHERE user_id = (
    SELECT id FROM users WHERE national_id = 'NATIONAL_ID'
);

-- أو فك ربط جهاز بالبصمة
DELETE FROM devices 
WHERE device_fingerprint = 'DEVICE_FINGERPRINT';
```

### **3. فحص الأجهزة المشبوهة:**
```sql
-- أجهزة متعددة لنفس المستخدم (يجب أن تكون 0 أو 1)
SELECT 
    u.full_name,
    u.national_id,
    COUNT(d.id) as device_count
FROM users u
JOIN devices d ON u.id = d.user_id
WHERE d.is_active = true
GROUP BY u.id, u.full_name, u.national_id
HAVING COUNT(d.id) > 1;
```

---

## 📊 مراقبة النشاط

### **1. آخر تسجيلات الدخول:**
```sql
SELECT 
    u.full_name,
    u.national_id,
    d.device_name,
    d.last_verified_at
FROM users u
JOIN devices d ON u.id = d.user_id
WHERE d.is_active = true
ORDER BY d.last_verified_at DESC
LIMIT 20;
```

### **2. المستخدمون النشطون:**
```sql
SELECT 
    u.full_name,
    u.national_id,
    d.last_verified_at,
    CASE 
        WHEN d.last_verified_at > NOW() - INTERVAL '1 day' THEN 'نشط اليوم'
        WHEN d.last_verified_at > NOW() - INTERVAL '7 days' THEN 'نشط هذا الأسبوع'
        WHEN d.last_verified_at > NOW() - INTERVAL '30 days' THEN 'نشط هذا الشهر'
        ELSE 'غير نشط'
    END as activity_status
FROM users u
JOIN devices d ON u.id = d.user_id
WHERE d.is_active = true
ORDER BY d.last_verified_at DESC;
```

---

## 🚨 حالات الطوارئ

### **1. مستخدم فقد جهازه:**
```sql
-- فك ربط الجهاز المفقود
DELETE FROM devices 
WHERE user_id = (SELECT id FROM users WHERE national_id = 'NATIONAL_ID');

-- المستخدم سيتمكن من تسجيل الدخول من جهاز جديد
```

### **2. مستخدم نسي كلمة المرور:**
```sql
-- إعادة تعيين كلمة مرور مؤقتة
UPDATE auth.users 
SET encrypted_password = crypt('123456789', gen_salt('bf'))
WHERE email = '<EMAIL>';

-- أخبر المستخدم بكلمة المرور المؤقتة: 123456789
```

### **3. حظر مستخدم مشبوه:**
```sql
-- تعطيل الحساب
UPDATE users SET is_active = false WHERE national_id = 'NATIONAL_ID';

-- فك ربط جميع الأجهزة
DELETE FROM devices WHERE user_id = (SELECT id FROM users WHERE national_id = 'NATIONAL_ID');
```

---

## 📈 إحصائيات سريعة

### **إحصائيات عامة:**
```sql
SELECT 
    'إجمالي المستخدمين' as metric,
    COUNT(*) as value
FROM users
UNION ALL
SELECT 
    'المستخدمون النشطون',
    COUNT(*)
FROM users 
WHERE is_active = true
UNION ALL
SELECT 
    'الأجهزة المرتبطة',
    COUNT(*)
FROM devices 
WHERE is_active = true;
```

---

## ⚠️ تحذيرات مهمة

1. **🔒 احذر من حذف البيانات** - استخدم UPDATE بدلاً من DELETE عند الإمكان
2. **💾 انسخ احتياطياً** قبل أي تعديل كبير
3. **🔍 تحقق من النتائج** باستخدام SELECT قبل تنفيذ UPDATE/DELETE
4. **📝 سجل التغييرات** في ملف منفصل للمراجعة

---

---

## 🔧 توصيات لمطور تطبيق الإدارة

### **✅ الطريقة الصحيحة لإنشاء المستخدمين:**

#### **1. استخدام Supabase في Flutter Windows (الأفضل):**

##### **أ. إعداد Supabase في pubspec.yaml:**
```yaml
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^2.3.4
  http: ^1.1.0
```

##### **ب. إعداد Supabase Client:**
```dart
import 'package:supabase_flutter/supabase_flutter.dart';

class AdminSupabaseService {
  static late SupabaseClient _client;

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL',
      anonKey: 'YOUR_SERVICE_ROLE_KEY', // استخدم Service Role Key
    );
    _client = Supabase.instance.client;
  }

  static SupabaseClient get client => _client;
}
```

##### **ج. دالة إنشاء المستخدم:**
```dart
class UserManagementService {
  static final _supabase = AdminSupabaseService.client;

  static Future<Map<String, dynamic>> createUser({
    required String nationalId,
    required String fullName,
    required String password,
    String? department,
    String? position,
  }) async {
    try {
      // 1. إنشاء المستخدم في Auth باستخدام Admin API
      final authResponse = await _supabase.auth.admin.createUser(
        UserAttributes(
          email: '$<EMAIL>',
          password: password,
          emailConfirm: true,
          userMetadata: {
            'full_name': fullName,
            'national_id': nationalId,
          },
        ),
      );

      if (authResponse.user == null) {
        return {
          'success': false,
          'error': 'فشل في إنشاء المستخدم في النظام',
        };
      }

      // 2. إنشاء المستخدم في جدول users
      final userResponse = await _supabase.from('users').insert({
        'id': authResponse.user!.id,
        'national_id': nationalId,
        'full_name': fullName,
        'email': '$<EMAIL>',
        'department': department,
        'position': position,
        'is_active': true,
        'is_admin': false,
        'account_type': 'user',
        'max_devices': 1,
        'storage_quota_mb': 1000,
      });

      return {
        'success': true,
        'user_id': authResponse.user!.id,
        'email': '$<EMAIL>',
        'password': password,
        'message': 'تم إنشاء المستخدم بنجاح',
      };

    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في إنشاء المستخدم: ${error.toString()}',
      };
    }
  }
}
```

#### **د. دوال إضافية مفيدة:**
```dart
class UserManagementService {
  // ... الكود السابق ...

  // عرض جميع المستخدمين
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await _supabase
          .from('users')
          .select('''
            *,
            devices!inner(count)
          ''')
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (error) {
      print('خطأ في جلب المستخدمين: $error');
      return [];
    }
  }

  // إعادة تعيين كلمة المرور
  static Future<Map<String, dynamic>> resetPassword({
    required String userId,
    required String newPassword,
  }) async {
    try {
      await _supabase.auth.admin.updateUserById(
        userId,
        attributes: UserAttributes(password: newPassword),
      );

      return {
        'success': true,
        'message': 'تم إعادة تعيين كلمة المرور بنجاح',
        'new_password': newPassword,
      };
    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في إعادة تعيين كلمة المرور: ${error.toString()}',
      };
    }
  }

  // تفعيل/إيقاف المستخدم
  static Future<Map<String, dynamic>> toggleUserStatus({
    required String userId,
    required bool isActive,
  }) async {
    try {
      await _supabase
          .from('users')
          .update({'is_active': isActive})
          .eq('id', userId);

      // إيقاف الأجهزة إذا تم إيقاف المستخدم
      if (!isActive) {
        await _supabase
            .from('devices')
            .update({'is_active': false})
            .eq('user_id', userId);
      }

      return {
        'success': true,
        'message': isActive ? 'تم تفعيل المستخدم' : 'تم إيقاف المستخدم',
      };
    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في تغيير حالة المستخدم: ${error.toString()}',
      };
    }
  }

  // فك ربط جهاز
  static Future<Map<String, dynamic>> unlinkDevice({
    required String userId,
  }) async {
    try {
      await _supabase
          .from('devices')
          .delete()
          .eq('user_id', userId);

      return {
        'success': true,
        'message': 'تم فك ربط الجهاز بنجاح',
      };
    } catch (error) {
      return {
        'success': false,
        'error': 'خطأ في فك ربط الجهاز: ${error.toString()}',
      };
    }
  }
}
```

#### **2. أو استخدام SQL المبسط (كبديل):**
```sql
-- في تطبيق الإدارة، استدعي هذا SQL مع المتغيرات
DO $$
DECLARE
    new_user_id UUID;
BEGIN
    INSERT INTO auth.users (
        instance_id, id, aud, role, email, encrypted_password,
        email_confirmed_at, created_at, updated_at
    ) VALUES (
        '********-0000-0000-0000-************',
        gen_random_uuid(),
        'authenticated', 'authenticated',
        $1 || '@moon-memory.com',  -- $1 = national_id
        crypt($2, gen_salt('bf')), -- $2 = password
        NOW(), NOW(), NOW()
    ) RETURNING id INTO new_user_id;

    INSERT INTO public.users (
        id, national_id, full_name, email, department, position,
        is_active, is_admin, account_type, max_devices, storage_quota_mb
    ) VALUES (
        new_user_id, $1, $3, $1 || '@moon-memory.com', $4, $5,
        true, false, 'user', 1, 1000
    );

    RAISE NOTICE 'User created successfully: %', new_user_id;
END $$;
```

#### **3. مثال لواجهة إنشاء المستخدم:**
```dart
class CreateUserScreen extends StatefulWidget {
  @override
  _CreateUserScreenState createState() => _CreateUserScreenState();
}

class _CreateUserScreenState extends State<CreateUserScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nationalIdController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إنشاء مستخدم جديد'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _nationalIdController,
                decoration: InputDecoration(
                  labelText: 'الرقم الوطني',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value?.length != 10) return 'الرقم الوطني يجب أن يكون 10 أرقام';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _fullNameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'الاسم مطلوب';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _departmentController,
                decoration: InputDecoration(
                  labelText: 'القسم',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _positionController,
                decoration: InputDecoration(
                  labelText: 'المنصب',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _createUser,
                child: _isLoading
                    ? CircularProgressIndicator(color: Colors.white)
                    : Text('إنشاء المستخدم'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  minimumSize: Size(double.infinity, 50),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final result = await UserManagementService.createUser(
      nationalId: _nationalIdController.text,
      fullName: _fullNameController.text,
      password: _nationalIdController.text, // كلمة المرور = الرقم الوطني
      department: _departmentController.text,
      position: _positionController.text,
    );

    setState(() => _isLoading = false);

    if (result['success']) {
      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء المستخدم بنجاح'),
          backgroundColor: Colors.green,
        ),
      );

      // عرض بيانات تسجيل الدخول
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('بيانات تسجيل الدخول'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('البريد الإلكتروني: ${result['email']}'),
              Text('كلمة المرور: ${result['password']}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('موافق'),
            ),
          ],
        ),
      );

      // مسح الحقول
      _nationalIdController.clear();
      _fullNameController.clear();
      _departmentController.clear();
      _positionController.clear();
    } else {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['error']),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
```

### **🚫 طرق خاطئة - تجنبها:**

#### **❌ لا تستخدم هذه الدوال:**
```sql
-- ❌ دالة معقدة ومشكوك فيها
SELECT create_user(...);

-- ❌ دالة admin_functions.sql
SELECT create_user_complex(...);

-- ❌ إنشاء في public.users فقط بدون auth.users
INSERT INTO users (...);
```

#### **⚠️ مشاكل الطرق الخاطئة:**
1. **عدم تزامن** بين `auth.users` و `public.users`
2. **فشل في تسجيل الدخول** للمستخدمين الجدد
3. **مشاكل في الأمان** والصلاحيات
4. **تضارب في البيانات**

### **✅ قائمة فحص لمطور Flutter Windows:**

#### **📦 الإعداد:**
- ✅ **أضف supabase_flutter** في pubspec.yaml
- ✅ **استخدم Service Role Key** (ليس anon key)
- ✅ **أنشئ AdminSupabaseService** للإعداد
- ✅ **اختبر الاتصال** بقاعدة البيانات

#### **👥 إدارة المستخدمين:**
- ✅ **استخدم auth.admin.createUser()** لإنشاء المستخدمين
- ✅ **تأكد من إنشاء المستخدم في auth.users أولاً**
- ✅ **ثم أنشئ المستخدم في public.users**
- ✅ **اختبر تسجيل الدخول** بعد كل إنشاء
- ✅ **تعامل مع الأخطاء** بشكل صحيح

#### **🎨 واجهة المستخدم:**
- ✅ **استخدم Forms للتحقق** من البيانات
- ✅ **أضف Loading States** للعمليات
- ✅ **اعرض رسائل النجاح/الخطأ** بوضوح
- ✅ **اعرض بيانات تسجيل الدخول** للمستخدم الجديد

#### **🔒 الأمان:**
- ✅ **احم Service Role Key** (لا تضعه في الكود مباشرة)
- ✅ **استخدم متغيرات البيئة** للمفاتيح الحساسة
- ✅ **سجل العمليات** للمراجعة
- ✅ **تحقق من الصلاحيات** قبل كل عملية

---

## 📞 الدعم التقني

في حالة وجود مشاكل تقنية معقدة، تواصل مع المطور مع تفاصيل:
- وقت حدوث المشكلة
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- نوع العملية (إنشاء مستخدم، إعادة تعيين كلمة مرور، إلخ)
