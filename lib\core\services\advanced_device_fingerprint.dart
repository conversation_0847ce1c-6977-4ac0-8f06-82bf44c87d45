import 'package:device_info_plus/device_info_plus.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:io';
import 'package:logger/logger.dart';
import 'package:flutter/services.dart';

/// نظام البصمة الرقمية المتقدم للجهاز
/// يجمع معلومات متعددة لإنشاء بصمة فريدة ومقاومة للتلاعب
class AdvancedDeviceFingerprint {
  final _logger = Logger();
  final _deviceInfo = DeviceInfoPlugin();
  
  // أوزان العوامل المختلفة (المجموع = 100)
  static const Map<String, double> _fieldWeights = {
    'androidId': 25.0,        // الأهم - ثابت ومميز
    'buildFingerprint': 20.0, // مهم جداً - فريد لكل ROM
    'hardwareInfo': 15.0,     // مهم - معلومات الهاردوير
    'deviceModel': 10.0,      // أساسي - الطراز
    'manufacturer': 8.0,      // أساسي - الشركة المصنعة
    'product': 7.0,           // مفيد - اسم المنتج
    'screenInfo': 5.0,        // إضافي - دقة الشاشة
    'systemInfo': 5.0,        // إضافي - معلومات النظام
    'cpuInfo': 3.0,          // إضافي - معلومات المعالج
    'storageInfo': 2.0,      // إضافي - معلومات التخزين
  };

  /// إنشاء البصمة الرقمية المتقدمة
  Future<DeviceFingerprintResult> generateFingerprint() async {
    try {
      _logger.i('بدء إنشاء البصمة الرقمية المتقدمة...');
      
      final fingerprint = DeviceFingerprintData();
      
      // جمع المعلومات الأساسية
      await _collectBasicInfo(fingerprint);
      
      // جمع معلومات الهاردوير
      await _collectHardwareInfo(fingerprint);
      
      // جمع معلومات النظام
      await _collectSystemInfo(fingerprint);
      
      // جمع معلومات إضافية
      await _collectAdditionalInfo(fingerprint);
      
      // إنشاء البصمة النهائية
      _generateUniqueHash(fingerprint);
      
      // حساب نقاط الثقة
      _calculateConfidenceScore(fingerprint);
      
      // التحقق من صحة البصمة
      final isValid = _validateFingerprint(fingerprint);
      
      _logger.i('تم إنشاء البصمة بنجاح - نقاط الثقة: ${fingerprint.confidenceScore}%');
      
      return DeviceFingerprintResult(
        fingerprint: fingerprint,
        isValid: isValid,
        timestamp: DateTime.now(),
      );
      
    } catch (e) {
      _logger.e('خطأ في إنشاء البصمة الرقمية: $e');
      return DeviceFingerprintResult(
        fingerprint: _createFallbackFingerprint(),
        isValid: false,
        timestamp: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  /// جمع المعلومات الأساسية
  Future<void> _collectBasicInfo(DeviceFingerprintData fingerprint) async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        
        // Android ID - الأهم
        fingerprint.androidId = androidInfo.id;
        
        // Build Fingerprint - فريد جداً
        fingerprint.buildFingerprint = androidInfo.fingerprint;
        
        // معلومات الجهاز الأساسية
        fingerprint.manufacturer = androidInfo.manufacturer;
        fingerprint.deviceModel = androidInfo.model;
        fingerprint.product = androidInfo.product;
        fingerprint.device = androidInfo.device;
        fingerprint.board = androidInfo.board;
        fingerprint.hardware = androidInfo.hardware;
        
        _logger.d('تم جمع المعلومات الأساسية بنجاح');
      }
    } catch (e) {
      _logger.w('خطأ في جمع المعلومات الأساسية: $e');
    }
  }

  /// جمع معلومات الهاردوير
  Future<void> _collectHardwareInfo(DeviceFingerprintData fingerprint) async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        
        // معلومات الهاردوير المجمعة
        fingerprint.hardwareInfo = _combineHardwareInfo(
          androidInfo.hardware,
          androidInfo.board,
          androidInfo.bootloader,
          androidInfo.device,
        );
        
        // معلومات الشاشة
        fingerprint.screenInfo = await _getScreenInfo();
        
        _logger.d('تم جمع معلومات الهاردوير بنجاح');
      }
    } catch (e) {
      _logger.w('خطأ في جمع معلومات الهاردوير: $e');
    }
  }

  /// جمع معلومات النظام
  Future<void> _collectSystemInfo(DeviceFingerprintData fingerprint) async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        
        // معلومات النظام المجمعة
        fingerprint.systemInfo = _combineSystemInfo(
          androidInfo.version.release,
          androidInfo.version.sdkInt.toString(),
          androidInfo.version.securityPatch ?? '',
          androidInfo.version.codename,
        );
        
        _logger.d('تم جمع معلومات النظام بنجاح');
      }
    } catch (e) {
      _logger.w('خطأ في جمع معلومات النظام: $e');
    }
  }

  /// جمع معلومات إضافية
  Future<void> _collectAdditionalInfo(DeviceFingerprintData fingerprint) async {
    try {
      // معلومات المعالج
      fingerprint.cpuInfo = await _getCPUInfo();
      
      // معلومات التخزين
      fingerprint.storageInfo = await _getStorageInfo();
      
      _logger.d('تم جمع المعلومات الإضافية بنجاح');
    } catch (e) {
      _logger.w('خطأ في جمع المعلومات الإضافية: $e');
    }
  }

  /// دمج معلومات الهاردوير
  String _combineHardwareInfo(String? hardware, String? board, String? bootloader, String? device) {
    final parts = <String>[];
    if (hardware?.isNotEmpty == true) parts.add('HW:$hardware');
    if (board?.isNotEmpty == true) parts.add('BD:$board');
    if (bootloader?.isNotEmpty == true) parts.add('BL:$bootloader');
    if (device?.isNotEmpty == true) parts.add('DV:$device');
    return parts.join('|');
  }

  /// دمج معلومات النظام
  String _combineSystemInfo(String release, String sdk, String patch, String codename) {
    return 'OS:$release|SDK:$sdk|PATCH:$patch|CODE:$codename';
  }

  /// الحصول على معلومات الشاشة
  Future<String> _getScreenInfo() async {
    try {
      // استخدام platform channel للحصول على معلومات الشاشة
      const platform = MethodChannel('device_fingerprint/screen');
      final result = await platform.invokeMethod('getScreenInfo');
      return result ?? 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// الحصول على معلومات المعالج
  Future<String> _getCPUInfo() async {
    try {
      const platform = MethodChannel('device_fingerprint/cpu');
      final result = await platform.invokeMethod('getCPUInfo');
      return result ?? 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// الحصول على معلومات التخزين
  Future<String> _getStorageInfo() async {
    try {
      const platform = MethodChannel('device_fingerprint/storage');
      final result = await platform.invokeMethod('getStorageInfo');
      return result ?? 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// إنشاء البصمة النهائية
  void _generateUniqueHash(DeviceFingerprintData fingerprint) {
    final components = <String>[];
    
    // ترتيب العناصر حسب الأهمية
    if (fingerprint.androidId?.isNotEmpty == true) {
      components.add('AID:${fingerprint.androidId}');
    }
    if (fingerprint.buildFingerprint?.isNotEmpty == true) {
      components.add('BF:${fingerprint.buildFingerprint}');
    }
    if (fingerprint.hardwareInfo?.isNotEmpty == true) {
      components.add('HW:${fingerprint.hardwareInfo}');
    }
    if (fingerprint.manufacturer?.isNotEmpty == true) {
      components.add('MFG:${fingerprint.manufacturer}');
    }
    if (fingerprint.deviceModel?.isNotEmpty == true) {
      components.add('MDL:${fingerprint.deviceModel}');
    }
    if (fingerprint.product?.isNotEmpty == true) {
      components.add('PRD:${fingerprint.product}');
    }
    if (fingerprint.screenInfo?.isNotEmpty == true) {
      components.add('SCR:${fingerprint.screenInfo}');
    }
    if (fingerprint.systemInfo?.isNotEmpty == true) {
      components.add('SYS:${fingerprint.systemInfo}');
    }
    if (fingerprint.cpuInfo?.isNotEmpty == true) {
      components.add('CPU:${fingerprint.cpuInfo}');
    }
    if (fingerprint.storageInfo?.isNotEmpty == true) {
      components.add('STG:${fingerprint.storageInfo}');
    }
    
    // إنشاء الهاش النهائي
    final combined = components.join('|');
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    
    fingerprint.uniqueFingerprint = digest.toString();
    fingerprint.rawData = combined;
    fingerprint.createdAt = DateTime.now();
  }

  /// حساب نقاط الثقة
  void _calculateConfidenceScore(DeviceFingerprintData fingerprint) {
    double score = 0.0;
    
    // حساب النقاط حسب توفر العوامل المهمة
    if (fingerprint.androidId?.isNotEmpty == true) {
      score += _fieldWeights['androidId']!;
    }
    if (fingerprint.buildFingerprint?.isNotEmpty == true) {
      score += _fieldWeights['buildFingerprint']!;
    }
    if (fingerprint.hardwareInfo?.isNotEmpty == true) {
      score += _fieldWeights['hardwareInfo']!;
    }
    if (fingerprint.manufacturer?.isNotEmpty == true) {
      score += _fieldWeights['manufacturer']!;
    }
    if (fingerprint.deviceModel?.isNotEmpty == true) {
      score += _fieldWeights['deviceModel']!;
    }
    if (fingerprint.product?.isNotEmpty == true) {
      score += _fieldWeights['product']!;
    }
    if (fingerprint.screenInfo?.isNotEmpty == true) {
      score += _fieldWeights['screenInfo']!;
    }
    if (fingerprint.systemInfo?.isNotEmpty == true) {
      score += _fieldWeights['systemInfo']!;
    }
    if (fingerprint.cpuInfo?.isNotEmpty == true) {
      score += _fieldWeights['cpuInfo']!;
    }
    if (fingerprint.storageInfo?.isNotEmpty == true) {
      score += _fieldWeights['storageInfo']!;
    }
    
    fingerprint.confidenceScore = score;
  }

  /// التحقق من صحة البصمة
  bool _validateFingerprint(DeviceFingerprintData fingerprint) {
    // فحص الحد الأدنى لنقاط الثقة
    if (fingerprint.confidenceScore < 50.0) {
      _logger.w('نقاط الثقة منخفضة: ${fingerprint.confidenceScore}%');
      return false;
    }
    
    // فحص وجود العوامل الأساسية
    if (fingerprint.androidId?.isEmpty != false) {
      _logger.w('Android ID مفقود');
      return false;
    }
    
    if (fingerprint.buildFingerprint?.isEmpty != false) {
      _logger.w('Build Fingerprint مفقود');
      return false;
    }
    
    return true;
  }

  /// إنشاء بصمة احتياطية في حالة الفشل
  DeviceFingerprintData _createFallbackFingerprint() {
    final fingerprint = DeviceFingerprintData();
    fingerprint.androidId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';
    fingerprint.buildFingerprint = 'fallback_build';
    fingerprint.manufacturer = 'Unknown';
    fingerprint.deviceModel = 'Unknown';
    fingerprint.confidenceScore = 10.0; // نقاط منخفضة للبصمة الاحتياطية
    
    _generateUniqueHash(fingerprint);
    
    return fingerprint;
  }
}

/// بيانات البصمة الرقمية
class DeviceFingerprintData {
  // المعرفات الأساسية
  String? androidId;
  String? buildFingerprint;

  // معلومات الجهاز
  String? manufacturer;
  String? deviceModel;
  String? product;
  String? device;
  String? board;
  String? hardware;

  // معلومات مجمعة
  String? hardwareInfo;
  String? systemInfo;
  String? screenInfo;
  String? cpuInfo;
  String? storageInfo;

  // البصمة النهائية
  String? uniqueFingerprint;
  String? rawData;
  double confidenceScore = 0.0;
  DateTime? createdAt;

  /// Constructor
  DeviceFingerprintData();
  
  /// تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'android_id': androidId,
      'build_fingerprint': buildFingerprint,
      'manufacturer': manufacturer,
      'device_model': deviceModel,
      'product': product,
      'device': device,
      'board': board,
      'hardware': hardware,
      'hardware_info': hardwareInfo,
      'system_info': systemInfo,
      'screen_info': screenInfo,
      'cpu_info': cpuInfo,
      'storage_info': storageInfo,
      'unique_fingerprint': uniqueFingerprint,
      'raw_data': rawData,
      'confidence_score': confidenceScore,
      'created_at': createdAt?.toIso8601String(),
    };
  }
  
  /// إنشاء من Map
  factory DeviceFingerprintData.fromMap(Map<String, dynamic> map) {
    final fingerprint = DeviceFingerprintData();
    fingerprint.androidId = map['android_id'];
    fingerprint.buildFingerprint = map['build_fingerprint'];
    fingerprint.manufacturer = map['manufacturer'];
    fingerprint.deviceModel = map['device_model'];
    fingerprint.product = map['product'];
    fingerprint.device = map['device'];
    fingerprint.board = map['board'];
    fingerprint.hardware = map['hardware'];
    fingerprint.hardwareInfo = map['hardware_info'];
    fingerprint.systemInfo = map['system_info'];
    fingerprint.screenInfo = map['screen_info'];
    fingerprint.cpuInfo = map['cpu_info'];
    fingerprint.storageInfo = map['storage_info'];
    fingerprint.uniqueFingerprint = map['unique_fingerprint'];
    fingerprint.rawData = map['raw_data'];
    fingerprint.confidenceScore = map['confidence_score']?.toDouble() ?? 0.0;
    fingerprint.createdAt = map['created_at'] != null 
        ? DateTime.parse(map['created_at']) 
        : null;
    return fingerprint;
  }
}

/// نتيجة إنشاء البصمة الرقمية
class DeviceFingerprintResult {
  final DeviceFingerprintData fingerprint;
  final bool isValid;
  final DateTime timestamp;
  final String? error;
  
  DeviceFingerprintResult({
    required this.fingerprint,
    required this.isValid,
    required this.timestamp,
    this.error,
  });
}
