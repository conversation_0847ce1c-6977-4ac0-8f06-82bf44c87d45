import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/persistent_auth_service.dart';
import '../../../core/services/permissions_service.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/session_service.dart';
import '../../../core/routing/app_router.dart';
import '../../../core/utils/logger.dart';
import '../../camera/presentation/camera_screen.dart';
import '../../camera/presentation/gallery_screen.dart';

class WelcomeScreen extends ConsumerStatefulWidget {
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  final _logger = getLogger();
  String? _username;
  bool _isLoading = true;
  String? _selectedLocationType; // نوع الموقع المختار (U أو C)
  String? _selectedLocationNumber; // رقم الموقع المختار
  String get _selectedLocation => _selectedLocationType != null && _selectedLocationNumber != null
      ? '$_selectedLocationType$_selectedLocationNumber'
      : '';

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // أنواع المواقع المتاحة
  final List<Map<String, String>> _locationTypes = [
    {'id': 'U', 'name_ar': 'مواقع U', 'name_en': 'U Locations'},
    {'id': 'C', 'name_ar': 'مواقع C', 'name_en': 'C Locations'},
  ];

  // إنشاء قائمة أرقام المواقع حسب النوع
  List<String> _getLocationNumbers(String type) {
    if (type == 'U') {
      return List.generate(25, (index) => (101 + index).toString()); // U101 إلى U125
    } else if (type == 'C') {
      return List.generate(45, (index) => (101 + index).toString()); // C101 إلى C145
    }
    return [];
  }

  @override
  void initState() {
    super.initState();
    // 🆕 إضافة مراقب دورة حياة التطبيق
    WidgetsBinding.instance.addObserver(this);
    _initAnimations();
    _loadUserData();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  Future<void> _loadUserData() async {
    try {
      final persistentAuth = ref.read(persistentAuthServiceProvider);
      final userData = await persistentAuth.getUserData();
      
      if (userData != null) {
        setState(() {
          _username = userData['full_name'] ?? 'المستخدم';
          _isLoading = false;
        });
      } else {
        // محاولة الحصول على البيانات من Supabase
        final user = ref.read(supabaseServiceProvider).client.auth.currentUser;
        if (user != null) {
          final response = await ref.read(supabaseServiceProvider).client
              .from('users')
              .select('full_name')
              .eq('id', user.id)
              .single();
          
          final fullName = response['full_name'] as String?;
          setState(() {
            _username = fullName ?? 'المستخدم';
            _isLoading = false;
          });
          
          // حفظ البيانات محلياً
          await persistentAuth.saveUserData({
            'id': user.id,
            'full_name': fullName,
          });
        }
      }
    } catch (e) {
      _logger.e('Error loading user data: $e');
      setState(() {
        _username = 'المستخدم';
        _isLoading = false;
      });
    }
  }

  Future<void> _openCamera() async {
    try {
      // فحص الأذونات أولاً
      final permissionsService = ref.read(permissionsServiceProvider);
      var permissionsResult = await permissionsService.checkAllPermissions();

      if (!permissionsResult['success']) {
        permissionsResult = await permissionsService.requestAllPermissions();
      }

      if (!mounted) return;

      if (permissionsResult['success']) {
        // التحقق من اختيار الموقع
        if (_selectedLocation.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                context.locale.languageCode == 'ar'
                    ? 'يرجى اختيار الموقع أولاً'
                    : 'Please select a location first'
              ),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CameraScreen(
              selectedLocation: _selectedLocation,
              username: _username, // تمرير اسم المستخدم من صفحة الترحيب
            ),
          ),
        );
      } else {
        final message = permissionsResult['message'] ?? 'permissions.all_permissions_required'.tr();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      _logger.e('Error opening camera: $e');
    }
  }

  Future<void> _openLocalGallery() async {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const GalleryScreen(),
        ),
      );
    } catch (e) {
      _logger.e('Error opening local gallery: $e');
    }
  }

  void _changeLanguage() {
    final currentLocale = context.locale;
    final newLocale = currentLocale.languageCode == 'ar' 
        ? const Locale('en') 
        : const Locale('ar');
    
    context.setLocale(newLocale);
  }

  String _getLocationTypeName(Map<String, String> locationType) {
    final isArabic = context.locale.languageCode == 'ar';
    return isArabic ? locationType['name_ar']! : locationType['name_en']!;
  }

  Future<void> _handleSignOut() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('auth.logout'.tr()),
        content: Text('auth.logout_confirm'.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'common.cancel'.tr(),
              style: const TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'auth.logout'.tr(),
              style: const TextStyle(color: Color(0xFFD4AF37)),
            ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final authService = ref.read(authServiceProvider);
      await authService.signOut();

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('auth.logout_success'.tr()),
          backgroundColor: Colors.green,
        ),
      );

      if (!mounted) return;

      Navigator.of(context).pushNamedAndRemoveUntil(
        AppRoutes.login,
        (route) => false,
      );
    } catch (e) {
      _logger.e('Error signing out: $e');
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('errors.general'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 🆕 مراقبة حالة التطبيق لتسجيل الأنشطة
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final userId = ref.read(supabaseServiceProvider).client.auth.currentUser?.id;
    if (userId == null) return;

    switch (state) {
      case AppLifecycleState.resumed:
        SessionService().logActivity(
          userId: userId,
          activityType: 'app_resumed',
          activityData: {'timestamp': DateTime.now().toIso8601String()},
        );
        break;
      case AppLifecycleState.paused:
        SessionService().logActivity(
          userId: userId,
          activityType: 'app_paused',
          activityData: {'timestamp': DateTime.now().toIso8601String()},
        );
        break;
      case AppLifecycleState.detached:
        SessionService().logActivity(
          userId: userId,
          activityType: 'app_closed',
          activityData: {'timestamp': DateTime.now().toIso8601String()},
        );
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    // 🆕 إزالة مراقب دورة حياة التطبيق
    WidgetsBinding.instance.removeObserver(this);
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2C1810), // بني داكن دافئ
              Color(0xFF3D2817), // بني متوسط
              Color(0xFF5D4037), // بني شوكولاتة
              Color(0xFF8D6E63), // بني فاتح
              Color(0xFFBCAAA4), // بيج دافئ
            ],
            stops: [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    // شريط علوي مع أزرار اللغة وتسجيل الخروج
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'app.name'.tr(),
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFFD4AF37),
                          ),
                        ),
                        Row(
                          children: [
                            IconButton(
                              onPressed: _changeLanguage,
                              icon: const Icon(
                                Icons.language,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            IconButton(
                              onPressed: _handleSignOut,
                              icon: const Icon(
                                Icons.logout_rounded,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // رسالة الترحيب
                    if (_isLoading)
                      const CircularProgressIndicator(color: Colors.white)
                    else
                      Column(
                        children: [
                          Text(
                            context.locale.languageCode == 'ar' ? 'مرحباً' : 'Welcome',
                            style: GoogleFonts.cairo(
                              fontSize: 24,
                              fontWeight: FontWeight.w300,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _username ?? 'المستخدم',
                            style: GoogleFonts.cairo(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    
                    const SizedBox(height: 60),
                    
                    // لوحة تحديد الموقع
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'welcome.select_location'.tr(),
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // اختيار نوع الموقع (U أو C)
                          DropdownButtonFormField<String>(
                            value: _selectedLocationType,
                            decoration: InputDecoration(
                              filled: true,
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              hintText: context.locale.languageCode == 'ar'
                                  ? 'اختر نوع الموقع'
                                  : 'Select Location Type',
                            ),
                            items: _locationTypes.map((locationType) {
                              return DropdownMenuItem<String>(
                                value: locationType['id'],
                                child: Text(
                                  _getLocationTypeName(locationType),
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedLocationType = value;
                                _selectedLocationNumber = null; // إعادة تعيين رقم الموقع
                              });
                            },
                          ),

                          if (_selectedLocationType != null) ...[
                            const SizedBox(height: 16),

                            // اختيار رقم الموقع
                            DropdownButtonFormField<String>(
                              value: _selectedLocationNumber,
                              decoration: InputDecoration(
                                filled: true,
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide.none,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                hintText: context.locale.languageCode == 'ar'
                                    ? 'اختر رقم الموقع'
                                    : 'Select Location Number',
                              ),
                              items: _getLocationNumbers(_selectedLocationType!).map((number) {
                                return DropdownMenuItem<String>(
                                  value: number,
                                  child: Text(
                                    '$_selectedLocationType$number',
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedLocationNumber = value;
                                });
                              },
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    const Spacer(),

                    // زر فتح الكاميرا
                    _buildActionButton(
                      icon: Icons.camera_alt_rounded,
                      title: _selectedLocation.isNotEmpty
                          ? (context.locale.languageCode == 'ar'
                              ? 'فتح الكاميرا - $_selectedLocation'
                              : 'Open Camera - $_selectedLocation')
                          : (context.locale.languageCode == 'ar'
                              ? 'اختر الموقع أولاً'
                              : 'Select Location First'),
                      isEnabled: _selectedLocation.isNotEmpty,
                      onTap: _openCamera,
                      gradient: const LinearGradient(
                        colors: [Color(0xFF8A2BE2), Color(0xFF9370DB)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // زر المعرض المحلي
                    _buildActionButton(
                      icon: Icons.photo_library_rounded,
                      title: context.locale.languageCode == 'ar'
                          ? 'المعرض المحلي'
                          : 'Local Gallery',
                      isEnabled: true,
                      onTap: _openLocalGallery,
                      gradient: const LinearGradient(
                        colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required bool isEnabled,
    required VoidCallback onTap,
    required Gradient gradient,
  }) {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        gradient: isEnabled ? gradient : null,
        color: !isEnabled ? Colors.white.withValues(alpha: 0.2) : null,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: isEnabled
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.4),
          width: 2,
        ),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onTap : null,
          borderRadius: BorderRadius.circular(25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: isEnabled
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.7),
              ),
              const SizedBox(width: 16),
              Flexible(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isEnabled
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
