import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/enhanced_upload_service.dart';
import '../utils/logger.dart';

/// مراقب حالة الرفع - يعرض إحصائيات الرفع في الوقت الفعلي
class UploadStatusMonitor extends ConsumerStatefulWidget {
  const UploadStatusMonitor({super.key});

  @override
  ConsumerState<UploadStatusMonitor> createState() => _UploadStatusMonitorState();
}

class _UploadStatusMonitorState extends ConsumerState<UploadStatusMonitor> {
  final _logger = getLogger();
  final _uploadService = EnhancedUploadService();
  
  Timer? _refreshTimer;
  Map<String, dynamic> _uploadStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUploadStats();
    _startPeriodicRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _loadUploadStats();
    });
  }

  Future<void> _loadUploadStats() async {
    try {
      final stats = await _uploadService.getUploadStats();
      if (mounted) {
        setState(() {
          _uploadStats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.e('❌ فشل في تحميل إحصائيات الرفع: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    final stats = _uploadStats['stats'] as List<dynamic>? ?? [];
    
    if (stats.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.cloud_done,
                size: 48,
                color: Colors.green[400],
              ),
              const SizedBox(height: 8),
              Text(
                'جميع الملفات مرفوعة!',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
              Text(
                'لا توجد ملفات في قائمة الانتظار',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.cloud_upload,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة الرفع',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _loadUploadStats,
                  tooltip: 'تحديث',
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...stats.map((stat) => _buildStatRow(stat)).toList(),
            const SizedBox(height: 8),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(Map<String, dynamic> stat) {
    final fileType = stat['file_type'] as String;
    final status = stat['status'] as String;
    final count = stat['count'] as int;
    final totalSize = stat['total_size'] as int?;

    IconData icon;
    Color color;
    String statusText;

    switch (status) {
      case 'pending':
        icon = Icons.schedule;
        color = Colors.orange;
        statusText = 'في الانتظار';
        break;
      case 'uploading':
        icon = Icons.cloud_upload;
        color = Colors.blue;
        statusText = 'جاري الرفع';
        break;
      case 'uploaded':
        icon = Icons.cloud_done;
        color = Colors.green;
        statusText = 'مرفوع';
        break;
      case 'failed':
        icon = Icons.error;
        color = Colors.red;
        statusText = 'فشل';
        break;
      default:
        icon = Icons.help;
        color = Colors.grey;
        statusText = status;
    }

    final fileTypeText = fileType == 'photo' ? 'صور' : 'فيديوهات';
    final sizeText = totalSize != null ? _formatFileSize(totalSize) : '';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$fileTypeText - $statusText ($count)',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (sizeText.isNotEmpty)
            Text(
              sizeText,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: _cleanupQueue,
          icon: const Icon(Icons.cleaning_services, size: 16),
          label: Text(
            'تنظيف',
            style: GoogleFonts.cairo(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange[100],
            foregroundColor: Colors.orange[800],
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
        ElevatedButton.icon(
          onPressed: _retryFailed,
          icon: const Icon(Icons.refresh, size: 16),
          label: Text(
            'إعادة المحاولة',
            style: GoogleFonts.cairo(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[100],
            foregroundColor: Colors.blue[800],
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  Future<void> _cleanupQueue() async {
    try {
      await _uploadService.cleanupUploadQueue();
      _loadUploadStats();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تنظيف قائمة الرفع',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.e('❌ فشل في تنظيف قائمة الرفع: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تنظيف قائمة الرفع',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _retryFailed() async {
    // هذه الوظيفة ستحتاج تطوير إضافي في EnhancedUploadService
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'سيتم إعادة المحاولة تلقائياً',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }
}
