-- إعد<PERSON> مبسط لقاعدة البيانات - Simple Database Setup
-- Date: 2025-01-15

-- ===== حذف الجداول الموجودة (إذا كنت تريد البدء من جديد) =====
-- DROP TABLE IF EXISTS videos CASCADE;
-- DROP TABLE IF EXISTS photos CASCADE;
-- DROP TABLE IF EXISTS users CASCADE;

-- ===== إنشاء جدول المستخدمين =====
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الصور =====
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    file_name TEXT,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    location TEXT,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الفيديو =====
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    file_name TEXT,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    location TEXT,
    duration_seconds INTEGER,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إضافة حقول نظام المواقع الجديد =====

-- إضافة حقول للصور
ALTER TABLE photos 
ADD COLUMN IF NOT EXISTS location_type TEXT,
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS sort_order INTEGER;

-- إضافة قيود للصور
ALTER TABLE photos 
ADD CONSTRAINT IF NOT EXISTS photos_location_type_check 
CHECK (location_type IN ('U', 'C') OR location_type IS NULL);

-- إضافة حقول للفيديو
ALTER TABLE videos 
ADD COLUMN IF NOT EXISTS location_type TEXT,
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS sort_order INTEGER,
ADD COLUMN IF NOT EXISTS file_size_mb DECIMAL(10,2);

-- إضافة قيود للفيديو
ALTER TABLE videos 
ADD CONSTRAINT IF NOT EXISTS videos_location_type_check 
CHECK (location_type IN ('U', 'C') OR location_type IS NULL);

-- ===== تفعيل Row Level Security =====
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- ===== إنشاء السياسات =====

-- حذف السياسات الموجودة
DROP POLICY IF EXISTS users_policy ON users;
DROP POLICY IF EXISTS photos_policy ON photos;
DROP POLICY IF EXISTS videos_policy ON videos;

-- إنشاء سياسات جديدة
CREATE POLICY users_policy ON users
    FOR ALL
    USING (auth.uid() = id);

CREATE POLICY photos_policy ON photos
    FOR ALL
    USING (auth.uid() = user_id);

CREATE POLICY videos_policy ON videos
    FOR ALL
    USING (auth.uid() = user_id);

-- ===== إنشاء الفهارس =====

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);

-- فهارس الفيديو
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);

-- ===== إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

-- ===== دوال الترقيم التلقائي =====

-- دالة للصور
CREATE OR REPLACE FUNCTION update_photo_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM photos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة للفيديو
CREATE OR REPLACE FUNCTION update_video_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM videos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفزات
DROP TRIGGER IF EXISTS trigger_update_photo_sort_order ON photos;
CREATE TRIGGER trigger_update_photo_sort_order
    BEFORE INSERT ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_sort_order();

DROP TRIGGER IF EXISTS trigger_update_video_sort_order ON videos;
CREATE TRIGGER trigger_update_video_sort_order
    BEFORE INSERT ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_sort_order();

-- ===== اختبار النظام =====

-- عرض الجداول المُنشأة
SELECT 'الجداول المُنشأة:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'photos', 'videos')
ORDER BY table_name;

-- عرض حقول جدول الصور
SELECT 'حقول جدول photos:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'photos' 
ORDER BY ordinal_position;

-- عرض حقول جدول الفيديو
SELECT 'حقول جدول videos:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'videos' 
ORDER BY ordinal_position;

-- رسالة النجاح
SELECT 'تم إعداد قاعدة البيانات بنجاح! ✅' as status;
SELECT 'نظام المواقع الجديد جاهز للاستخدام' as message;
