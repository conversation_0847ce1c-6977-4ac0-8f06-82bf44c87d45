-- السياسات الأمنية والدوال المتقدمة
-- Security Policies and Advanced Functions
-- Date: 2025-01-15
-- يجب تطبيق هذا الملف بعد fixed_complete_setup.sql

-- ===== إضافة الحقول المُحسوبة =====

-- إضافة الحقل المُحسوب للصور
ALTER TABLE photos
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED;

-- إضافة الحقل المُحسوب للفيديو
ALTER TABLE videos
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED;

-- ===== تفعيل Row Level Security =====
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_stats ENABLE ROW LEVEL SECURITY;

-- ===== إنشاء السياسات الأمنية =====

-- سياسات المستخدمين
DROP POLICY IF EXISTS users_policy ON users;
CREATE POLICY users_policy ON users
    FOR ALL
    USING (auth.uid() = id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الأجهزة
DROP POLICY IF EXISTS devices_policy ON devices;
CREATE POLICY devices_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الصور
DROP POLICY IF EXISTS photos_policy ON photos;
CREATE POLICY photos_policy ON photos
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الفيديو
DROP POLICY IF EXISTS videos_policy ON videos;
CREATE POLICY videos_policy ON videos
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الجلسات
DROP POLICY IF EXISTS sessions_policy ON user_sessions;
CREATE POLICY sessions_policy ON user_sessions
    FOR ALL
    USING (auth.uid() = user_id OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات سجلات الإدارة (للمشرفين فقط)
DROP POLICY IF EXISTS admin_logs_policy ON admin_logs;
CREATE POLICY admin_logs_policy ON admin_logs
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- سياسات الإحصائيات (للمشرفين فقط)
DROP POLICY IF EXISTS stats_policy ON system_stats;
CREATE POLICY stats_policy ON system_stats
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND is_admin = TRUE
    ));

-- ===== إنشاء الفهارس للأداء =====

-- فهارس المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_national_id ON users(national_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);
CREATE INDEX IF NOT EXISTS idx_users_account_type ON users(account_type);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- فهارس الأجهزة
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);
CREATE INDEX IF NOT EXISTS idx_devices_trust_level ON devices(trust_level);
CREATE INDEX IF NOT EXISTS idx_devices_is_active ON devices(is_active);
CREATE INDEX IF NOT EXISTS idx_devices_is_blocked ON devices(is_blocked);
CREATE INDEX IF NOT EXISTS idx_devices_last_verified ON devices(last_verified_at);

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_status ON photos(status);
CREATE INDEX IF NOT EXISTS idx_photos_tags ON photos USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON photos(location_type, location_number, capture_timestamp, username);

-- فهارس الفيديو
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_status ON videos(status);
CREATE INDEX IF NOT EXISTS idx_videos_tags ON videos USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON videos(location_type, location_number, capture_timestamp, username);

-- فهارس الجلسات
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_device_id ON user_sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_sessions_started_at ON user_sessions(started_at);

-- فهارس سجلات الإدارة
CREATE INDEX IF NOT EXISTS idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_target_user_id ON admin_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_action ON admin_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_logs_entity_type ON admin_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_admin_logs_created_at ON admin_logs(created_at);

-- ===== إنشاء الدوال المساعدة =====

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للصور
CREATE OR REPLACE FUNCTION update_photo_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM photos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للفيديو
CREATE OR REPLACE FUNCTION update_video_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM videos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الإحصائيات اليومية
CREATE OR REPLACE FUNCTION update_daily_stats()
RETURNS VOID AS $$
DECLARE
    today_date DATE := CURRENT_DATE;
BEGIN
    INSERT INTO system_stats (
        stat_date,
        total_users,
        active_users,
        new_users_today,
        total_devices,
        active_devices,
        blocked_devices,
        total_photos,
        total_videos,
        photos_uploaded_today,
        videos_uploaded_today,
        total_storage_used_mb,
        photos_storage_mb,
        videos_storage_mb,
        u_locations_used,
        c_locations_used
    ) VALUES (
        today_date,
        (SELECT COUNT(*) FROM users),
        (SELECT COUNT(*) FROM users WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM devices),
        (SELECT COUNT(*) FROM devices WHERE is_active = TRUE),
        (SELECT COUNT(*) FROM devices WHERE is_blocked = TRUE),
        (SELECT COUNT(*) FROM photos WHERE status = 'active'),
        (SELECT COUNT(*) FROM videos WHERE status = 'active'),
        (SELECT COUNT(*) FROM photos WHERE DATE(created_at) = today_date),
        (SELECT COUNT(*) FROM videos WHERE DATE(created_at) = today_date),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active') +
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active'),
        (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'U' AND status = 'active'),
        (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'C' AND status = 'active')
    )
    ON CONFLICT (stat_date) 
    DO UPDATE SET
        total_users = EXCLUDED.total_users,
        active_users = EXCLUDED.active_users,
        new_users_today = EXCLUDED.new_users_today,
        total_devices = EXCLUDED.total_devices,
        active_devices = EXCLUDED.active_devices,
        blocked_devices = EXCLUDED.blocked_devices,
        total_photos = EXCLUDED.total_photos,
        total_videos = EXCLUDED.total_videos,
        photos_uploaded_today = EXCLUDED.photos_uploaded_today,
        videos_uploaded_today = EXCLUDED.videos_uploaded_today,
        total_storage_used_mb = EXCLUDED.total_storage_used_mb,
        photos_storage_mb = EXCLUDED.photos_storage_mb,
        videos_storage_mb = EXCLUDED.videos_storage_mb,
        u_locations_used = EXCLUDED.u_locations_used,
        c_locations_used = EXCLUDED.c_locations_used,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- ===== تطبيق المحفزات =====

-- محفزات تحديث updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON devices;
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_photos_updated_at ON photos;
CREATE TRIGGER update_photos_updated_at 
    BEFORE UPDATE ON photos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_videos_updated_at ON videos;
CREATE TRIGGER update_videos_updated_at 
    BEFORE UPDATE ON videos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- محفزات الترقيم التلقائي
DROP TRIGGER IF EXISTS trigger_update_photo_sort_order ON photos;
CREATE TRIGGER trigger_update_photo_sort_order
    BEFORE INSERT ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_sort_order();

DROP TRIGGER IF EXISTS trigger_update_video_sort_order ON videos;
CREATE TRIGGER trigger_update_video_sort_order
    BEFORE INSERT ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_sort_order();

-- تحديث الإحصائيات الأولية
SELECT update_daily_stats();

-- ===== رسائل النجاح =====
SELECT 'تم إعداد السياسات والدوال بنجاح! ✅' as status;
SELECT 'النظام جاهز للاستخدام! 🚀' as final_message;
