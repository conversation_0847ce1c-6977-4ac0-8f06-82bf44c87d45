import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';

class SupabaseConfig {
  static final _logger = Logger();

  /// رابط Supabase الخاص بالمشروع
  static String get supabaseUrl {
    final envUrl = dotenv.env['SUPABASE_URL'] ?? '';
    if (envUrl.isEmpty || envUrl == 'YOUR_SUPABASE_URL_HERE') {
      // رابط Supabase الحقيقي كنسخة احتياطية
      return 'https://xufiuvdtfusbaerwrkzb.supabase.co';
    }
    return envUrl;
  }

  /// مفتاح Supabase العام (anon key)
  static String get supabaseAnonKey {
    final envKey = dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    if (envKey.isEmpty || envKey == 'YOUR_SUPABASE_ANON_KEY_HERE') {
      throw Exception('SUPABASE_ANON_KEY not found in .env file');
    }

    // التحقق من صحة المفتاح
    _validateApiKey(envKey);

    return envKey;
  }

  /// التحقق من صحة إعدادات Supabase
  static bool get isConfigured {
    try {
      final url = supabaseUrl;
      final key = supabaseAnonKey;

      return url.isNotEmpty &&
             key.isNotEmpty &&
             url.startsWith('https://') &&
             key.startsWith('eyJ') && // JWT tokens start with eyJ
             !url.contains('YOUR_SUPABASE_URL_HERE') &&
             !key.contains('YOUR_SUPABASE_ANON_KEY_HERE');
    } catch (e) {
      _logger.w('Supabase configuration check failed: $e');
      return false;
    }
  }

  /// التحقق من صحة مفتاح API
  static void _validateApiKey(String key) {
    try {
      final parts = key.split('.');
      if (parts.length == 3) {
        _logger.d('API key validation: Key format is valid (${key.length} chars)');
      } else {
        _logger.w('API key validation: Invalid JWT format');
      }
    } catch (e) {
      _logger.w('API key validation warning: $e');
    }
  }
}
