-- ===== 🔧 المرحلة 2: إكمال البيانات المفقودة =====
-- تاريخ التشغيل: 2025-07-20
-- الهدف: إكمال national_id و full_name للمستخدمين الموجودين

-- 1. استخراج national_id من البريد الإلكتروني
UPDATE public.users 
SET national_id = REPLACE(email, '@moon-memory.com', '')
WHERE national_id IS NULL 
  AND email LIKE '%@moon-memory.com';

-- 2. إكمال full_name من البيانات الوصفية في auth.users (إن وجدت)
UPDATE public.users 
SET full_name = COALESCE(
    (SELECT au.raw_user_meta_data->>'full_name' 
     FROM auth.users au 
     WHERE au.id = public.users.id),
    'مستخدم - ' || REPLACE(email, '@moon-memory.com', '')
)
WHERE full_name IS NULL;

-- 3. تحديث البيانات الإضافية للمستخدمين المعروفين
-- (بناءً على البيانات من النسخة الاحتياطية)

-- المستخدم الأول: علي عبدالرحمن
UPDATE public.users 
SET 
    national_id = '1234567890',
    full_name = 'علي عبدالرحمن',
    department = 'مقوت',
    position = 'موظف',
    phone = NULL
WHERE email = '<EMAIL>' 
   OR id = '81b3d566-d65d-44b6-902e-8d4fb3258ae0';

-- المستخدم الثاني: الشيخ ابراهيم  
UPDATE public.users 
SET 
    national_id = '1234567890',
    full_name = 'الشيخ ابراهيم',
    department = 'شيخ سنحان',
    position = 'شيخ',
    phone = NULL
WHERE email = '<EMAIL>'
   OR id = 'cbcea21d-4153-4d22-9910-780f607f35e5';

-- المستخدم الثالث: ابو عنان
UPDATE public.users 
SET 
    national_id = '**********',
    full_name = 'ابو عنان',
    department = 'صنعاء',
    position = 'مدير',
    phone = NULL
WHERE email = '<EMAIL>'
   OR id = '92bdd014-b659-433f-bb09-909cd4c094b';

-- المستخدم الرابع: anan (المطور)
UPDATE public.users 
SET 
    national_id = '**********',
    full_name = 'anan',
    department = 'a',
    position = 'مطور',
    phone = NULL,
    is_admin = true,
    account_type = 'admin',
    max_devices = 10,
    storage_quota_mb = 10000
WHERE email = '<EMAIL>'
   OR id = '1bf8deee-d364-494d-a984-f0c775e92f33';

-- 4. تحديث updated_at للسجلات المحدثة
UPDATE public.users 
SET updated_at = NOW()
WHERE national_id IS NOT NULL AND full_name IS NOT NULL;

-- 5. التحقق من النتائج
SELECT 
    '=== النتائج بعد التحديث ===' as status,
    id,
    email,
    national_id,
    full_name,
    department,
    position,
    is_admin,
    account_type,
    created_at
FROM public.users 
ORDER BY created_at;

-- 6. إحصائيات سريعة
SELECT 
    '=== إحصائيات التحديث ===' as update_stats,
    COUNT(*) as total_users,
    COUNT(CASE WHEN national_id IS NOT NULL THEN 1 END) as users_with_national_id,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as users_with_full_name,
    COUNT(CASE WHEN department IS NOT NULL THEN 1 END) as users_with_department,
    COUNT(CASE WHEN is_admin = true THEN 1 END) as admin_users
FROM public.users;

-- 7. فحص التطابق مع الجداول الأخرى
SELECT 
    '=== فحص العلاقات ===' as relationship_check,
    'photos' as table_name,
    COUNT(p.id) as total_records,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as linked_records,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as orphaned_records
FROM public.photos p
LEFT JOIN public.users u ON p.user_id = u.id
UNION ALL
SELECT 
    '=== فحص العلاقات ===' as relationship_check,
    'videos' as table_name,
    COUNT(v.id) as total_records,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as linked_records,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as orphaned_records
FROM public.videos v
LEFT JOIN public.users u ON v.user_id = u.id
UNION ALL
SELECT 
    '=== فحص العلاقات ===' as relationship_check,
    'devices' as table_name,
    COUNT(d.id) as total_records,
    COUNT(CASE WHEN u.id IS NOT NULL THEN 1 END) as linked_records,
    COUNT(CASE WHEN u.id IS NULL THEN 1 END) as orphaned_records
FROM public.devices d
LEFT JOIN public.users u ON d.user_id = u.id;
