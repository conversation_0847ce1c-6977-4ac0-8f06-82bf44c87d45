-- تحديث مبسط لجدول الأجهزة - نسخة آمنة للاختبار
-- Migration: 001_simple_devices_update.sql
-- Date: 2025-01-12

-- إضا<PERSON>ة الحقول الأساسية فقط أولاً
ALTER TABLE devices 
ADD COLUMN IF NOT EXISTS device_fingerprint TEXT,
ADD COLUMN IF NOT EXISTS android_id TEXT,
ADD COLUMN IF NOT EXISTS build_fingerprint TEXT,
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS trust_level TEXT,
ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMP WITH TIME ZONE;

-- إضافة فهارس أساسية
CREATE INDEX IF NOT EXISTS idx_devices_device_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);
CREATE INDEX IF NOT EXISTS idx_devices_trust_level ON devices(trust_level);

-- <PERSON><PERSON><PERSON><PERSON>ة تعليقات
COMMENT ON COLUMN devices.device_fingerprint IS 'البصمة الرقمية المتقدمة للجهاز (SHA-256)';
COMMENT ON COLUMN devices.android_id IS 'معرف Android الفريد للجهاز';
COMMENT ON COLUMN devices.build_fingerprint IS 'بصمة البناء الخاصة بنظام التشغيل';
COMMENT ON COLUMN devices.confidence_score IS 'نقاط الثقة في البصمة (0-100)';
COMMENT ON COLUMN devices.trust_level IS 'مستوى الثقة في الجهاز';
COMMENT ON COLUMN devices.last_verified_at IS 'آخر وقت تم التحقق من الجهاز';

-- دالة بسيطة للتحقق من الجهاز
CREATE OR REPLACE FUNCTION verify_device_simple(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_confidence_score DECIMAL DEFAULT 0,
    p_trust_level TEXT DEFAULT 'untrusted'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_devices INTEGER := 3;
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- تحديث الجهاز الموجود
        UPDATE devices SET
            device_fingerprint = p_device_fingerprint,
            android_id = p_android_id,
            confidence_score = p_confidence_score,
            trust_level = p_trust_level,
            last_verified_at = NOW()
        WHERE id = device_record.id;
        
        result := json_build_object(
            'status', 'success',
            'action', 'updated',
            'device_id', device_record.id,
            'message', 'تم تحديث الجهاز بنجاح'
        );
    ELSE
        -- فحص عدد الأجهزة
        SELECT COUNT(*) INTO device_count 
        FROM devices 
        WHERE user_id = p_user_id;
        
        IF device_count >= max_devices THEN
            result := json_build_object(
                'status', 'error',
                'code', 'DEVICE_LIMIT_REACHED',
                'message', 'تم الوصول للحد الأقصى من الأجهزة'
            );
        ELSE
            -- إضافة جهاز جديد
            INSERT INTO devices (
                user_id,
                device_fingerprint,
                android_id,
                confidence_score,
                trust_level,
                last_verified_at,
                created_at
            ) VALUES (
                p_user_id,
                p_device_fingerprint,
                p_android_id,
                p_confidence_score,
                p_trust_level,
                NOW(),
                NOW()
            ) RETURNING id INTO device_record;
            
            result := json_build_object(
                'status', 'success',
                'action', 'created',
                'device_id', device_record.id,
                'message', 'تم إضافة الجهاز بنجاح'
            );
        END IF;
    END IF;
    
    RETURN result;
END;
$$;

-- تفعيل Row Level Security
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين - يمكنهم رؤية أجهزتهم فقط
DROP POLICY IF EXISTS devices_user_policy ON devices;
CREATE POLICY devices_user_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id);

-- اختبار الدالة
SELECT 'Migration completed successfully' as status;
