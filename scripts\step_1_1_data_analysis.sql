-- ===== 🔍 الخطوة 1.1: تحليل البيانات الحالية (مصححة) =====
-- تاريخ التشغيل: 2025-07-20
-- الهدف: فهم الوضع الحالي قبل التوحيد

-- إن<PERSON><PERSON><PERSON> جدول تحليل مؤقت
CREATE TEMP TABLE users_analysis AS
SELECT 
    'auth.users' as source_table,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as users_with_email,
    COUNT(CASE WHEN last_sign_in_at IS NOT NULL THEN 1 END) as users_with_login,
    <PERSON><PERSON>(created_at) as oldest_user,
    <PERSON><PERSON>(created_at) as newest_user
FROM auth.users
UNION ALL
SELECT 
    'public.users' as source_table,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as users_with_email,
    COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as users_with_login,
    <PERSON><PERSON>(created_at) as oldest_user,
    <PERSON><PERSON>(created_at) as newest_user
FROM public.users;

-- عرض تحليل البيانات
SELECT 
    '=== تحليل البيانات الحالية ===' as analysis_step,
    source_table,
    total_users,
    users_with_email,
    users_with_login,
    oldest_user,
    newest_user
FROM users_analysis
ORDER BY source_table;

-- تحليل إضافي: البحث عن المستخدمين المكررين
SELECT 
    '=== فحص المستخدمين المكررين ===' as duplicate_check,
    'auth.users vs public.users' as comparison,
    COUNT(au.id) as auth_users_count,
    COUNT(pu.id) as public_users_count,
    COUNT(CASE WHEN au.id IS NOT NULL AND pu.id IS NOT NULL THEN 1 END) as matching_users,
    COUNT(CASE WHEN au.id IS NOT NULL AND pu.id IS NULL THEN 1 END) as auth_only_users,
    COUNT(CASE WHEN au.id IS NULL AND pu.id IS NOT NULL THEN 1 END) as public_only_users
FROM auth.users au
FULL OUTER JOIN public.users pu ON au.id = pu.id;

-- تحليل إضافي: فحص البيانات المتطابقة
SELECT 
    '=== فحص تطابق البيانات ===' as data_matching,
    au.id,
    au.email as auth_email,
    pu.email as public_email,
    CASE 
        WHEN au.email = pu.email THEN 'متطابق'
        WHEN au.email IS NULL THEN 'مفقود في auth'
        WHEN pu.email IS NULL THEN 'مفقود في public'
        ELSE 'غير متطابق'
    END as email_status,
    pu.full_name,
    pu.national_id,
    au.last_sign_in_at as auth_last_login,
    pu.last_login as public_last_login
FROM auth.users au
FULL OUTER JOIN public.users pu ON au.id = pu.id
ORDER BY 
    CASE 
        WHEN au.email = pu.email THEN 1
        WHEN au.email IS NULL THEN 2
        WHEN pu.email IS NULL THEN 3
        ELSE 4
    END,
    au.email;

-- إحصائيات سريعة
SELECT 
    '=== إحصائيات سريعة ===' as quick_stats,
    (SELECT COUNT(*) FROM auth.users) as total_auth_users,
    (SELECT COUNT(*) FROM public.users) as total_public_users,
    (SELECT COUNT(*) FROM auth.users WHERE email IS NOT NULL) as auth_users_with_email,
    (SELECT COUNT(*) FROM public.users WHERE email IS NOT NULL) as public_users_with_email,
    (SELECT COUNT(*) FROM public.users WHERE is_active = true) as active_public_users,
    (SELECT COUNT(*) FROM public.users WHERE is_admin = true) as admin_users;
