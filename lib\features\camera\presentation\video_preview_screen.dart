import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:video_player/video_player.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../../core/utils/logger.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/auto_upload_service.dart';
import '../../../core/services/videos_service.dart';
import '../../../core/services/cache_service.dart';
import '../../../core/services/session_service.dart';
import '../../../core/services/enhanced_upload_service.dart';

class VideoPreviewScreen extends ConsumerStatefulWidget {
  final String videoPath;
  final String? selectedLocation;
  final String? currentAddress; // الموقع الجغرافي الحقيقي

  const VideoPreviewScreen({
    super.key,
    required this.videoPath,
    this.selectedLocation,
    this.currentAddress,
  });

  @override
  ConsumerState<VideoPreviewScreen> createState() => _VideoPreviewScreenState();
}

class _VideoPreviewScreenState extends ConsumerState<VideoPreviewScreen> {
  final _logger = getLogger();
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _isUploading = false;
  bool _isUploadComplete = false; // متغير جديد لتتبع انتهاء الرفع

  String _username = '';

  // خدمات الفيديو
  late VideosService _videosService;
  late CacheService _cacheService;

  // Timer للفحص الدوري
  Timer? _uploadCheckTimer;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeVideo();
    _loadUserInfo(); // تحميل معلومات المستخدم فقط
  }

  void _initializeServices() {
    final supabaseService = ref.read(supabaseServiceProvider);
    _cacheService = CacheService();
    _videosService = VideosService(supabaseService.client, _cacheService);

    // فحص المعرض المحلي للفيديوهات المحفوظة
    _checkLocalGalleryForPendingUploads();

    // بدء فحص دوري كل 30 ثانية
    _uploadCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkLocalGalleryForPendingUploads();
    });
  }

  /// فحص المعرض المحلي للفيديوهات المحفوظة ورفعها
  Future<void> _checkLocalGalleryForPendingUploads() async {
    try {
      _logger.i('Checking local gallery for pending video uploads...');

      final directory = await getApplicationDocumentsDirectory();
      final galleryDir = Directory('${directory.path}/gallery');

      if (!await galleryDir.exists()) {
        _logger.i('Gallery directory does not exist');
        return;
      }

      final files = galleryDir.listSync();
      final videoFiles = files.where((file) =>
        file is File &&
        file.path.toLowerCase().endsWith('.mp4') &&
        file.path.contains('video_')
      ).cast<File>().toList();

      _logger.i('Found ${videoFiles.length} video files in local gallery');

      for (final videoFile in videoFiles) {
        _logger.i('Processing video file: ${videoFile.path}');

        // محاولة رفع الفيديو
        try {
          final result = await _videosService.uploadVideo(videoFile.path, 'Unknown');
          if (result != null) {
            _logger.i('Successfully uploaded video: ${videoFile.path}');
            // حذف الفيديو بعد الرفع الناجح
            await videoFile.delete();
            _logger.i('Deleted uploaded video: ${videoFile.path}');
          }
        } catch (e) {
          _logger.e('Failed to upload video ${videoFile.path}: $e');
        }
      }
    } catch (e) {
      _logger.e('Error checking local gallery: $e');
    }
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.file(File(widget.videoPath));
      await _controller!.initialize();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _logger.e('Error initializing video: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    _uploadCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadUserInfo() async {
    try {
      // الحصول على معلومات المستخدم
      final user = ref.read(supabaseServiceProvider).client.auth.currentUser;

      if (user != null) {
        // محاولة جلب اسم المستخدم من قاعدة البيانات أولاً
        try {
          final response = await ref.read(supabaseServiceProvider).client
              .from('users')
              .select('full_name')
              .eq('id', user.id)
              .single();

          if (mounted) {
            setState(() {
              _username = response['full_name'] as String? ?? 'مستخدم';
            });
          }
          _logger.i('Username loaded from database for video: $_username');
        } catch (e) {
          _logger.w('Failed to load username from database for video: $e');

          // في حالة فشل الاتصال، استخدم البيانات المحفوظة محلياً
          final prefs = await SharedPreferences.getInstance();
          final userDataString = prefs.getString('user_data');

          if (userDataString != null) {
            try {
              final userData = jsonDecode(userDataString) as Map<String, dynamic>;
              final username = userData['full_name'] as String? ?? 'مستخدم';

              if (mounted) {
                setState(() {
                  _username = username;
                });
              }
              _logger.i('Username loaded from local storage for video: $username');
            } catch (e) {
              _logger.e('Error parsing local user data for video: $e');
              if (mounted) {
                setState(() {
                  _username = 'مستخدم';
                });
              }
            }
          } else {
            _logger.w('No local user data found for video, using default username');
            if (mounted) {
              setState(() {
                _username = 'مستخدم';
              });
            }
          }
        }
      } else {
        // إذا لم يكن هناك مستخدم مسجل، استخدم قيم افتراضية
        _logger.w('No authenticated user found for video');
        if (mounted) {
          setState(() {
            _username = 'مستخدم';
          });
        }
      }
    } catch (e) {
      _logger.e('Error loading user info for video: $e');
      if (mounted) {
        setState(() {
          _username = 'مستخدم';
        });
      }
    }
  }





  /// حفظ الفيديو إلى المعرض المحلي
  Future<void> _saveToGallery() async {
    try {
      // إنشاء مجلد المعرض المحلي
      final directory = await getApplicationDocumentsDirectory();
      final galleryDir = Directory('${directory.path}/gallery');
      if (!await galleryDir.exists()) {
        await galleryDir.create(recursive: true);
      }

      // نسخ الفيديو الأصلي إلى المعرض
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'video_$timestamp.mp4';
      final savedPath = '${galleryDir.path}/$fileName';

      await File(widget.videoPath).copy(savedPath);

      // حفظ معلومات الفيديو للرفع التلقائي
      await _saveUploadMetadata(savedPath, fileName);

      _logger.i('Video saved to gallery: $savedPath');
    } catch (e) {
      _logger.e('Error saving video to gallery: $e');
      rethrow;
    }
  }

  /// حفظ معلومات الفيديو للرفع التلقائي
  Future<void> _saveUploadMetadata(String savedPath, String fileName) async {
    try {
      // استخدام الخدمة المحسنة بدلاً من SharedPreferences
      final enhancedUploadService = EnhancedUploadService();

      // حساب حجم الملف
      int? fileSize;
      try {
        final file = File(savedPath);
        if (await file.exists()) {
          fileSize = await file.length();
        }
      } catch (e) {
        _logger.w('⚠️ فشل في حساب حجم الملف: $e');
      }

      // حساب مدة الفيديو
      int? durationSeconds;
      if (_controller != null && _controller!.value.isInitialized) {
        durationSeconds = _controller!.value.duration.inSeconds;
      }

      final queueId = await enhancedUploadService.addToUploadQueue(
        filePath: savedPath,
        fileName: fileName,
        fileType: 'video',
        location: widget.selectedLocation ?? 'unknown',
        username: _username,
        fileSizeBytes: fileSize,
        durationSeconds: durationSeconds,
        metadata: {
          'geo_location': widget.currentAddress,
          'capture_timestamp': DateTime.now().toIso8601String(),
          'original_path': widget.videoPath,
        },
      );

      if (queueId != null) {
        _logger.i('✅ تم إضافة الفيديو إلى قائمة الرفع: $queueId');
      } else {
        _logger.e('❌ فشل في إضافة الفيديو إلى قائمة الرفع');
      }
    } catch (e) {
      _logger.e('❌ خطأ في حفظ معلومات الفيديو للرفع: $e');
    }
  }

  /// رفع الفيديو في الخلفية
  void _uploadVideoInBackground() {
    _logger.i('🎬 === _uploadVideoInBackground called ===');
    _logger.i('🎬 Starting video upload in background...');
    _logger.i('🎬 Video path: ${widget.videoPath}');
    _logger.i('🎬 Location: ${widget.selectedLocation}');
    _logger.i('🎬 VideosService instance: $_videosService');

    // تحديث حالة بدء الرفع
    if (mounted) {
      setState(() {
        _isUploading = true;
      });
    }

    // رفع الفيديو بدون انتظار (في الخلفية)
    _logger.i('🎬 Calling _videosService.uploadVideo...');
    _videosService.uploadVideo(widget.videoPath, widget.selectedLocation).then((storagePath) {
      _logger.i('🎬 Video uploaded successfully in background');
      _logger.i('🎬 Storage path: $storagePath');

      // تحديث حالة انتهاء الرفع
      if (mounted) {
        setState(() {
          _isUploading = false;
          _isUploadComplete = true;
        });
      }

      // حذف الفيديو من المعرض المحلي بعد الرفع الناجح
      _deleteVideoFromLocalGallery();

    }).catchError((e) {
      _logger.e('🎬 Error uploading video in background: $e');
      _logger.e('🎬 Error details: ${e.toString()}');
      // في حالة فشل الرفع، نسمح بالمشاركة أيضاً
      if (mounted) {
        setState(() {
          _isUploading = false;
          _isUploadComplete = true;
        });
      }
    });
  }

  /// حذف الفيديو من المعرض المحلي بعد الرفع
  Future<void> _deleteVideoFromLocalGallery() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final galleryDir = Directory('${directory.path}/gallery');

      if (await galleryDir.exists()) {
        // البحث عن الفيديو المحدد وحذفه بناءً على المسار
        final videoName = path.basename(widget.videoPath);

        // البحث عن الفيديو في المعرض المحلي
        final files = galleryDir.listSync();
        for (var file in files) {
          if (file is File && path.basename(file.path).contains(videoName.split('.').first)) {
            await file.delete();
            _logger.i('Deleted specific video from local gallery: ${file.path}');

            // تحديث metadata لإزالة الفيديو من قائمة الانتظار
            await _markVideoAsUploaded(file.path);
            break;
          }
        }
      }
    } catch (e) {
      _logger.e('Error deleting video from local gallery: $e');
    }
  }

  /// تحديد الفيديو كمرفوع في metadata
  Future<void> _markVideoAsUploaded(String videoPath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_video_uploads') ?? [];
      final updatedUploads = <String>[];

      for (final uploadJson in pendingUploads) {
        final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;

        if (uploadData['path'] == videoPath) {
          // تحديث حالة الرفع
          uploadData['uploaded'] = true;
          uploadData['uploadedAt'] = DateTime.now().toIso8601String();
        }

        // الاحتفاظ بالملفات غير المرفوعة فقط
        if (uploadData['uploaded'] != true) {
          updatedUploads.add(jsonEncode(uploadData));
        }
      }

      await prefs.setStringList('pending_video_uploads', updatedUploads);
      _logger.i('Video marked as uploaded and removed from pending list');
    } catch (e) {
      _logger.e('Error marking video as uploaded: $e');
    }
  }

  /// مشاركة الفيديو
  Future<void> _shareVideo() async {
    try {
      // إنشاء نسخة مؤقتة للمشاركة بامتداد صحيح
      final directory = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final shareVideoPath = '${directory.path}/share_video_$timestamp.mp4';

      // نسخ الفيديو مع امتداد صحيح
      await File(widget.videoPath).copy(shareVideoPath);

      await Share.shareXFiles(
        [XFile(shareVideoPath)],
        text: 'فيديو من تطبيق ذاكرة القمر',
      );

      // حذف الملف المؤقت بعد المشاركة
      await File(shareVideoPath).delete();

      _logger.i('Video shared successfully');
    } catch (e) {
      _logger.e('Error sharing video: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'camera.photo_share_error'.tr(),
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _saveVideo() async {
    _logger.i('🎬 === _saveVideo called ===');
    _logger.i('🎬 Video path: ${widget.videoPath}');
    _logger.i('🎬 Selected location: ${widget.selectedLocation}');
    setState(() {
      _isUploading = true;
    });

    try {
      // حفظ الفيديو في المعرض المحلي دائماً
      _logger.i('🎬 Saving video to gallery...');
      await _saveToGallery();
      _logger.i('🎬 Video saved to gallery successfully');

      // 🆕 تسجيل نشاط تسجيل الفيديو
      await _logVideoActivity();

      // إنهاء حالة التحميل فوراً
      setState(() {
        _isUploading = false;
      });

      // رفع الفيديو في الخلفية (بدون انتظار)
      _logger.i('🎬 Starting background upload...');
      _uploadVideoInBackground();

      // بدء الرفع التلقائي في الخلفية
      _startBackgroundUpload();

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'video.saved_to_gallery'.tr(),
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: _shareVideo,
                    child: Text(
                      'video.share_button'.tr(),
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _isUploadComplete
                    ? 'video.upload_complete'.tr()
                    : 'video.upload_pending'.tr(),
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: _isUploadComplete ? Colors.green[300] : Colors.white70,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 6),
        ),
      );

      setState(() {
        _isUploading = false;
      });

    } catch (e) {
      _logger.e('Error saving video: $e');

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'video.save_error'.tr(),
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }



  /// تسجيل نشاط تسجيل الفيديو
  Future<void> _logVideoActivity() async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final userId = supabaseService.client.auth.currentUser?.id;
      if (userId == null) return;

      // حساب حجم الملف ومدة الفيديو
      int? fileSize;
      int? duration;

      final file = File(widget.videoPath);
      if (await file.exists()) {
        fileSize = await file.length();
      }

      // يمكن إضافة حساب مدة الفيديو هنا لاحقاً
      if (_controller != null && _controller!.value.isInitialized) {
        duration = _controller!.value.duration.inSeconds;
      }

      await SessionService().logActivity(
        userId: userId,
        activityType: 'video_recorded',
        activityData: {
          'location': widget.selectedLocation,
          'geoLocation': widget.currentAddress,
          'username': _username,
        },
        fileSizeBytes: fileSize,
        durationSeconds: duration,
      );

      _logger.i('🎬 تم تسجيل نشاط تسجيل الفيديو');
    } catch (e) {
      _logger.w('⚠️ فشل في تسجيل نشاط تسجيل الفيديو: $e');
    }
  }

  void _startBackgroundUpload() {
    // بدء الرفع التلقائي في الخلفية
    AutoUploadService().startAutoUpload();
    _logger.i('Background upload started for video');
  }

  Future<void> _retakeVideo() async {
    try {
      // حذف الفيديو المؤقت
      await File(widget.videoPath).delete();

      // العودة للكاميرا
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      _logger.e('Error deleting video: $e');
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // عرض الفيديو
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFD4AF37),
              ),
            )
          else if (_controller != null && _controller!.value.isInitialized)
            Center(
              child: AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: VideoPlayer(_controller!),
              ),
            )
          else
            const Center(
              child: Icon(
                Icons.error,
                color: Colors.red,
                size: 64,
              ),
            ),

          // أزرار التحكم (مثل شاشة الصور)
          if (!_isLoading && _controller != null && _controller!.value.isInitialized)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withAlpha((0.8 * 255).round()),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر إعادة التصوير
                    IconButton(
                      icon: const Icon(
                        Icons.refresh_rounded,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: _retakeVideo,
                    ),

                    // زر تشغيل/إيقاف
                    IconButton(
                      icon: Icon(
                        _controller!.value.isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: () {
                        setState(() {
                          if (_controller!.value.isPlaying) {
                            _controller!.pause();
                          } else {
                            _controller!.play();
                          }
                        });
                      },
                    ),

                    // زر الحفظ
                    _isUploading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : IconButton(
                            icon: const Icon(
                              Icons.check_rounded,
                              color: Colors.white,
                              size: 32,
                            ),
                            onPressed: _saveVideo,
                          ),

                    // زر المشاركة (يظهر فقط بعد انتهاء الرفع)
                    (!_isUploading && _isUploadComplete)
                        ? IconButton(
                            icon: const Icon(
                              Icons.share_rounded,
                              color: Colors.white,
                              size: 32,
                            ),
                            onPressed: _shareVideo,
                          )
                        : const SizedBox.shrink(),
                  ],
                ),
              ),
            ),




        ],
      ),
    );
  }


}
