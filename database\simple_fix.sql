-- 🔧 إصلاح مبسط - خطوة واحدة
-- Simple Fix - One Step
-- Date: 2025-01-17

-- إيقا<PERSON> RLS مؤقتاً
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;

-- حذ<PERSON> جدول المواقع القديم إذا كان موجوداً
DROP TABLE IF EXISTS public.locations CASCADE;

-- إنشاء جدول المواقع الجديد
CREATE TABLE public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    location_code TEXT UNIQUE NOT NULL,
    location_type TEXT NOT NULL CHECK (location_type IN ('U', 'C')),
    location_number TEXT NOT NULL,
    location_name_ar TEXT,
    location_name_en TEXT,
    sort_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة مواقع U (U101 - U125)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
VALUES 
('U101', 'U', '101', 'موقع U101', 'Location U101', 101, true),
('U102', 'U', '102', 'موقع U102', 'Location U102', 102, true),
('U103', 'U', '103', 'موقع U103', 'Location U103', 103, true),
('U104', 'U', '104', 'موقع U104', 'Location U104', 104, true),
('U105', 'U', '105', 'موقع U105', 'Location U105', 105, true),
('U106', 'U', '106', 'موقع U106', 'Location U106', 106, true),
('U107', 'U', '107', 'موقع U107', 'Location U107', 107, true),
('U108', 'U', '108', 'موقع U108', 'Location U108', 108, true),
('U109', 'U', '109', 'موقع U109', 'Location U109', 109, true),
('U110', 'U', '110', 'موقع U110', 'Location U110', 110, true),
('U111', 'U', '111', 'موقع U111', 'Location U111', 111, true),
('U112', 'U', '112', 'موقع U112', 'Location U112', 112, true),
('U113', 'U', '113', 'موقع U113', 'Location U113', 113, true),
('U114', 'U', '114', 'موقع U114', 'Location U114', 114, true),
('U115', 'U', '115', 'موقع U115', 'Location U115', 115, true),
('U116', 'U', '116', 'موقع U116', 'Location U116', 116, true),
('U117', 'U', '117', 'موقع U117', 'Location U117', 117, true),
('U118', 'U', '118', 'موقع U118', 'Location U118', 118, true),
('U119', 'U', '119', 'موقع U119', 'Location U119', 119, true),
('U120', 'U', '120', 'موقع U120', 'Location U120', 120, true),
('U121', 'U', '121', 'موقع U121', 'Location U121', 121, true),
('U122', 'U', '122', 'موقع U122', 'Location U122', 122, true),
('U123', 'U', '123', 'موقع U123', 'Location U123', 123, true),
('U124', 'U', '124', 'موقع U124', 'Location U124', 124, true),
('U125', 'U', '125', 'موقع U125', 'Location U125', 125, true);

-- إضافة مواقع C (C101 - C145) - جزء 1
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
VALUES 
('C101', 'C', '101', 'موقع C101', 'Location C101', 101, true),
('C102', 'C', '102', 'موقع C102', 'Location C102', 102, true),
('C103', 'C', '103', 'موقع C103', 'Location C103', 103, true),
('C104', 'C', '104', 'موقع C104', 'Location C104', 104, true),
('C105', 'C', '105', 'موقع C105', 'Location C105', 105, true),
('C106', 'C', '106', 'موقع C106', 'Location C106', 106, true),
('C107', 'C', '107', 'موقع C107', 'Location C107', 107, true),
('C108', 'C', '108', 'موقع C108', 'Location C108', 108, true),
('C109', 'C', '109', 'موقع C109', 'Location C109', 109, true),
('C110', 'C', '110', 'موقع C110', 'Location C110', 110, true),
('C111', 'C', '111', 'موقع C111', 'Location C111', 111, true),
('C112', 'C', '112', 'موقع C112', 'Location C112', 112, true),
('C113', 'C', '113', 'موقع C113', 'Location C113', 113, true),
('C114', 'C', '114', 'موقع C114', 'Location C114', 114, true),
('C115', 'C', '115', 'موقع C115', 'Location C115', 115, true),
('C116', 'C', '116', 'موقع C116', 'Location C116', 116, true),
('C117', 'C', '117', 'موقع C117', 'Location C117', 117, true),
('C118', 'C', '118', 'موقع C118', 'Location C118', 118, true),
('C119', 'C', '119', 'موقع C119', 'Location C119', 119, true),
('C120', 'C', '120', 'موقع C120', 'Location C120', 120, true),
('C121', 'C', '121', 'موقع C121', 'Location C121', 121, true),
('C122', 'C', '122', 'موقع C122', 'Location C122', 122, true),
('C123', 'C', '123', 'موقع C123', 'Location C123', 123, true),
('C124', 'C', '124', 'موقع C124', 'Location C124', 124, true),
('C125', 'C', '125', 'موقع C125', 'Location C125', 125, true),
('C126', 'C', '126', 'موقع C126', 'Location C126', 126, true),
('C127', 'C', '127', 'موقع C127', 'Location C127', 127, true),
('C128', 'C', '128', 'موقع C128', 'Location C128', 128, true),
('C129', 'C', '129', 'موقع C129', 'Location C129', 129, true),
('C130', 'C', '130', 'موقع C130', 'Location C130', 130, true),
('C131', 'C', '131', 'موقع C131', 'Location C131', 131, true),
('C132', 'C', '132', 'موقع C132', 'Location C132', 132, true),
('C133', 'C', '133', 'موقع C133', 'Location C133', 133, true),
('C134', 'C', '134', 'موقع C134', 'Location C134', 134, true),
('C135', 'C', '135', 'موقع C135', 'Location C135', 135, true),
('C136', 'C', '136', 'موقع C136', 'Location C136', 136, true),
('C137', 'C', '137', 'موقع C137', 'Location C137', 137, true),
('C138', 'C', '138', 'موقع C138', 'Location C138', 138, true),
('C139', 'C', '139', 'موقع C139', 'Location C139', 139, true),
('C140', 'C', '140', 'موقع C140', 'Location C140', 140, true),
('C141', 'C', '141', 'موقع C141', 'Location C141', 141, true),
('C142', 'C', '142', 'موقع C142', 'Location C142', 142, true),
('C143', 'C', '143', 'موقع C143', 'Location C143', 143, true),
('C144', 'C', '144', 'موقع C144', 'Location C144', 144, true),
('C145', 'C', '145', 'موقع C145', 'Location C145', 145, true);

-- إضافة الحقول الجديدة للصور
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0;

-- إضافة الحقول الجديدة للفيديوهات
ALTER TABLE public.videos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS duration_seconds INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS resolution TEXT;

-- إنشاء دالة الحصول على المواقع مع الإحصائيات
CREATE OR REPLACE FUNCTION get_locations_with_stats()
RETURNS TABLE(
    location_code TEXT,
    location_type TEXT,
    location_name_ar TEXT,
    location_name_en TEXT,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.location_code,
        l.location_type,
        l.location_name_ar,
        l.location_name_en,
        l.total_photos,
        l.total_videos,
        (l.total_photos + l.total_videos) as total_files,
        l.last_used_at,
        l.is_active
    FROM public.locations l
    WHERE l.is_active = true
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة تحديث الإحصائيات
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR location_rec IN SELECT location_code FROM public.locations WHERE is_active = true
    LOOP
        UPDATE public.locations 
        SET 
            total_photos = (
                SELECT COUNT(*) FROM public.photos 
                WHERE full_location_code = location_rec.location_code AND status = 'active'
            ),
            total_videos = (
                SELECT COUNT(*) FROM public.videos 
                WHERE full_location_code = location_rec.location_code AND status = 'active'
            ),
            updated_at = NOW()
        WHERE location_code = location_rec.location_code;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع';
END;
$$ LANGUAGE plpgsql;

-- إعادة تفعيل RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- إنشاء السياسات
CREATE POLICY users_policy ON public.users FOR ALL USING (auth.uid() = id);
CREATE POLICY photos_policy ON public.photos FOR ALL USING (auth.uid() = user_id);
CREATE POLICY videos_policy ON public.videos FOR ALL USING (auth.uid() = user_id);
CREATE POLICY locations_policy ON public.locations FOR SELECT USING (true);

-- تقرير النتائج
SELECT 'تم إنشاء جدول المواقع بنجاح!' as status;
SELECT COUNT(*) as total_locations FROM public.locations;
SELECT COUNT(*) as u_locations FROM public.locations WHERE location_type = 'U';
SELECT COUNT(*) as c_locations FROM public.locations WHERE location_type = 'C';
