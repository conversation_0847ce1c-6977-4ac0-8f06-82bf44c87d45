-- ===== 🔍 فحص بسيط وإعداد سريع =====

-- ===== 1️⃣ فحص هيكل جدول users =====
SELECT 'هيكل جدول users الحالي:' as info;
SELECT 
    column_name, 
    data_type,
    CASE WHEN is_nullable = 'YES' THEN 'يمكن أن يكون فارغ' ELSE 'مطلوب' END as nullable_status
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- ===== 2️⃣ إنشاء جدول الجلسات فقط =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id TEXT,
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 minutes'),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== 3️⃣ فهارس أساسية =====
CREATE INDEX IF NOT EXISTS idx_user_sessions_active 
ON user_sessions (is_active, last_activity) WHERE is_active = TRUE;

-- ===== 4️⃣ دالة تنظيف بسيطة =====
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER LANGUAGE plpgsql AS $$
DECLARE expired_count INTEGER;
BEGIN
    UPDATE user_sessions SET is_active = FALSE, ended_at = NOW(), end_reason = 'timeout'
    WHERE is_active = TRUE AND last_activity < NOW() - INTERVAL '30 minutes';
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END; $$;

-- ===== 5️⃣ دالة بسيطة للمستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users_simple()
RETURNS TABLE (
    user_id UUID,
    session_id UUID,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    device_info TEXT
) LANGUAGE plpgsql AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        s.user_id,
        s.id as session_id,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.user_agent as device_info
    FROM user_sessions s
    WHERE s.is_active = TRUE 
      AND s.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 6️⃣ دالة إحصائيات بسيطة =====
CREATE OR REPLACE FUNCTION get_online_stats_simple()
RETURNS TABLE (
    total_online INTEGER,
    active_sessions INTEGER
) LANGUAGE plpgsql AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT s.user_id)::INTEGER as total_online,
        COUNT(s.id)::INTEGER as active_sessions
    FROM user_sessions s
    WHERE s.is_active = TRUE 
      AND s.last_activity > NOW() - INTERVAL '30 minutes';
END; $$;

-- ===== 7️⃣ دالة تحديث النشاط =====
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id UUID)
RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions 
    SET last_activity = NOW(), expires_at = NOW() + INTERVAL '30 minutes'
    WHERE id = p_session_id AND is_active = TRUE;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== ✅ اختبار سريع =====
SELECT 'تنظيف الجلسات...' as step;
SELECT cleanup_expired_sessions() as cleaned;

SELECT 'المستخدمون المتصلون (بسيط):' as step;
SELECT * FROM get_online_users_simple();

SELECT 'الإحصائيات (بسيط):' as step;
SELECT * FROM get_online_stats_simple();

-- ===== 🎉 النجاح =====
SELECT '✅ تم إعداد النظام الأساسي بنجاح!' as status;
SELECT 'يمكنك الآن اختبار النظام' as ready;

-- ===== 📝 ملاحظة مهمة =====
SELECT 'ملاحظة: هذا الإعداد الأساسي يعمل مع أي هيكل لجدول users' as note;
SELECT 'بعد التأكد من العمل، يمكن تحسين الدوال لتشمل معلومات أكثر' as improvement;
