# دليل الإعداد النهائي لنظام Moon Memory
# Final Setup Guide for Moon Memory System

## 🎯 الهدف
إعداد نظام كامل يشمل:
- ✅ تطبيق الكاميرا مع نظام البصمة الرقمية
- ✅ نظام المواقع الجديد (U/C)
- ✅ قاعدة البيانات الشاملة
- ✅ نظام الرفع التلقائي والمعرض
- 🔄 تطبيق الإدارة (سيتم إنشاؤه لاحقاً)

## 📋 خطوات التطبيق بالترتيب

### الخطوة 1: إعداد قاعدة البيانات الكاملة
```sql
-- في Supabase Dashboard > SQL Editor
-- نسخ ولصق محتوى الملف:
```
📁 `database/complete_system_setup.sql`

**ما سيتم إنشاؤه:**
- جدول `users` محسن مع الصلاحيات
- جدول `devices` مع البصمة الرقمية المتقدمة
- جدول `photos` مع نظام المواقع الجديد
- جدول `videos` مع نظام المواقع الجديد
- جدول `user_sessions` لتتبع الجلسات
- جدول `admin_logs` لسجلات الإدارة
- جدول `system_stats` للإحصائيات
- جميع الفهارس والسياسات الأمنية

### الخطوة 2: إضافة دوال الإدارة المتقدمة
```sql
-- نسخ ولصق محتوى الملف:
```
📁 `database/admin_functions.sql`

**الدوال المضافة:**
- `create_user()` - إنشاء مستخدم جديد
- `reset_user_password()` - إعادة تعيين كلمة المرور
- `toggle_user_status()` - تفعيل/إيقاف المستخدم
- `get_users_list()` - قائمة المستخدمين مع الإحصائيات
- `get_photos_admin()` - الصور مع الترتيب المطلوب
- `get_videos_admin()` - الفيديو مع الترتيب المطلوب
- `get_dashboard_stats()` - إحصائيات شاملة

### الخطوة 3: إنشاء المستخدم المشرف
```sql
-- أولاً: إنشاء المستخدم في Auth (عبر Supabase Dashboard)
-- Authentication > Users > Add User
-- Email: <EMAIL>
-- Password: [كلمة مرور قوية]
-- انسخ UUID المُنشأ

-- ثانياً: إضافة المستخدم في جدول users
INSERT INTO users (
    id, 
    national_id, 
    full_name, 
    email, 
    is_admin, 
    account_type,
    max_devices,
    storage_quota_mb
) VALUES (
    '[UUID من الخطوة السابقة]'::UUID,
    '**********', -- رقم هوية المشرف
    'مشرف النظام',
    '<EMAIL>',
    TRUE,
    'admin',
    10, -- عدد أجهزة أكبر للمشرف
    10000 -- مساحة تخزين أكبر
);
```

## 🧪 اختبار النظام

### 1. اختبار نظام البصمة الرقمية
1. **تسجيل دخول المشرف** من الجهاز الأول
2. **إنشاء مستخدم جديد** عبر تطبيق الإدارة (لاحقاً)
3. **محاولة تسجيل دخول المستخدم الجديد** من نفس الجهاز
4. **يجب أن تظهر الرسالة**: "هذا الجهاز غير مصرح له بالدخول. يرجى التواصل مع المشرف"

### 2. اختبار نظام المواقع
1. تسجيل دخول المستخدم من جهازه المصرح به
2. اختيار موقع من النوع U (مثل U101)
3. التقاط صورة أو فيديو
4. التحقق من حفظ البيانات بالترتيب الصحيح في قاعدة البيانات

### 3. اختبار الرفع التلقائي
1. التقاط صورة/فيديو بدون إنترنت
2. حفظها في المعرض المحلي
3. الاتصال بالإنترنت
4. التحقق من الرفع التلقائي والحذف بعد النجاح

## 🔍 فحص البيانات

### فحص المستخدمين
```sql
SELECT 
    national_id,
    full_name,
    email,
    is_active,
    is_admin,
    account_type,
    created_at
FROM users 
ORDER BY created_at;
```

### فحص الأجهزة والبصمة
```sql
SELECT 
    u.full_name,
    d.device_name,
    d.device_model,
    d.trust_level,
    d.confidence_score,
    d.is_active,
    d.is_blocked,
    d.last_verified_at
FROM devices d
JOIN users u ON d.user_id = u.id
ORDER BY d.created_at;
```

### فحص الصور مع الترتيب الجديد
```sql
SELECT 
    u.full_name,
    p.location_type,
    p.location_number,
    p.full_location_code,
    p.username,
    p.capture_timestamp,
    p.sort_order
FROM photos p
JOIN users u ON p.user_id = u.id
WHERE p.status = 'active'
ORDER BY 
    p.location_type,
    p.location_number,
    p.capture_timestamp,
    p.username;
```

### فحص الإحصائيات
```sql
SELECT get_dashboard_stats();
```

## ⚠️ نقاط مهمة

### 1. أمان البصمة الرقمية
- ✅ **يعمل تلقائياً**: عند تسجيل دخول المشرف لأول مرة، يتم ربط جهازه
- ✅ **منع الدخول**: أي مستخدم آخر لن يستطيع الدخول من نفس الجهاز
- ✅ **رسالة واضحة**: "هذا الجهاز غير مصرح له بالدخول. يرجى التواصل مع المشرف"

### 2. نظام المواقع
- ✅ **مواقع U**: من U101 إلى U125 (25 موقع)
- ✅ **مواقع C**: من C101 إلى C145 (45 موقع)
- ✅ **ترتيب ذكي**: حسب النوع، الرقم، الوقت، المستخدم

### 3. المعرض والرفع
- ✅ **عمل بدون إنترنت**: حفظ محلي
- ✅ **رفع تلقائي**: عند توفر الإنترنت
- ✅ **حذف ذكي**: بعد التأكد من الرفع الناجح

## 🚀 الخطوات التالية

### 1. تطبيق قاعدة البيانات
- [ ] تطبيق `complete_system_setup.sql`
- [ ] تطبيق `admin_functions.sql`
- [ ] إنشاء المستخدم المشرف

### 2. اختبار التطبيق
- [ ] اختبار البصمة الرقمية
- [ ] اختبار نظام المواقع
- [ ] اختبار الرفع التلقائي

### 3. تطوير تطبيق الإدارة
- [ ] إنشاء مشروع Flutter جديد في `moon_memory_admin`
- [ ] واجهة عصرية بدون تسجيل دخول
- [ ] إدارة المستخدمين والصور والإحصائيات

---

**ملاحظة**: بعد تطبيق قاعدة البيانات واختبار النظام، سنبدأ في تطوير تطبيق الإدارة المخصص لك! 🎯
