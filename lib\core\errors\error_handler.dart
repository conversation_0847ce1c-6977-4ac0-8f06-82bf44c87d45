import 'dart:io';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:easy_localization/easy_localization.dart';
import 'package:logger/logger.dart';
import 'app_exceptions.dart';

/// معالج الأخطاء العام
class ErrorHandler {
  static final _logger = Logger();

  /// معالجة الأخطاء وتحويلها إلى استثناءات مخصصة
  static AppException handleError(dynamic error) {
    // تسجيل آمن للأخطاء (بدون معلومات حساسة)
    _logErrorSafely(error);

    if (error is AppException) {
      return error;
    }

    // معالجة أخطاء Supabase
    if (error is supabase.AuthException) {
      return _handleAuthError(error);
    }

    // معالجة أخطاء الشبكة
    if (error is SocketException) {
      return const NetworkException(
        'لا يوجد اتصال بالإنترنت',
        code: 'no_internet',
      );
    }

    if (error is HttpException) {
      return NetworkException(
        'خطأ في الاتصال بالخادم',
        code: 'http_error',
        originalError: _sanitizeError(error),
      );
    }

    // معالجة أخطاء عامة مع تنظيف المعلومات الحساسة
    return GeneralException(
      _sanitizeErrorMessage(error.toString()),
      originalError: _sanitizeError(error),
    );
  }

  /// معالجة أخطاء المصادقة
  static AuthException _handleAuthError(supabase.AuthException error) {
    switch (error.message.toLowerCase()) {
      case 'invalid login credentials':
      case 'invalid email or password':
        return const AuthException(
          'رقم الهوية أو كلمة المرور غير صحيح',
          code: 'invalid_credentials',
        );
      case 'email not confirmed':
        return const AuthException(
          'يرجى تأكيد البريد الإلكتروني أولاً',
          code: 'email_not_confirmed',
        );
      case 'user not found':
        return const AuthException(
          'المستخدم غير موجود',
          code: 'user_not_found',
        );
      case 'signup disabled':
        return const AuthException(
          'التسجيل معطل حالياً',
          code: 'signup_disabled',
        );
      case 'email address invalid':
        return const AuthException(
          'البريد الإلكتروني غير صحيح',
          code: 'email_invalid',
        );
      case 'password too short':
      case 'weak password':
        return const AuthException(
          'كلمة المرور ضعيفة جداً (يجب أن تكون 6 أحرف على الأقل)',
          code: 'weak_password',
        );
      case 'email already registered':
      case 'user already registered':
        return const AuthException(
          'البريد الإلكتروني مسجل مسبقاً',
          code: 'email_already_registered',
        );
      case 'too many requests':
        return const AuthException(
          'محاولات كثيرة جداً، يرجى المحاولة بعد دقيقة',
          code: 'too_many_requests',
        );
      default:
        return AuthException(
          'خطأ في تسجيل الدخول: ${error.message}',
          code: 'general_error',
          originalError: error,
        );
    }
  }

  /// عرض رسالة خطأ للمستخدم
  static void showErrorSnackBar(BuildContext context, AppException error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(error.getLocalizedMessage()),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'common.ok'.tr(),
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// عرض حوار خطأ
  static Future<void> showErrorDialog(
    BuildContext context,
    AppException error, {
    VoidCallback? onRetry,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('common.error'.tr()),
        content: Text(error.getLocalizedMessage()),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: Text('common.retry'.tr()),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('common.ok'.tr()),
          ),
        ],
      ),
    );
  }

  /// تسجيل الخطأ للمراقبة
  static void logError(AppException error, {StackTrace? stackTrace}) {
    _logger.e(
      'App Error: ${error.message}',
      error: error.originalError,
      stackTrace: stackTrace,
    );

    // هنا يمكن إضافة تكامل مع خدمات المراقبة مثل Crashlytics
    // FirebaseCrashlytics.instance.recordError(error, stackTrace);
  }

  /// معالجة الأخطاء غير المتوقعة
  static void handleUnexpectedError(dynamic error, StackTrace stackTrace) {
    final appError = handleError(error);
    logError(appError, stackTrace: stackTrace);
  }

  /// تسجيل آمن للأخطاء (بدون معلومات حساسة)
  static void _logErrorSafely(dynamic error) {
    try {
      final sanitizedMessage = _sanitizeErrorMessage(error.toString());
      _logger.e('Error occurred: $sanitizedMessage');
    } catch (e) {
      _logger.e('Error occurred: [Error details hidden for security]');
    }
  }

  /// تنظيف رسائل الأخطاء من المعلومات الحساسة
  static String _sanitizeErrorMessage(String message) {
    // إزالة المعلومات الحساسة
    String sanitized = message;

    // إزالة JWT tokens
    sanitized = sanitized.replaceAll(RegExp(r'eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*'), '[JWT_TOKEN_HIDDEN]');

    // إزالة API keys
    sanitized = sanitized.replaceAll(RegExp(r'[A-Za-z0-9]{32,}'), '[API_KEY_HIDDEN]');

    // إزالة URLs مع معلومات حساسة
    sanitized = sanitized.replaceAll(RegExp(r'https?://[^\s]+'), '[URL_HIDDEN]');

    // إزالة أرقام الهوية
    sanitized = sanitized.replaceAll(RegExp(r'\b\d{10}\b'), '[NATIONAL_ID_HIDDEN]');

    // إزالة كلمات المرور
    sanitized = sanitized.replaceAll(RegExp(r'password["\s]*[:=]["\s]*[^"\s,}]+', caseSensitive: false), 'password: [HIDDEN]');

    return sanitized;
  }

  /// تنظيف كائن الخطأ من المعلومات الحساسة
  static dynamic _sanitizeError(dynamic error) {
    if (error == null) return null;

    try {
      // إذا كان الخطأ يحتوي على معلومات حساسة، أرجع نسخة منظفة
      final errorString = error.toString();
      if (_containsSensitiveInfo(errorString)) {
        return Exception('Error details hidden for security');
      }
      return error;
    } catch (e) {
      return Exception('Error details hidden for security');
    }
  }

  /// فحص وجود معلومات حساسة في النص
  static bool _containsSensitiveInfo(String text) {
    final sensitivePatterns = [
      RegExp(r'eyJ[A-Za-z0-9-_=]+'), // JWT tokens
      RegExp(r'[A-Za-z0-9]{32,}'), // API keys
      RegExp(r'\b\d{10}\b'), // National IDs
      RegExp(r'password', caseSensitive: false),
      RegExp(r'secret', caseSensitive: false),
      RegExp(r'token', caseSensitive: false),
      RegExp(r'key', caseSensitive: false),
    ];

    return sensitivePatterns.any((pattern) => pattern.hasMatch(text));
  }
}
