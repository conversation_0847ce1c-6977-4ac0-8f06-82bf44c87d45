# 📱 دليل تحديث تطبيق الكاميرا للجدول الموحد

## 🎯 الهدف
تحديث تطبيق الكاميرا للتعامل مع الجدول الموحد الجديد `users_unified` بدلاً من الجدولين المنفصلين.

---

## 📋 الملفات الجديدة المنشأة

### 1. **خدمة المصادقة الموحدة**
- **الملف:** `lib/core/services/unified_auth_service.dart`
- **الوظيفة:** إدارة تسجيل الدخول/الخروج مع الجدول الموحد
- **الميزات:**
  - تسجيل دخول محسن
  - إدارة جلسات متقدمة
  - Heartbeat تلقائي
  - تحديث الموقع

### 2. **خدمة الوسائط الموحدة**
- **الملف:** `lib/core/services/unified_media_service.dart`
- **الوظيفة:** رفع الصور والفيديوهات مع الجدول الموحد
- **الميزات:**
  - رفع محسن للصور والفيديوهات
  - تحديث إحصائيات المستخدم
  - معالجة أخطاء محسنة

---

## 🔄 التحديثات المطلوبة

### **المرحلة 1: استبدال خدمات المصادقة**

#### **أ. تحديث ملف تسجيل الدخول:**
```dart
// بدلاً من:
import 'package:moon_memory/core/services/auth_service.dart';

// استخدم:
import 'package:moon_memory/core/services/unified_auth_service.dart';

class LoginScreen extends StatefulWidget {
  // ...
}

class _LoginScreenState extends State<LoginScreen> {
  final _authService = UnifiedAuthService();

  Future<void> _login() async {
    final result = await _authService.signIn(
      nationalId: _nationalIdController.text,
      password: _passwordController.text,
      locationLat: _currentLocation?.latitude,
      locationLng: _currentLocation?.longitude,
      locationName: _currentLocationName,
    );

    if (result['success']) {
      // تسجيل دخول ناجح
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result['message'])),
      );
    }
  }
}
```

#### **ب. تحديث ملف الصفحة الرئيسية:**
```dart
import 'package:moon_memory/core/services/unified_auth_service.dart';

class HomeScreen extends StatefulWidget {
  // ...
}

class _HomeScreenState extends State<HomeScreen> {
  final _authService = UnifiedAuthService();
  Map<String, dynamic>? _currentUser;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    final user = await _authService.getCurrentUser();
    setState(() {
      _currentUser = user;
    });
  }

  Future<void> _logout() async {
    await _authService.signOut();
    Navigator.pushReplacementNamed(context, '/login');
  }
}
```

### **المرحلة 2: استبدال خدمات الرفع**

#### **أ. تحديث ملف الكاميرا:**
```dart
// بدلاً من:
import 'package:moon_memory/core/services/auto_upload_service.dart';

// استخدم:
import 'package:moon_memory/core/services/unified_media_service.dart';

class CameraScreen extends StatefulWidget {
  // ...
}

class _CameraScreenState extends State<CameraScreen> {
  final _mediaService = UnifiedMediaService();

  Future<void> _uploadPhoto(File imageFile) async {
    final result = await _mediaService.uploadPhoto(
      imageFile: imageFile,
      location: _selectedLocation,
      locationLat: _currentLocation?.latitude,
      locationLng: _currentLocation?.longitude,
      locationName: _currentLocationName,
    );

    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم رفع الصورة بنجاح')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في رفع الصورة: ${result['message']}')),
      );
    }
  }

  Future<void> _uploadVideo(File videoFile) async {
    final result = await _mediaService.uploadVideo(
      videoFile: videoFile,
      location: _selectedLocation,
      durationSeconds: _videoDuration,
      locationLat: _currentLocation?.latitude,
      locationLng: _currentLocation?.longitude,
      locationName: _currentLocationName,
    );

    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم رفع الفيديو بنجاح')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في رفع الفيديو: ${result['message']}')),
      );
    }
  }
}
```

#### **ب. تحديث ملف المعرض:**
```dart
import 'package:moon_memory/core/services/unified_media_service.dart';

class GalleryScreen extends StatefulWidget {
  // ...
}

class _GalleryScreenState extends State<GalleryScreen> {
  final _mediaService = UnifiedMediaService();
  List<dynamic> _photos = [];
  List<dynamic> _videos = [];

  @override
  void initState() {
    super.initState();
    _loadUserMedia();
  }

  Future<void> _loadUserMedia() async {
    final result = await _mediaService.getUserMedia(
      limit: 50,
      locationType: _selectedLocationType,
    );

    if (result['success']) {
      setState(() {
        _photos = result['photos'];
        _videos = result['videos'];
      });
    }
  }
}
```

### **المرحلة 3: تحديث إدارة الجلسات**

#### **أ. تحديث ملف إدارة الجلسات:**
```dart
import 'package:moon_memory/core/services/unified_auth_service.dart';

class SessionManager {
  static final SessionManager _instance = SessionManager._internal();
  factory SessionManager() => _instance;
  SessionManager._internal();

  final _authService = UnifiedAuthService();

  // فحص صحة الجلسة
  Future<bool> isSessionValid() async {
    return await _authService.isSessionValid();
  }

  // تحديث الموقع
  Future<void> updateLocation(double lat, double lng, String? name) async {
    await _authService.updateLocation(
      latitude: lat,
      longitude: lng,
      locationName: name,
    );
  }

  // الحصول على المستخدم الحالي
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return await _authService.getCurrentUser();
  }

  // تسجيل الخروج
  Future<void> logout() async {
    await _authService.signOut();
  }
}
```

---

## 🔧 التحديثات الإضافية

### **1. تحديث ملف main.dart:**
```dart
import 'package:moon_memory/core/services/unified_auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Supabase.initialize(
    url: 'https://xufiuvdtfusbaerwrkzb.supabase.co',
    anonKey: 'YOUR_ANON_KEY',
  );

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Moon Memory',
      home: SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final _authService = UnifiedAuthService();

  @override
  void initState() {
    super.initState();
    _checkSession();
  }

  Future<void> _checkSession() async {
    await Future.delayed(Duration(seconds: 2)); // شاشة البداية

    final isValid = await _authService.isSessionValid();
    
    if (isValid) {
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار التطبيق
            Image.asset('assets/logo.png', width: 150),
            SizedBox(height: 20),
            Text('ذاكرة القمر', style: TextStyle(fontSize: 24)),
            SizedBox(height: 20),
            CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}
```

### **2. تحديث pubspec.yaml:**
```yaml
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^2.0.0
  logger: ^2.0.0
  crypto: ^3.0.3
  path: ^1.8.3
  # ... باقي التبعيات
```

---

## ⚠️ نقاط مهمة

### **1. قبل التحديث:**
- ✅ تأكد من تشغيل سكريبت توحيد الجدولين
- ✅ اختبر الجدول الموحد في بيئة التطوير
- ✅ عمل نسخة احتياطية من الكود الحالي

### **2. أثناء التحديث:**
- ✅ استبدل الخدمات تدريجياً
- ✅ اختبر كل تحديث على حدة
- ✅ تأكد من عمل تسجيل الدخول/الخروج

### **3. بعد التحديث:**
- ✅ اختبر جميع وظائف التطبيق
- ✅ تأكد من رفع الصور والفيديوهات
- ✅ راقب الأخطاء والأداء

---

## 🎯 النتيجة المتوقعة

بعد تطبيق هذه التحديثات:
- ✅ تطبيق يعمل مع الجدول الموحد
- ✅ إدارة جلسات محسنة ومستقرة
- ✅ رفع ملفات أكثر موثوقية
- ✅ عدم وجود تضارب في البيانات
- ✅ أداء أفضل وصيانة أسهل

---

## 📞 الدعم

في حالة وجود مشاكل:
1. راجع logs التطبيق للأخطاء
2. تأكد من تشغيل سكريبت قاعدة البيانات
3. اختبر الاتصال مع الجدول الموحد
4. تواصل مع فريق التطوير مع تفاصيل الخطأ
