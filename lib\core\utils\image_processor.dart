import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:logger/logger.dart';
import 'package:google_fonts/google_fonts.dart';

class ImageProcessor {
  static final _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  static Future<ui.Image> loadImageFromAsset(String assetPath) async {
    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo fi = await codec.getNextFrame();
    return fi.image;
  }

  static Future<ui.Image> loadImageFromFile(String imagePath) async {
    final File imageFile = File(imagePath);
    final Uint8List imageBytes = await imageFile.readAsBytes();
    final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
    final ui.FrameInfo fi = await codec.getNextFrame();
    return fi.image;
  }

  static String _formatDate(DateTime date) {
    final List<String> arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${date.day} ${arabicMonths[date.month - 1]} ${date.year}';
  }

  static String _formatTime(DateTime time) {
    final hour = time.hour % 12 == 0 ? 12 : time.hour % 12;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour < 12 ? 'ص' : 'م';
    final dayNightIndicator = time.hour >= 6 && time.hour < 18 ? '☀️' : '🌙';
    return '$hour:$minute $period $dayNightIndicator';
  }

  static Future<String> processImage({
    required String imagePath,
    required String username,
    required String location,
    String? geoLocation,
  }) async {
    _logger.d('Starting image processing...');
    _logger.d('Username to add: $username');
    _logger.d('Location to add: $location');

    try {
      // قراءة الصورة الأصلية
      final ui.Image originalImage = await loadImageFromFile(imagePath);
      final Size size = Size(originalImage.width.toDouble(), originalImage.height.toDouble());

      // قراءة صور العلامات المائية
      final ui.Image rightIcon = await loadImageFromAsset('assets/images/icon_right.png');
      final ui.Image leftIcon = await loadImageFromAsset('assets/images/icon_left.png');

      _logger.d('Images loaded successfully');

      // إنشاء recorder للرسم
      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      // رسم الصورة الأصلية
      canvas.drawImage(originalImage, Offset.zero, Paint());

      // حساب حجم العلامات المائية
      final double rightIconSize = size.width * 0.2;
      final double leftIconSize = size.width * 0.2;
      final double aspectRatioRight = rightIcon.width / rightIcon.height;
      final double aspectRatioLeft = leftIcon.width / leftIcon.height;

      // رسم العلامات المائية
      final watermarkPaint = Paint()
        ..filterQuality = FilterQuality.high
        ..isAntiAlias = true;

      // العلامة المائية اليمنى
      canvas.saveLayer(null, watermarkPaint);
      canvas.drawImageRect(
        rightIcon,
        Rect.fromLTWH(0, 0, rightIcon.width.toDouble(), rightIcon.height.toDouble()),
        Rect.fromLTWH(
          size.width - rightIconSize - 16,
          16,
          rightIconSize,
          rightIconSize / aspectRatioRight,
        ),
        watermarkPaint,
      );
      canvas.restore();

      // العلامة المائية اليسرى
      canvas.saveLayer(null, watermarkPaint);
      canvas.drawImageRect(
        leftIcon,
        Rect.fromLTWH(0, 0, leftIcon.width.toDouble(), leftIcon.height.toDouble()),
        Rect.fromLTWH(
          16,
          16,
          leftIconSize,
          leftIconSize / aspectRatioLeft,
        ),
        watermarkPaint,
      );
      canvas.restore();

      _logger.d('Watermarks added successfully');

      // تعريف أنماط النصوص
      const shadowStyle = ui.Shadow(
        color: Color(0xFF000000),
        blurRadius: 3,
        offset: Offset(1, 1),
      );

      final locationStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 36,
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final dateStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 40,
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final timeStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 48,
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      final userNameStyle = GoogleFonts.cairo(
        color: const Color(0xFFFFFFFF),
        fontSize: 32,
        fontWeight: FontWeight.bold,
        shadows: const [shadowStyle],
      );

      // إعداد التاريخ والوقت
      final now = DateTime.now();
      final dateStr = _formatDate(now);
      final timeStr = _formatTime(now);

      // إعداد نص الموقع (موقع التصوير + الموقع الجغرافي)
      String locationText = location;
      if (geoLocation != null && geoLocation.isNotEmpty && geoLocation != 'جاري تحديد الموقع...') {
        locationText = '$location\n$geoLocation';
      }

      // رسم معلومات الموقع والتاريخ والوقت
      final mainTextPainter = TextPainter(
        text: TextSpan(
          children: [
            TextSpan(
              text: '$locationText\n',
              style: locationStyle,
            ),
            TextSpan(
              text: '$dateStr\n',
              style: dateStyle,
            ),
            TextSpan(
              text: timeStr,
              style: timeStyle,
            ),
          ],
        ),
        textDirection: ui.TextDirection.rtl,
        textAlign: TextAlign.right,
      );

      mainTextPainter.layout(maxWidth: size.width - 32);
      mainTextPainter.paint(
        canvas,
        Offset(
          size.width - mainTextPainter.width - 24,
          size.height - mainTextPainter.height - 48,
        ),
      );

      // رسم اسم المستخدم
      final userTextPainter = TextPainter(
        text: TextSpan(
          text: username,
          style: userNameStyle,
        ),
        textDirection: ui.TextDirection.rtl,
        textAlign: TextAlign.left,
      );

      userTextPainter.layout(maxWidth: size.width - 32);
      userTextPainter.paint(
        canvas,
        Offset(
          24,
          size.height - userTextPainter.height - 24,
        ),
      );

      _logger.d('Text added successfully');

      // تحويل الرسم إلى صورة
      final ui.Picture picture = recorder.endRecording();
      final ui.Image renderedImage = await picture.toImage(
        originalImage.width,
        originalImage.height,
      );

      // تحويل الصورة إلى PNG أولاً ثم إلى JPEG
      final ByteData? byteData = await renderedImage.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData == null) {
        throw Exception('فشل في معالجة الصورة');
      }

      // حفظ الصورة بصيغة PNG صحيحة
      final directory = await getTemporaryDirectory();
      final String newPath = path.join(directory.path, 'watermarked_${DateTime.now().millisecondsSinceEpoch}.png');
      final File newImage = File(newPath);

      // حفظ البيانات بصيغة PNG
      await newImage.writeAsBytes(byteData.buffer.asUint8List(), flush: true);

      _logger.d('Image saved successfully to: $newPath');

      // تحرير الموارد
      originalImage.dispose();
      rightIcon.dispose();
      leftIcon.dispose();
      renderedImage.dispose();

      // التأكد من تحرير الذاكرة
      picture.dispose();

      return newPath;
    } catch (e) {
      _logger.e('Error in image processing', error: e);
      rethrow;
    }
  }



  /// تحسين حجم الصورة للعرض
  static Future<ui.Image> resizeImage(ui.Image image, {int? maxWidth, int? maxHeight}) async {
    int targetWidth = image.width;
    int targetHeight = image.height;

    if (maxWidth != null && targetWidth > maxWidth) {
      targetHeight = (targetHeight * maxWidth / targetWidth).round();
      targetWidth = maxWidth;
    }

    if (maxHeight != null && targetHeight > maxHeight) {
      targetWidth = (targetWidth * maxHeight / targetHeight).round();
      targetHeight = maxHeight;
    }

    if (targetWidth == image.width && targetHeight == image.height) {
      return image;
    }

    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTWH(0, 0, targetWidth.toDouble(), targetHeight.toDouble()),
      Paint()..filterQuality = FilterQuality.high,
    );

    final ui.Picture picture = recorder.endRecording();
    final ui.Image resizedImage = await picture.toImage(targetWidth, targetHeight);

    picture.dispose();
    return resizedImage;
  }
}
