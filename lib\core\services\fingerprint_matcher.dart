import 'package:logger/logger.dart';
import 'advanced_device_fingerprint.dart';

/// نظام مقارنة البصمات الرقمية
/// يحدد ما إذا كانت البصمتان تنتميان لنفس الجهاز
class FingerprintMatcher {
  final _logger = Logger();
  
  // عتبات المقارنة
  static const double _highConfidenceThreshold = 0.85;  // 85% تطابق عالي
  static const double _mediumConfidenceThreshold = 0.70; // 70% تطابق متوسط
  static const double _lowConfidenceThreshold = 0.50;    // 50% تطابق منخفض
  
  // أوزان المقارنة للعوامل المختلفة
  static const Map<String, double> _comparisonWeights = {
    'androidId': 30.0,        // الأهم - يجب أن يتطابق
    'buildFingerprint': 25.0, // مهم جداً - قد يتغير مع التحديثات
    'hardwareInfo': 20.0,     // مهم - ثابت نسبياً
    'deviceModel': 10.0,      // أساسي - يجب أن يتطابق
    'manufacturer': 8.0,      // أساسي - يجب أن يتطابق
    'product': 5.0,           // مفيد - قد يتغير
    'systemInfo': 2.0,        // متغير - يتغير مع التحديثات
  };

  /// مقارنة بصمتين لتحديد ما إذا كانتا لنفس الجهاز
  FingerprintMatchResult compareFingerprints(
    DeviceFingerprintData stored,
    DeviceFingerprintData current,
  ) {
    try {
      _logger.i('بدء مقارنة البصمات الرقمية...');
      
      // فحص أولي للبصمات
      if (!_isValidForComparison(stored) || !_isValidForComparison(current)) {
        return FingerprintMatchResult(
          isMatch: false,
          confidence: 0.0,
          matchLevel: MatchLevel.noMatch,
          reason: 'بصمة غير صالحة للمقارنة',
        );
      }
      
      // مقارنة العوامل الأساسية
      final coreMatch = _compareCoreFactors(stored, current);
      
      // مقارنة العوامل المتغيرة
      final variableMatch = _compareVariableFactors(stored, current);
      
      // حساب النتيجة الإجمالية
      final totalScore = (coreMatch.score * 0.8) + (variableMatch.score * 0.2);
      
      // تحديد مستوى التطابق
      final matchLevel = _determineMatchLevel(totalScore);
      
      // تحديد ما إذا كان تطابق أم لا
      final isMatch = totalScore >= _lowConfidenceThreshold;
      
      final result = FingerprintMatchResult(
        isMatch: isMatch,
        confidence: totalScore,
        matchLevel: matchLevel,
        coreFactorsMatch: coreMatch,
        variableFactorsMatch: variableMatch,
        reason: _generateMatchReason(totalScore, coreMatch, variableMatch),
      );
      
      _logger.i('نتيجة المقارنة: ${result.isMatch ? 'تطابق' : 'عدم تطابق'} - الثقة: ${result.confidence.toStringAsFixed(2)}%');
      
      return result;
      
    } catch (e) {
      _logger.e('خطأ في مقارنة البصمات: $e');
      return FingerprintMatchResult(
        isMatch: false,
        confidence: 0.0,
        matchLevel: MatchLevel.error,
        reason: 'خطأ في المقارنة: $e',
      );
    }
  }

  /// فحص صحة البصمة للمقارنة
  bool _isValidForComparison(DeviceFingerprintData fingerprint) {
    return fingerprint.androidId?.isNotEmpty == true &&
           fingerprint.buildFingerprint?.isNotEmpty == true &&
           fingerprint.manufacturer?.isNotEmpty == true &&
           fingerprint.deviceModel?.isNotEmpty == true;
  }

  /// مقارنة العوامل الأساسية (الثابتة)
  FactorMatchResult _compareCoreFactors(
    DeviceFingerprintData stored,
    DeviceFingerprintData current,
  ) {
    double totalWeight = 0.0;
    double matchedWeight = 0.0;
    final details = <String, bool>{};
    
    // مقارنة Android ID
    final androidIdWeight = _comparisonWeights['androidId']!;
    totalWeight += androidIdWeight;
    final androidIdMatch = stored.androidId == current.androidId;
    details['androidId'] = androidIdMatch;
    if (androidIdMatch) matchedWeight += androidIdWeight;
    
    // مقارنة Build Fingerprint (مع تساهل للتحديثات)
    final buildFingerprintWeight = _comparisonWeights['buildFingerprint']!;
    totalWeight += buildFingerprintWeight;
    final buildMatch = _compareBuildFingerprints(stored.buildFingerprint, current.buildFingerprint);
    details['buildFingerprint'] = buildMatch;
    if (buildMatch) matchedWeight += buildFingerprintWeight;
    
    // مقارنة Hardware Info
    final hardwareWeight = _comparisonWeights['hardwareInfo']!;
    totalWeight += hardwareWeight;
    final hardwareMatch = _compareHardwareInfo(stored.hardwareInfo, current.hardwareInfo);
    details['hardwareInfo'] = hardwareMatch;
    if (hardwareMatch) matchedWeight += hardwareWeight;
    
    // مقارنة Device Model
    final modelWeight = _comparisonWeights['deviceModel']!;
    totalWeight += modelWeight;
    final modelMatch = stored.deviceModel == current.deviceModel;
    details['deviceModel'] = modelMatch;
    if (modelMatch) matchedWeight += modelWeight;
    
    // مقارنة Manufacturer
    final manufacturerWeight = _comparisonWeights['manufacturer']!;
    totalWeight += manufacturerWeight;
    final manufacturerMatch = stored.manufacturer == current.manufacturer;
    details['manufacturer'] = manufacturerMatch;
    if (manufacturerMatch) matchedWeight += manufacturerWeight;
    
    // مقارنة Product
    final productWeight = _comparisonWeights['product']!;
    totalWeight += productWeight;
    final productMatch = stored.product == current.product;
    details['product'] = productMatch;
    if (productMatch) matchedWeight += productWeight;
    
    final score = totalWeight > 0 ? (matchedWeight / totalWeight) : 0.0;
    
    return FactorMatchResult(
      score: score,
      details: details,
      totalFactors: details.length,
      matchedFactors: details.values.where((match) => match).length,
    );
  }

  /// مقارنة العوامل المتغيرة
  FactorMatchResult _compareVariableFactors(
    DeviceFingerprintData stored,
    DeviceFingerprintData current,
  ) {
    double totalWeight = 0.0;
    double matchedWeight = 0.0;
    final details = <String, bool>{};
    
    // مقارنة System Info (مع تساهل للتحديثات)
    final systemWeight = _comparisonWeights['systemInfo']!;
    totalWeight += systemWeight;
    final systemMatch = _compareSystemInfo(stored.systemInfo, current.systemInfo);
    details['systemInfo'] = systemMatch;
    if (systemMatch) matchedWeight += systemWeight;
    
    // مقارنة Screen Info
    if (stored.screenInfo?.isNotEmpty == true && current.screenInfo?.isNotEmpty == true) {
      totalWeight += 5.0;
      final screenMatch = stored.screenInfo == current.screenInfo;
      details['screenInfo'] = screenMatch;
      if (screenMatch) matchedWeight += 5.0;
    }
    
    // مقارنة CPU Info
    if (stored.cpuInfo?.isNotEmpty == true && current.cpuInfo?.isNotEmpty == true) {
      totalWeight += 3.0;
      final cpuMatch = stored.cpuInfo == current.cpuInfo;
      details['cpuInfo'] = cpuMatch;
      if (cpuMatch) matchedWeight += 3.0;
    }
    
    final score = totalWeight > 0 ? (matchedWeight / totalWeight) : 1.0;
    
    return FactorMatchResult(
      score: score,
      details: details,
      totalFactors: details.length,
      matchedFactors: details.values.where((match) => match).length,
    );
  }

  /// مقارنة Build Fingerprints مع تساهل للتحديثات
  bool _compareBuildFingerprints(String? stored, String? current) {
    if (stored == null || current == null) return false;
    if (stored == current) return true;
    
    // فحص ما إذا كان التغيير بسبب تحديث أمني فقط
    final storedParts = stored.split('/');
    final currentParts = current.split('/');
    
    if (storedParts.length >= 3 && currentParts.length >= 3) {
      // مقارنة الأجزاء الأساسية (تجاهل تاريخ البناء)
      return storedParts[0] == currentParts[0] && // الشركة المصنعة
             storedParts[1] == currentParts[1] && // اسم المنتج
             storedParts[2] == currentParts[2];   // اسم الجهاز
    }
    
    return false;
  }

  /// مقارنة Hardware Info
  bool _compareHardwareInfo(String? stored, String? current) {
    if (stored == null || current == null) return false;
    if (stored == current) return true;
    
    // مقارنة الأجزاء الأساسية من معلومات الهاردوير
    final storedParts = stored.split('|');
    final currentParts = current.split('|');
    
    int matches = 0;
    int total = 0;
    
    for (final storedPart in storedParts) {
      for (final currentPart in currentParts) {
        if (storedPart.split(':')[0] == currentPart.split(':')[0]) {
          total++;
          if (storedPart == currentPart) matches++;
          break;
        }
      }
    }
    
    // يجب أن يتطابق 80% على الأقل من معلومات الهاردوير
    return total > 0 && (matches / total) >= 0.8;
  }

  /// مقارنة System Info مع تساهل للتحديثات
  bool _compareSystemInfo(String? stored, String? current) {
    if (stored == null || current == null) return false;
    if (stored == current) return true;
    
    // استخراج إصدار Android الأساسي
    final storedVersion = _extractAndroidVersion(stored);
    final currentVersion = _extractAndroidVersion(current);
    
    if (storedVersion != null && currentVersion != null) {
      // السماح بالترقية لإصدار أحدث أو نفس الإصدار
      return currentVersion >= storedVersion;
    }
    
    return false;
  }

  /// استخراج رقم إصدار Android
  int? _extractAndroidVersion(String systemInfo) {
    try {
      final parts = systemInfo.split('|');
      for (final part in parts) {
        if (part.startsWith('OS:')) {
          final version = part.substring(3);
          return int.tryParse(version.split('.')[0]);
        }
      }
    } catch (e) {
      // تجاهل الأخطاء
    }
    return null;
  }

  /// تحديد مستوى التطابق
  MatchLevel _determineMatchLevel(double score) {
    if (score >= _highConfidenceThreshold) {
      return MatchLevel.highConfidence;
    } else if (score >= _mediumConfidenceThreshold) {
      return MatchLevel.mediumConfidence;
    } else if (score >= _lowConfidenceThreshold) {
      return MatchLevel.lowConfidence;
    } else {
      return MatchLevel.noMatch;
    }
  }

  /// إنشاء سبب التطابق أو عدم التطابق
  String _generateMatchReason(
    double totalScore,
    FactorMatchResult coreMatch,
    FactorMatchResult variableMatch,
  ) {
    if (totalScore >= _highConfidenceThreshold) {
      return 'تطابق عالي الثقة - جميع العوامل الأساسية متطابقة';
    } else if (totalScore >= _mediumConfidenceThreshold) {
      return 'تطابق متوسط الثقة - معظم العوامل الأساسية متطابقة';
    } else if (totalScore >= _lowConfidenceThreshold) {
      return 'تطابق منخفض الثقة - بعض العوامل الأساسية متطابقة';
    } else {
      final failedFactors = <String>[];
      coreMatch.details.forEach((factor, isMatch) {
        if (!isMatch) failedFactors.add(factor);
      });
      return 'عدم تطابق - العوامل غير المتطابقة: ${failedFactors.join(', ')}';
    }
  }
}

/// نتيجة مقارنة البصمات
class FingerprintMatchResult {
  final bool isMatch;
  final double confidence;
  final MatchLevel matchLevel;
  final FactorMatchResult? coreFactorsMatch;
  final FactorMatchResult? variableFactorsMatch;
  final String reason;
  
  FingerprintMatchResult({
    required this.isMatch,
    required this.confidence,
    required this.matchLevel,
    this.coreFactorsMatch,
    this.variableFactorsMatch,
    required this.reason,
  });
  
  @override
  String toString() {
    return 'FingerprintMatchResult(isMatch: $isMatch, confidence: ${confidence.toStringAsFixed(2)}%, level: $matchLevel)';
  }
}

/// نتيجة مقارنة مجموعة من العوامل
class FactorMatchResult {
  final double score;
  final Map<String, bool> details;
  final int totalFactors;
  final int matchedFactors;
  
  FactorMatchResult({
    required this.score,
    required this.details,
    required this.totalFactors,
    required this.matchedFactors,
  });
  
  double get matchPercentage => totalFactors > 0 ? (matchedFactors / totalFactors) : 0.0;
}

/// مستويات التطابق
enum MatchLevel {
  highConfidence,   // ثقة عالية - نفس الجهاز بالتأكيد
  mediumConfidence, // ثقة متوسطة - على الأرجح نفس الجهاز
  lowConfidence,    // ثقة منخفضة - قد يكون نفس الجهاز
  noMatch,          // لا يوجد تطابق - جهاز مختلف
  error,            // خطأ في المقارنة
}
