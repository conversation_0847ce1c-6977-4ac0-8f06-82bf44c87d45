include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "**/generated_plugin_registrant.dart"

linter:
  rules:
    # تعطيل القواعد المزعجة
    avoid_print: false
    prefer_single_quotes: false
    prefer_const_constructors: false
    prefer_const_literals_to_create_immutables: false
    avoid_unnecessary_containers: false
    use_key_in_widget_constructors: false
    sized_box_for_whitespace: false
    sort_child_properties_last: false
    lines_longer_than_80_chars: false
    always_specify_types: false
    prefer_expression_function_bodies: false
    prefer_const_declarations: false
    prefer_final_fields: false
    prefer_final_locals: false
    unnecessary_const: false
    unnecessary_new: false
    avoid_redundant_argument_values: false
    prefer_collection_literals: false
    prefer_if_null_operators: false
    prefer_null_aware_operators: false
    unnecessary_null_in_if_null_operators: false
    avoid_init_to_null: false
    unnecessary_nullable_for_final_variable_declarations: false
