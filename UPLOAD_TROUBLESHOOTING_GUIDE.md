# 🔧 دليل استكشاف أخطاء الرفع - Moon Memory

## 🎯 **المشاكل الشائعة وحلولها:**

### **1. مشكلة: الملفات لا ترفع نهائياً**

#### **الأعراض:**
- الصور والفيديوهات تحفظ محلياً لكن لا ترفع
- لا توجد رسائل خطأ واضحة
- قائمة الرفع تتراكم

#### **الحلول:**
```sql
-- 1. فحص قاعدة البيانات
SELECT * FROM upload_queue WHERE status = 'pending' LIMIT 10;

-- 2. فحص الأخطاء
SELECT error_message, COUNT(*) 
FROM upload_queue 
WHERE status = 'failed' 
GROUP BY error_message;
```

```dart
// 3. فحص الـ logs في التطبيق
// ابحث عن هذه الرسائل:
// ❌ المستخدم غير مصادق عليه
// ⚠️ لا يوجد اتصال بالإنترنت
// ❌ فشل في رفع الملف
```

### **2. مشكلة: timeout في الرفع**

#### **الأعراض:**
- رسائل "Upload timeout"
- الملفات الكبيرة لا ترفع
- الرفع يتوقف في المنتصف

#### **الحلول:**
```dart
// تحديث timeout في EnhancedUploadService
static const Duration _uploadTimeout = Duration(minutes: 3); // زيادة الوقت

// أو تقسيم الملفات الكبيرة
if (fileSize > 50 * 1024 * 1024) { // 50MB
  // استخدام رفع متقطع
}
```

### **3. مشكلة: تضارب في أسماء الحقول**

#### **الأعراض:**
- خطأ "column does not exist"
- البيانات لا تحفظ في قاعدة البيانات
- رسائل خطأ SQL

#### **الحلول:**
```sql
-- تطبيق إصلاح قاعدة البيانات
\i database/fix_upload_issues.sql

-- فحص بنية الجداول
\d photos
\d videos
\d upload_queue
```

### **4. مشكلة: Storage bucket غير متاح**

#### **الأعراض:**
- رسائل "Videos bucket not available"
- الملفات تحفظ برابط محلي
- فشل في الوصول للملفات

#### **الحلول:**
```sql
-- 1. إنشاء videos bucket في Supabase
-- اذهب إلى Storage > Create bucket > videos

-- 2. تعديل policies
CREATE POLICY "Allow authenticated uploads" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'videos');

CREATE POLICY "Allow public access" ON storage.objects
FOR SELECT TO public
USING (bucket_id = 'videos');
```

### **5. مشكلة: تراكم الملفات المحلية**

#### **الأعراض:**
- مساحة التخزين ممتلئة
- التطبيق بطيء
- ملفات قديمة لا تحذف

#### **الحلول:**
```dart
// تفعيل الحذف التلقائي
await _deleteLocalFile(filePath); // بعد الرفع الناجح

// تنظيف دوري
await enhancedUploadService.cleanupUploadQueue();
```

## 🔍 **أدوات التشخيص:**

### **1. فحص حالة الرفع:**
```dart
// في التطبيق
final stats = await EnhancedUploadService().getUploadStats();
print('Upload Stats: $stats');
```

### **2. فحص قاعدة البيانات:**
```sql
-- إحصائيات سريعة
SELECT * FROM upload_stats;

-- الملفات المعلقة
SELECT file_name, status, upload_attempts, error_message 
FROM upload_queue 
WHERE status != 'uploaded' 
ORDER BY created_at DESC;

-- الملفات الفاشلة
SELECT error_message, COUNT(*) as count
FROM upload_queue 
WHERE status = 'failed'
GROUP BY error_message
ORDER BY count DESC;
```

### **3. فحص الشبكة:**
```dart
// فحص الاتصال
final connectivity = await Connectivity().checkConnectivity();
print('Connection: $connectivity');

// فحص سرعة الرفع
final stopwatch = Stopwatch()..start();
// ... رفع الملف
stopwatch.stop();
print('Upload took: ${stopwatch.elapsedMilliseconds}ms');
```

## 🛠️ **إصلاحات سريعة:**

### **إصلاح 1: إعادة تعيين قائمة الرفع**
```sql
-- حذف الملفات الفاشلة القديمة
DELETE FROM upload_queue 
WHERE status = 'failed' 
AND created_at < NOW() - INTERVAL '7 days';

-- إعادة تعيين الملفات المعلقة
UPDATE upload_queue 
SET status = 'pending', upload_attempts = 0, error_message = NULL
WHERE status = 'failed' AND upload_attempts < 3;
```

### **إصلاح 2: تنظيف التخزين المحلي**
```dart
// حذف الملفات المحلية القديمة
final directory = await getApplicationDocumentsDirectory();
final galleryDir = Directory('${directory.path}/gallery');

if (await galleryDir.exists()) {
  final files = galleryDir.listSync();
  for (final file in files) {
    final stat = await file.stat();
    if (DateTime.now().difference(stat.modified).inDays > 7) {
      await file.delete();
    }
  }
}
```

### **إصلاح 3: إعادة تشغيل خدمة الرفع**
```dart
// في main.dart أو أي مكان مناسب
EnhancedUploadService().stopAutoUpload();
await Future.delayed(Duration(seconds: 2));
EnhancedUploadService().startAutoUpload();
```

## 📊 **مراقبة الأداء:**

### **مؤشرات مهمة:**
1. **معدل نجاح الرفع**: > 95%
2. **متوسط وقت الرفع**: < 30 ثانية للصور، < 2 دقيقة للفيديوهات
3. **حجم قائمة الانتظار**: < 10 ملفات
4. **معدل الأخطاء**: < 5%

### **تنبيهات:**
```dart
// إضافة تنبيهات للمشاكل
if (pendingCount > 20) {
  logger.w('⚠️ قائمة الرفع كبيرة: $pendingCount ملف');
}

if (failureRate > 0.1) {
  logger.e('❌ معدل فشل عالي: ${(failureRate * 100).toInt()}%');
}
```

## 🔧 **صيانة دورية:**

### **يومياً:**
```sql
-- تنظيف الملفات المرفوعة القديمة
SELECT cleanup_upload_queue(1);

-- فحص الإحصائيات
SELECT * FROM upload_stats;
```

### **أسبوعياً:**
```sql
-- تنظيف شامل
SELECT cleanup_upload_queue(7);

-- فحص الملفات الفاشلة
SELECT file_type, error_message, COUNT(*)
FROM upload_queue 
WHERE status = 'failed'
GROUP BY file_type, error_message;
```

### **شهرياً:**
```sql
-- أرشفة البيانات القديمة
-- حذف الملفات المرفوعة القديمة جداً
DELETE FROM upload_queue 
WHERE status = 'uploaded' 
AND uploaded_at < NOW() - INTERVAL '30 days';
```

## 🆘 **عند الطوارئ:**

### **إذا توقف الرفع تماماً:**
1. فحص الاتصال بالإنترنت
2. فحص صحة قاعدة البيانات
3. إعادة تشغيل خدمة الرفع
4. فحص Storage buckets في Supabase
5. فحص الـ logs للأخطاء

### **إذا امتلأت مساحة التخزين:**
1. تنظيف الملفات المحلية القديمة
2. حذف الملفات المرفوعة من التخزين المحلي
3. تفعيل الحذف التلقائي
4. ضغط الملفات قبل الرفع

---

**💡 نصيحة:** استخدم `UploadStatusMonitor` widget لمراقبة حالة الرفع في الوقت الفعلي!
