import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/logger.dart';
import 'enhanced_upload_service.dart';

/// خدمة ترحيل الملفات القديمة إلى النظام الجديد
class MigrationService {
  static final MigrationService _instance = MigrationService._internal();
  factory MigrationService() => _instance;
  MigrationService._internal();

  final _logger = getLogger();
  final _supabase = Supabase.instance.client;
  final _uploadService = EnhancedUploadService();

  /// ترحيل جميع الملفات القديمة إلى النظام الجديد
  Future<void> migrateOldFiles() async {
    try {
      _logger.i('🔄 بدء ترحيل الملفات القديمة...');

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        _logger.w('⚠️ المستخدم غير مصادق عليه');
        return;
      }

      // ترحيل الصور من SharedPreferences
      await _migratePhotosFromPrefs();

      // ترحيل الفيديوهات من SharedPreferences
      await _migrateVideosFromPrefs();

      // ترحيل الملفات من المعرض المحلي
      await _migrateFilesFromGallery();

      _logger.i('✅ تم الانتهاء من ترحيل الملفات القديمة');

    } catch (e) {
      _logger.e('❌ خطأ في ترحيل الملفات القديمة: $e');
    }
  }

  /// ترحيل الصور من SharedPreferences
  Future<void> _migratePhotosFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];

      if (pendingUploads.isEmpty) {
        _logger.d('📭 لا توجد صور معلقة في SharedPreferences');
        return;
      }

      _logger.i('📸 ترحيل ${pendingUploads.length} صورة من SharedPreferences...');

      int migratedCount = 0;
      final List<String> failedUploads = [];

      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          
          // تخطي الملفات المرفوعة بالفعل
          if (uploadData['status'] == 'uploaded') {
            continue;
          }

          final filePath = uploadData['file_path'] as String?;
          final fileName = uploadData['file_name'] as String?;
          final location = uploadData['location'] as String?;

          if (filePath == null || fileName == null) {
            _logger.w('⚠️ بيانات ناقصة في الصورة: $uploadJson');
            continue;
          }

          // فحص وجود الملف
          final file = File(filePath);
          if (!await file.exists()) {
            _logger.w('⚠️ الملف غير موجود: $filePath');
            continue;
          }

          // إضافة إلى النظام الجديد
          final queueId = await _uploadService.addToUploadQueue(
            filePath: filePath,
            fileName: fileName,
            fileType: 'photo',
            location: location ?? 'unknown',
            metadata: {
              'migrated_from': 'shared_preferences',
              'original_data': uploadData,
            },
          );

          if (queueId != null) {
            migratedCount++;
            _logger.d('✅ تم ترحيل الصورة: $fileName');
          } else {
            failedUploads.add(uploadJson);
          }

        } catch (e) {
          _logger.w('⚠️ فشل في ترحيل صورة: $e');
          failedUploads.add(uploadJson);
        }
      }

      // تحديث SharedPreferences - إزالة الملفات المرحلة
      if (migratedCount > 0) {
        await prefs.setStringList('pending_uploads', failedUploads);
        _logger.i('✅ تم ترحيل $migratedCount صورة من SharedPreferences');
      }

    } catch (e) {
      _logger.e('❌ خطأ في ترحيل الصور من SharedPreferences: $e');
    }
  }

  /// ترحيل الفيديوهات من SharedPreferences
  Future<void> _migrateVideosFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_video_uploads') ?? [];

      if (pendingUploads.isEmpty) {
        _logger.d('📭 لا توجد فيديوهات معلقة في SharedPreferences');
        return;
      }

      _logger.i('🎬 ترحيل ${pendingUploads.length} فيديو من SharedPreferences...');

      int migratedCount = 0;
      final List<String> failedUploads = [];

      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = jsonDecode(uploadJson) as Map<String, dynamic>;
          
          // تخطي الملفات المرفوعة بالفعل
          if (uploadData['uploaded'] == true) {
            continue;
          }

          final filePath = uploadData['path'] as String?;
          final fileName = uploadData['fileName'] as String?;
          final location = uploadData['location'] as String?;

          if (filePath == null || fileName == null) {
            _logger.w('⚠️ بيانات ناقصة في الفيديو: $uploadJson');
            continue;
          }

          // فحص وجود الملف
          final file = File(filePath);
          if (!await file.exists()) {
            _logger.w('⚠️ الملف غير موجود: $filePath');
            continue;
          }

          // إضافة إلى النظام الجديد
          final queueId = await _uploadService.addToUploadQueue(
            filePath: filePath,
            fileName: fileName,
            fileType: 'video',
            location: location ?? 'unknown',
            metadata: {
              'migrated_from': 'shared_preferences',
              'original_data': uploadData,
            },
          );

          if (queueId != null) {
            migratedCount++;
            _logger.d('✅ تم ترحيل الفيديو: $fileName');
          } else {
            failedUploads.add(uploadJson);
          }

        } catch (e) {
          _logger.w('⚠️ فشل في ترحيل فيديو: $e');
          failedUploads.add(uploadJson);
        }
      }

      // تحديث SharedPreferences - إزالة الملفات المرحلة
      if (migratedCount > 0) {
        await prefs.setStringList('pending_video_uploads', failedUploads);
        _logger.i('✅ تم ترحيل $migratedCount فيديو من SharedPreferences');
      }

    } catch (e) {
      _logger.e('❌ خطأ في ترحيل الفيديوهات من SharedPreferences: $e');
    }
  }

  /// ترحيل الملفات من المعرض المحلي
  Future<void> _migrateFilesFromGallery() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final galleryDir = Directory('${directory.path}/gallery');

      if (!await galleryDir.exists()) {
        _logger.d('📭 مجلد المعرض غير موجود');
        return;
      }

      final files = galleryDir.listSync();
      if (files.isEmpty) {
        _logger.d('📭 لا توجد ملفات في المعرض المحلي');
        return;
      }

      _logger.i('📁 فحص ${files.length} ملف في المعرض المحلي...');

      int migratedCount = 0;

      for (final file in files) {
        if (file is! File) continue;

        final fileName = file.path.split('/').last;
        final fileExtension = fileName.split('.').last.toLowerCase();

        // تحديد نوع الملف
        String fileType;
        if (['jpg', 'jpeg', 'png'].contains(fileExtension)) {
          fileType = 'photo';
        } else if (['mp4', 'mov', 'avi'].contains(fileExtension)) {
          fileType = 'video';
        } else {
          _logger.d('⚠️ نوع ملف غير مدعوم: $fileName');
          continue;
        }

        // فحص إذا كان الملف موجود بالفعل في النظام الجديد
        final existsInQueue = await _checkIfFileExistsInQueue(fileName);
        if (existsInQueue) {
          _logger.d('⚠️ الملف موجود بالفعل في قائمة الرفع: $fileName');
          continue;
        }

        // فحص إذا كان الملف مرفوع بالفعل في قاعدة البيانات
        final existsInDB = await _checkIfFileExistsInDB(fileName, fileType);
        if (existsInDB) {
          _logger.d('⚠️ الملف مرفوع بالفعل في قاعدة البيانات: $fileName');
          // حذف الملف المحلي
          await file.delete();
          continue;
        }

        // إضافة إلى النظام الجديد
        final queueId = await _uploadService.addToUploadQueue(
          filePath: file.path,
          fileName: fileName,
          fileType: fileType,
          location: 'unknown', // لا نعرف الموقع للملفات القديمة
          metadata: {
            'migrated_from': 'local_gallery',
            'file_size': await file.length(),
            'modified_at': (await file.stat()).modified.toIso8601String(),
          },
        );

        if (queueId != null) {
          migratedCount++;
          _logger.d('✅ تم ترحيل الملف: $fileName');
        }
      }

      if (migratedCount > 0) {
        _logger.i('✅ تم ترحيل $migratedCount ملف من المعرض المحلي');
      }

    } catch (e) {
      _logger.e('❌ خطأ في ترحيل الملفات من المعرض: $e');
    }
  }

  /// فحص إذا كان الملف موجود في قائمة الرفع
  Future<bool> _checkIfFileExistsInQueue(String fileName) async {
    try {
      final result = await _supabase
          .from('upload_queue')
          .select('id')
          .eq('file_name', fileName)
          .maybeSingle();
      
      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// فحص إذا كان الملف موجود في قاعدة البيانات
  Future<bool> _checkIfFileExistsInDB(String fileName, String fileType) async {
    try {
      final tableName = fileType == 'photo' ? 'photos' : 'videos';
      final result = await _supabase
          .from(tableName)
          .select('id')
          .eq('file_name', fileName)
          .maybeSingle();
      
      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// فحص إذا كانت الترحيل مطلوب
  Future<bool> isMigrationNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // فحص وجود ملفات في SharedPreferences
      final pendingPhotos = prefs.getStringList('pending_uploads') ?? [];
      final pendingVideos = prefs.getStringList('pending_video_uploads') ?? [];
      
      if (pendingPhotos.isNotEmpty || pendingVideos.isNotEmpty) {
        return true;
      }

      // فحص وجود ملفات في المعرض المحلي
      final directory = await getApplicationDocumentsDirectory();
      final galleryDir = Directory('${directory.path}/gallery');
      
      if (await galleryDir.exists()) {
        final files = galleryDir.listSync();
        return files.isNotEmpty;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// تشغيل الترحيل إذا كان مطلوب
  Future<void> runMigrationIfNeeded() async {
    try {
      final isNeeded = await isMigrationNeeded();
      if (isNeeded) {
        _logger.i('🔄 ترحيل الملفات القديمة مطلوب...');
        await migrateOldFiles();
      } else {
        _logger.d('✅ لا يوجد ملفات تحتاج ترحيل');
      }
    } catch (e) {
      _logger.e('❌ خطأ في فحص الحاجة للترحيل: $e');
    }
  }
}
