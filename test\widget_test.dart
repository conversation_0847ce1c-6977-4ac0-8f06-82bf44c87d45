import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() {
  group('App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // بناء التطبيق الأساسي
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: Text('Test App'),
              ),
            ),
          ),
        ),
      );

      // التحقق من وجود النص
      expect(find.text('Test App'), findsOneWidget);
    });

    testWidgets('MaterialApp should be created', (WidgetTester tester) async {
      // بناء widget بسيط
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Test')),
            body: const Center(child: Text('Hello World')),
          ),
        ),
      );

      // التحقق من وجود العناصر
      expect(find.text('Test'), findsOneWidget);
      expect(find.text('Hello World'), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });
  });
}
