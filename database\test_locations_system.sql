-- 🧪 اختبار نظام المواقع المكتمل
-- Test Complete Locations System
-- Date: 2025-01-16

-- ===== 📊 اختبار 1: التحقق من عدد المواقع =====
SELECT 
    '🔍 اختبار عدد المواقع' as test_name,
    CASE 
        WHEN COUNT(*) = 70 THEN '✅ نجح - 70 موقع'
        ELSE '❌ فشل - ' || COUNT(*) || ' موقع'
    END as result
FROM locations;

-- ===== 📊 اختبار 2: التحقق من مواقع U =====
SELECT 
    '🔍 اختبار مواقع U' as test_name,
    CASE 
        WHEN COUNT(*) = 25 THEN '✅ نجح - 25 موقع U'
        ELSE '❌ فشل - ' || COUNT(*) || ' موقع U'
    END as result
FROM locations WHERE location_type = 'U';

-- ===== 📊 اختبار 3: التحقق من مواقع C =====
SELECT 
    '🔍 اختبار مواقع C' as test_name,
    CASE 
        WHEN COUNT(*) = 45 THEN '✅ نجح - 45 موقع C'
        ELSE '❌ فشل - ' || COUNT(*) || ' موقع C'
    END as result
FROM locations WHERE location_type = 'C';

-- ===== 📊 اختبار 4: التحقق من ترقيم مواقع U =====
SELECT 
    '🔍 اختبار ترقيم U' as test_name,
    CASE 
        WHEN MIN(sort_order) = 1 AND MAX(sort_order) = 25 THEN '✅ نجح - U101 إلى U125'
        ELSE '❌ فشل - من ' || MIN(sort_order) || ' إلى ' || MAX(sort_order)
    END as result
FROM locations WHERE location_type = 'U';

-- ===== 📊 اختبار 5: التحقق من ترقيم مواقع C =====
SELECT 
    '🔍 اختبار ترقيم C' as test_name,
    CASE 
        WHEN MIN(sort_order) = 101 AND MAX(sort_order) = 145 THEN '✅ نجح - C101 إلى C145'
        ELSE '❌ فشل - من ' || MIN(sort_order) || ' إلى ' || MAX(sort_order)
    END as result
FROM locations WHERE location_type = 'C';

-- ===== 📊 اختبار 6: التحقق من وجود الدوال =====
SELECT 
    '🔍 اختبار الدوال' as test_name,
    CASE 
        WHEN COUNT(*) >= 8 THEN '✅ نجح - ' || COUNT(*) || ' دالة موجودة'
        ELSE '❌ فشل - ' || COUNT(*) || ' دالة فقط'
    END as result
FROM pg_proc 
WHERE proname IN (
    'update_location_statistics',
    'update_all_locations_statistics', 
    'get_locations_statistics_detailed',
    'analyze_locations_usage',
    'get_top_locations',
    'get_locations_summary_stats',
    'analyze_daily_location_usage',
    'search_locations'
);

-- ===== 📊 اختبار 7: التحقق من الـ Triggers =====
SELECT 
    '🔍 اختبار Triggers' as test_name,
    CASE 
        WHEN COUNT(*) >= 2 THEN '✅ نجح - ' || COUNT(*) || ' trigger موجود'
        ELSE '❌ فشل - ' || COUNT(*) || ' trigger فقط'
    END as result
FROM pg_trigger 
WHERE tgname IN (
    'trigger_photos_location_stats',
    'trigger_videos_location_stats'
);

-- ===== 📊 اختبار 8: اختبار دالة الإحصائيات =====
SELECT 
    '🔍 اختبار دالة الإحصائيات' as test_name,
    CASE 
        WHEN result LIKE '%تم تحديث إحصائيات%' THEN '✅ نجح - ' || result
        ELSE '❌ فشل - ' || result
    END as result
FROM (
    SELECT update_all_locations_statistics() as result
) test;

-- ===== 📊 اختبار 9: اختبار دالة التحليل =====
SELECT 
    '🔍 اختبار دالة التحليل' as test_name,
    CASE 
        WHEN COUNT(*) = 70 THEN '✅ نجح - تحليل 70 موقع'
        ELSE '❌ فشل - تحليل ' || COUNT(*) || ' موقع فقط'
    END as result
FROM analyze_locations_usage(30);

-- ===== 📊 اختبار 10: اختبار دالة أكثر المواقع استخداماً =====
SELECT 
    '🔍 اختبار أكثر المواقع استخداماً' as test_name,
    CASE 
        WHEN COUNT(*) <= 10 THEN '✅ نجح - ' || COUNT(*) || ' موقع في النتائج'
        ELSE '❌ فشل - ' || COUNT(*) || ' موقع (أكثر من 10)'
    END as result
FROM get_top_locations(10);

-- ===== 📊 اختبار 11: اختبار البحث =====
SELECT 
    '🔍 اختبار البحث' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ نجح - ' || COUNT(*) || ' نتيجة بحث'
        ELSE '❌ فشل - لا توجد نتائج'
    END as result
FROM search_locations('101');

-- ===== 📊 اختبار 12: اختبار الإحصائيات الشاملة =====
SELECT 
    '🔍 اختبار الإحصائيات الشاملة' as test_name,
    CASE 
        WHEN COUNT(*) > 10 THEN '✅ نجح - ' || COUNT(*) || ' إحصائية'
        ELSE '❌ فشل - ' || COUNT(*) || ' إحصائية فقط'
    END as result
FROM get_locations_summary_stats();

-- ===== 📊 ملخص النتائج =====
SELECT 
    '📋 ملخص الاختبارات' as summary,
    'جميع الاختبارات مكتملة' as status,
    NOW() as test_time;

-- ===== 📊 عرض عينة من البيانات =====
SELECT 
    '📍 عينة من مواقع U' as section,
    location_code,
    location_name_ar,
    sort_order,
    is_active
FROM locations 
WHERE location_type = 'U' 
ORDER BY sort_order 
LIMIT 5;

SELECT 
    '📍 عينة من مواقع C' as section,
    location_code,
    location_name_ar,
    sort_order,
    is_active
FROM locations 
WHERE location_type = 'C' 
ORDER BY sort_order 
LIMIT 5;

-- ===== 📊 إحصائيات سريعة =====
SELECT 
    location_type,
    COUNT(*) as total_locations,
    COUNT(CASE WHEN is_active THEN 1 END) as active_locations,
    COUNT(CASE WHEN is_available THEN 1 END) as available_locations,
    SUM(total_photos) as total_photos,
    SUM(total_videos) as total_videos,
    COUNT(CASE WHEN last_used_at IS NOT NULL THEN 1 END) as used_locations
FROM locations
GROUP BY location_type
ORDER BY location_type;

-- ===== 🎯 اختبار الأداء =====
-- قياس وقت تنفيذ الاستعلامات المهمة
\timing on

-- اختبار سرعة استعلام المواقع
SELECT COUNT(*) FROM locations WHERE is_active = true;

-- اختبار سرعة دالة التحليل
SELECT COUNT(*) FROM analyze_locations_usage(7);

-- اختبار سرعة البحث
SELECT COUNT(*) FROM search_locations();

\timing off

-- ===== ✅ رسالة النجاح =====
SELECT 
    '🎉 اكتمل اختبار نظام المواقع!' as message,
    '70 موقع جاهز للاستخدام' as locations_status,
    'جميع الدوال والـ Triggers تعمل بنجاح' as functions_status,
    NOW() as completion_time;
