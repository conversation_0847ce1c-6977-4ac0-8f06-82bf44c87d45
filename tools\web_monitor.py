#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب لمراقبة نظام ذاكرة القمر
Moon Memory Web Monitoring Interface

واجهة ويب بسيطة لمراقبة النظام في الوقت الفعلي
مع لوحة معلومات تفاعلية ومخططات بيانية

المطور: فريق ذاكرة القمر
التاريخ: يناير 2025
"""

import os
import sys
import json
import threading
from datetime import datetime
from flask import Flask, render_template, jsonify, request, send_from_directory
from system_monitor import SystemMonitor

# إنشاء تطبيق Flask
app = Flask(__name__)
app.secret_key = 'moon_memory_monitor_secret_key'

# مراقب النظام العام
monitor = None
monitor_thread = None

@app.route('/')
def dashboard():
    """الصفحة الرئيسية - لوحة المعلومات"""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """API للحصول على حالة النظام الحالية"""
    if not monitor:
        return jsonify({'error': 'المراقب غير مفعل'}), 500
    
    status = monitor.get_current_status()
    return jsonify(status)

@app.route('/api/metrics/history')
def api_metrics_history():
    """API للحصول على تاريخ المقاييس"""
    if not monitor or not monitor.metrics_history:
        return jsonify({'error': 'لا توجد بيانات متاحة'}), 404
    
    # إرجاع آخر 50 عينة
    history = monitor.metrics_history[-50:]
    return jsonify(history)

@app.route('/api/alerts')
def api_alerts():
    """API للحصول على التنبيهات النشطة"""
    if not monitor:
        return jsonify([])
    
    status = monitor.get_current_status()
    return jsonify(status.get('alerts', []))

@app.route('/api/control/start', methods=['POST'])
def api_start_monitoring():
    """API لبدء المراقبة"""
    global monitor, monitor_thread
    
    if monitor and monitor.monitoring_active:
        return jsonify({'error': 'المراقبة نشطة بالفعل'}), 400
    
    try:
        interval = request.json.get('interval', 60) if request.json else 60
        
        monitor = SystemMonitor()
        monitor_thread = threading.Thread(
            target=monitor.start_monitoring,
            args=(interval,),
            daemon=True
        )
        monitor_thread.start()
        
        return jsonify({'message': 'تم بدء المراقبة بنجاح', 'interval': interval})
    except Exception as e:
        return jsonify({'error': f'خطأ في بدء المراقبة: {str(e)}'}), 500

@app.route('/api/control/stop', methods=['POST'])
def api_stop_monitoring():
    """API لإيقاف المراقبة"""
    global monitor
    
    if not monitor or not monitor.monitoring_active:
        return jsonify({'error': 'المراقبة غير نشطة'}), 400
    
    try:
        monitor.stop_monitoring()
        return jsonify({'message': 'تم إيقاف المراقبة بنجاح'})
    except Exception as e:
        return jsonify({'error': f'خطأ في إيقاف المراقبة: {str(e)}'}), 500

@app.route('/static/<path:filename>')
def static_files(filename):
    """خدمة الملفات الثابتة"""
    return send_from_directory('static', filename)

def create_dashboard_template():
    """إنشاء قالب لوحة المعلومات"""
    template_dir = 'templates'
    os.makedirs(template_dir, exist_ok=True)
    
    dashboard_html = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب ذاكرة القمر</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-bar {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .status-dot.warning { background: #ffc107; }
        .status-dot.critical { background: #dc3545; }
        
        .controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .metric-card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .alerts-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .alert.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert.critical {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        #loading {
            text-align: center;
            color: white;
            font-size: 1.2em;
            margin: 50px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌙 مراقب ذاكرة القمر</h1>
            <p>مراقبة النظام في الوقت الفعلي</p>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">جاري التحميل...</span>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startMonitoring()">بدء المراقبة</button>
                <button class="btn btn-danger" onclick="stopMonitoring()">إيقاف المراقبة</button>
            </div>
        </div>
        
        <div id="loading">جاري تحميل البيانات...</div>
        
        <div id="dashboard" style="display: none;">
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>💻 المعالج</h3>
                    <div class="metric-value" id="cpuValue">-</div>
                    <div class="metric-label">نسبة الاستخدام</div>
                </div>
                
                <div class="metric-card">
                    <h3>🧠 الذاكرة</h3>
                    <div class="metric-value" id="memoryValue">-</div>
                    <div class="metric-label">نسبة الاستخدام</div>
                </div>
                
                <div class="metric-card">
                    <h3>💾 القرص الصلب</h3>
                    <div class="metric-value" id="diskValue">-</div>
                    <div class="metric-label">نسبة الاستخدام</div>
                </div>
                
                <div class="metric-card">
                    <h3>🗄️ قاعدة البيانات</h3>
                    <div class="metric-value" id="dbConnections">-</div>
                    <div class="metric-label">اتصالات نشطة</div>
                </div>
            </div>
            
            <div class="alerts-section">
                <h3>🚨 التنبيهات</h3>
                <div id="alertsList">لا توجد تنبيهات</div>
            </div>
            
            <div class="chart-container">
                <h3>📊 مخطط الأداء</h3>
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <script>
        let chart;
        let updateInterval;
        
        // تهيئة المخطط
        function initChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'المعالج %',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        tension: 0.1
                    }, {
                        label: 'الذاكرة %',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        // تحديث البيانات
        async function updateData() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('statusText').textContent = data.error;
                    return;
                }
                
                // تحديث الحالة
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');
                
                statusDot.className = 'status-dot ' + data.status;
                statusText.textContent = getStatusText(data.status);
                
                // تحديث المقاييس
                if (data.metrics && data.metrics.system) {
                    document.getElementById('cpuValue').textContent = 
                        data.metrics.system.cpu_percent.toFixed(1) + '%';
                    document.getElementById('memoryValue').textContent = 
                        data.metrics.system.memory_percent.toFixed(1) + '%';
                    document.getElementById('diskValue').textContent = 
                        data.metrics.system.disk_percent.toFixed(1) + '%';
                }
                
                if (data.metrics && data.metrics.database) {
                    document.getElementById('dbConnections').textContent = 
                        data.metrics.database.active_connections || '-';
                }
                
                // تحديث التنبيهات
                updateAlerts(data.alerts || []);
                
                // تحديث المخطط
                updateChart(data.metrics);
                
                // إظهار لوحة المعلومات
                document.getElementById('loading').style.display = 'none';
                document.getElementById('dashboard').style.display = 'block';
                
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
                document.getElementById('statusText').textContent = 'خطأ في الاتصال';
            }
        }
        
        function getStatusText(status) {
            switch(status) {
                case 'ok': return 'النظام يعمل بشكل طبيعي';
                case 'warning': return 'تحذير - يوجد مشاكل طفيفة';
                case 'critical': return 'خطر - يوجد مشاكل خطيرة';
                default: return 'حالة غير معروفة';
            }
        }
        
        function updateAlerts(alerts) {
            const alertsList = document.getElementById('alertsList');
            
            if (alerts.length === 0) {
                alertsList.innerHTML = '<p>لا توجد تنبيهات</p>';
                return;
            }
            
            alertsList.innerHTML = alerts.map(alert => 
                `<div class="alert ${alert.severity}">
                    <strong>${alert.type}:</strong> ${alert.message}
                </div>`
            ).join('');
        }
        
        function updateChart(metrics) {
            if (!chart || !metrics || !metrics.system) return;
            
            const now = new Date().toLocaleTimeString();
            
            // إضافة نقطة جديدة
            chart.data.labels.push(now);
            chart.data.datasets[0].data.push(metrics.system.cpu_percent);
            chart.data.datasets[1].data.push(metrics.system.memory_percent);
            
            // الاحتفاظ بآخر 20 نقطة فقط
            if (chart.data.labels.length > 20) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
                chart.data.datasets[1].data.shift();
            }
            
            chart.update();
        }
        
        async function startMonitoring() {
            try {
                const response = await fetch('/api/control/start', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({interval: 30})
                });
                const result = await response.json();
                alert(result.message || result.error);
            } catch (error) {
                alert('خطأ في بدء المراقبة');
            }
        }
        
        async function stopMonitoring() {
            try {
                const response = await fetch('/api/control/stop', {
                    method: 'POST'
                });
                const result = await response.json();
                alert(result.message || result.error);
            } catch (error) {
                alert('خطأ في إيقاف المراقبة');
            }
        }
        
        // بدء التطبيق
        window.onload = function() {
            initChart();
            updateData();
            updateInterval = setInterval(updateData, 5000); // تحديث كل 5 ثوان
        };
    </script>
</body>
</html>'''
    
    with open(os.path.join(template_dir, 'dashboard.html'), 'w', encoding='utf-8') as f:
        f.write(dashboard_html)

def main():
    """تشغيل خادم الويب"""
    import argparse
    
    parser = argparse.ArgumentParser(description='واجهة ويب لمراقبة ذاكرة القمر')
    parser.add_argument('--host', default='127.0.0.1', help='عنوان الخادم')
    parser.add_argument('--port', type=int, default=5000, help='منفذ الخادم')
    parser.add_argument('--debug', action='store_true', help='وضع التطوير')
    
    args = parser.parse_args()
    
    # إنشاء قالب لوحة المعلومات
    create_dashboard_template()
    
    print(f"🌙 بدء خادم مراقبة ذاكرة القمر على http://{args.host}:{args.port}")
    
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == '__main__':
    main()
