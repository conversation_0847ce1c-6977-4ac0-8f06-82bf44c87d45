-- 🔧 إصلاح صلاحيات Storage والجداول
-- Fix Storage and Table Permissions
-- Date: 2025-01-15

-- ===== 🗂️ إنشاء سياسات Storage =====

-- سياسات للصور
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'photos_policy_select',
    'photos',
    'Allow users to view their own photos',
    '(auth.uid() IS NOT NULL)',
    NULL,
    'SELECT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'photos_policy_insert',
    'photos',
    'Allow users to upload photos',
    '(auth.uid() IS NOT NULL)',
    '(auth.uid() IS NOT NULL)',
    'INSERT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition,
    check_definition = EXCLUDED.check_definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'photos_policy_update',
    'photos',
    'Allow users to update their own photos',
    '(auth.uid() IS NOT NULL)',
    '(auth.uid() IS NOT NULL)',
    'UPDATE'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition,
    check_definition = EXCLUDED.check_definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'photos_policy_delete',
    'photos',
    'Allow users to delete their own photos',
    '(auth.uid() IS NOT NULL)',
    NULL,
    'DELETE'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition;

-- سياسات للفيديو
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'videos_policy_select',
    'videos',
    'Allow users to view their own videos',
    '(auth.uid() IS NOT NULL)',
    NULL,
    'SELECT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'videos_policy_insert',
    'videos',
    'Allow users to upload videos',
    '(auth.uid() IS NOT NULL)',
    '(auth.uid() IS NOT NULL)',
    'INSERT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition,
    check_definition = EXCLUDED.check_definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'videos_policy_update',
    'videos',
    'Allow users to update their own videos',
    '(auth.uid() IS NOT NULL)',
    '(auth.uid() IS NOT NULL)',
    'UPDATE'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition,
    check_definition = EXCLUDED.check_definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'videos_policy_delete',
    'videos',
    'Allow users to delete their own videos',
    '(auth.uid() IS NOT NULL)',
    NULL,
    'DELETE'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition;

-- سياسات للصور الشخصية
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'avatars_policy_select',
    'avatars',
    'Allow users to view avatars',
    '(auth.uid() IS NOT NULL)',
    NULL,
    'SELECT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition;

INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command)
VALUES (
    'avatars_policy_insert',
    'avatars',
    'Allow users to upload avatars',
    '(auth.uid() IS NOT NULL)',
    '(auth.uid() IS NOT NULL)',
    'INSERT'
) ON CONFLICT (id) DO UPDATE SET
    definition = EXCLUDED.definition,
    check_definition = EXCLUDED.check_definition;

-- ===== 🔓 إضافة صلاحيات إضافية للجداول =====

-- منح صلاحيات للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- منح صلاحيات للمستخدمين المجهولين (للقراءة فقط)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- ===== 🔄 تحديث السياسات لتكون أكثر مرونة =====

-- تحديث سياسة الصور لتسمح بالوصول الكامل للمستخدمين المصادق عليهم
DROP POLICY IF EXISTS photos_simple_policy ON public.photos;
CREATE POLICY photos_open_policy ON public.photos
    FOR ALL
    USING (auth.uid() IS NOT NULL);

-- تحديث سياسة الفيديو
DROP POLICY IF EXISTS videos_simple_policy ON public.videos;
CREATE POLICY videos_open_policy ON public.videos
    FOR ALL
    USING (auth.uid() IS NOT NULL);

-- تحديث سياسة الأجهزة
DROP POLICY IF EXISTS devices_simple_policy ON public.devices;
CREATE POLICY devices_open_policy ON public.devices
    FOR ALL
    USING (auth.uid() IS NOT NULL);

-- تحديث سياسة الجلسات
DROP POLICY IF EXISTS sessions_simple_policy ON public.user_sessions;
CREATE POLICY sessions_open_policy ON public.user_sessions
    FOR ALL
    USING (auth.uid() IS NOT NULL);

-- ===== ✅ اختبار الصلاحيات =====

-- اختبار الوصول للجداول
SELECT 'اختبار الوصول للجداول:' as test_info;

-- عد الصور (يجب أن يعمل بدون خطأ)
SELECT COUNT(*) as photos_count FROM public.photos;

-- عد الفيديو (يجب أن يعمل بدون خطأ)
SELECT COUNT(*) as videos_count FROM public.videos;

-- عد الأجهزة (يجب أن يعمل بدون خطأ)
SELECT COUNT(*) as devices_count FROM public.devices;

-- عرض سياسات Storage
SELECT bucket_id, name, definition, command
FROM storage.policies
ORDER BY bucket_id, command;

SELECT 'تم إصلاح جميع الصلاحيات! ✅' as status;
SELECT 'النظام جاهز للاختبار النهائي! 🚀' as final_message;
