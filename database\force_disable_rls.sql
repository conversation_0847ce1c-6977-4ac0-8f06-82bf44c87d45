-- 🔥 إصلاح قوي - إلغاء تفعيل RLS نهائياً
-- Force Disable RLS - Ultimate Fix
-- Date: 2025-01-19
-- انسخ والصق هذا الكود في Supabase SQL Editor

-- ===== 🚨 إلغاء تفعيل RLS نهائياً على جميع الجداول =====

-- إلغاء تفعيل RLS
ALTER TABLE IF EXISTS public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.locations DISABLE ROW LEVEL SECURITY;

-- ===== 🗑️ حذف جميع السياسات بالقوة =====

-- حذف جميع السياسات من جدول users
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'users' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.users';
    END LOOP;
END $$;

-- حذف جميع السياسات من جدول photos
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'photos' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.photos';
    END LOOP;
END $$;

-- حذف جميع السياسات من جدول videos
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'videos' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.videos';
    END LOOP;
END $$;

-- حذف جميع السياسات من جدول devices
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'devices' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON public.devices';
    END LOOP;
END $$;

-- حذف جميع السياسات من storage.objects
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || ' ON storage.objects';
    END LOOP;
END $$;

-- ===== 🔓 منح صلاحيات كاملة لجميع الأدوار =====

-- منح صلاحيات للمستخدمين المصادق عليهم
GRANT ALL PRIVILEGES ON public.photos TO authenticated;
GRANT ALL PRIVILEGES ON public.videos TO authenticated;
GRANT ALL PRIVILEGES ON public.users TO authenticated;
GRANT ALL PRIVILEGES ON public.devices TO authenticated;
GRANT ALL PRIVILEGES ON public.locations TO authenticated;

-- منح صلاحيات للمستخدمين المجهولين
GRANT ALL PRIVILEGES ON public.photos TO anon;
GRANT ALL PRIVILEGES ON public.videos TO anon;
GRANT ALL PRIVILEGES ON public.users TO anon;
GRANT ALL PRIVILEGES ON public.devices TO anon;
GRANT ALL PRIVILEGES ON public.locations TO anon;

-- منح صلاحيات للخدمة
GRANT ALL PRIVILEGES ON public.photos TO service_role;
GRANT ALL PRIVILEGES ON public.videos TO service_role;
GRANT ALL PRIVILEGES ON public.users TO service_role;
GRANT ALL PRIVILEGES ON public.devices TO service_role;
GRANT ALL PRIVILEGES ON public.locations TO service_role;

-- ===== 📊 فحص النتائج =====
SELECT 
    'RLS تم إلغاء تفعيله نهائياً! 🔥' as status,
    'جميع السياسات تم حذفها' as policies_status,
    'صلاحيات كاملة تم منحها' as permissions_status,
    'التطبيق جاهز للعمل الآن' as result;

-- عرض حالة RLS على الجداول
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('photos', 'videos', 'users', 'devices', 'locations')
ORDER BY tablename;
