@echo off
echo ===== استيراد النسخة الاحتياطية =====
echo.

if not exist moon_memory_backup.sql (
    echo ❌ ملف النسخة الاحتياطية غير موجود!
    echo تأكد من وجود الملف: moon_memory_backup.sql
    pause
    exit /b 1
)

echo جاري استيراد النسخة الاحتياطية إلى قاعدة البيانات المحلية...
echo هذا قد يستغرق بضع دقائق...

psql -U postgres -d moon_memory_local -f moon_memory_backup.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم استيراد النسخة الاحتياطية بنجاح!
    echo.
    echo جاري فحص الجداول المستوردة...
    psql -U postgres -d moon_memory_local -c "\dt"
) else (
    echo ❌ فشل في استيراد النسخة الاحتياطية
    echo تحقق من:
    echo 1. صحة ملف النسخة الاحتياطية
    echo 2. وجود قاعدة البيانات moon_memory_local
    echo 3. صلاحيات المستخدم postgres
)

echo.
pause
