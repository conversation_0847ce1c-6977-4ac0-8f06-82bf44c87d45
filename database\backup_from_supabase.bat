@echo off
echo ===== جلب نسخة احتياطية من Supabase =====
echo.

set SUPABASE_URL=xufiuvdtfusbaerwrkzb.supabase.co
set SUPABASE_DB=postgres
set SUPABASE_USER=postgres.xufiuvdtfusbaerwrkzb
set /p SUPABASE_PASSWORD=ادخل كلمة مرور Supabase: 

echo.
echo جاري جلب النسخة الاحتياطية...
echo هذا قد يستغرق بضع دقائق...

pg_dump "postgresql://%SUPABASE_USER%:%SUPABASE_PASSWORD%@%SUPABASE_URL%:5432/%SUPABASE_DB%" > moon_memory_backup.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم جلب النسخة الاحتياطية بنجاح!
    echo الملف: moon_memory_backup.sql
    echo الحجم:
    dir moon_memory_backup.sql | find "moon_memory_backup.sql"
) else (
    echo ❌ فشل في جلب النسخة الاحتياطية
    echo تحقق من:
    echo 1. اتصال الإنترنت
    echo 2. صحة كلمة المرور
    echo 3. صحة عنوان Supabase
)

echo.
pause
