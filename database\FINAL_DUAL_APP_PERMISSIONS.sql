-- 🎯 الحل النهائي والمضمون للتطبيقين معاً
-- Final Dual App Permissions Solution
-- Date: 2025-01-19
-- للتطبيقين: تطبيق الكاميرا + تطبيق الإدارة
-- بدون تعارض وبصلاحيات صحيحة ومضمونة

-- ===== 🧹 تنظيف شامل أولاً =====

-- إلغاء تفعيل RLS على جميع الجداول
ALTER TABLE IF EXISTS public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.admin_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.system_stats DISABLE ROW LEVEL SECURITY;

-- حذف جميع السياسات الموجودة
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    -- حذف سياسات الجداول
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || 
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
    
    -- حذف سياسات Storage
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'storage'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || 
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
END $$;

-- ===== 🔐 إعداد الصلاحيات الأساسية =====

-- 1. منح صلاحيات كاملة للمشرف (service_role)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO service_role;
GRANT ALL PRIVILEGES ON SCHEMA public TO service_role;

-- 2. منح صلاحيات Storage للمشرف
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA storage TO service_role;
GRANT ALL PRIVILEGES ON SCHEMA storage TO service_role;

-- 3. منح صلاحيات محددة للمستخدمين المصادق عليهم (تطبيق الكاميرا)
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.photos TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.videos TO authenticated;
GRANT SELECT, UPDATE ON public.users TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.devices TO authenticated;
GRANT SELECT ON public.locations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_sessions TO authenticated;

-- 4. منح صلاحيات Storage للمستخدمين المصادق عليهم
GRANT SELECT, INSERT, UPDATE, DELETE ON storage.objects TO authenticated;
GRANT SELECT ON storage.buckets TO authenticated;

-- 5. منح صلاحيات محدودة للمستخدمين المجهولين
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON public.locations TO anon;

-- ===== 🛡️ إنشاء سياسات RLS ذكية =====

-- إعادة تفعيل RLS مع سياسات ذكية
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- سياسة المستخدمين: المستخدم يرى بياناته + المشرف يرى الكل
CREATE POLICY "users_dual_app_policy" ON public.users
    FOR ALL
    USING (
        -- المشرف يرى كل شيء
        auth.jwt() ->> 'role' = 'service_role' OR
        -- المستخدم يرى بياناته فقط
        auth.uid() = id OR
        -- المشرف المسجل في النظام يرى كل شيء
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسة الصور: المستخدم يرى صوره + المشرف يرى الكل
CREATE POLICY "photos_dual_app_policy" ON public.photos
    FOR ALL
    USING (
        -- المشرف يرى كل شيء
        auth.jwt() ->> 'role' = 'service_role' OR
        -- المستخدم يرى صوره فقط
        auth.uid() = user_id OR
        -- المشرف المسجل في النظام يرى كل شيء
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسة الفيديوهات: نفس منطق الصور
CREATE POLICY "videos_dual_app_policy" ON public.videos
    FOR ALL
    USING (
        -- المشرف يرى كل شيء
        auth.jwt() ->> 'role' = 'service_role' OR
        -- المستخدم يرى فيديوهاته فقط
        auth.uid() = user_id OR
        -- المشرف المسجل في النظام يرى كل شيء
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسة الأجهزة: المستخدم يرى أجهزته + المشرف يرى الكل
CREATE POLICY "devices_dual_app_policy" ON public.devices
    FOR ALL
    USING (
        -- المشرف يرى كل شيء
        auth.jwt() ->> 'role' = 'service_role' OR
        -- المستخدم يرى أجهزته فقط
        auth.uid() = user_id OR
        -- المشرف المسجل في النظام يرى كل شيء
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسة الجلسات: المستخدم يرى جلساته + المشرف يرى الكل
CREATE POLICY "sessions_dual_app_policy" ON public.user_sessions
    FOR ALL
    USING (
        -- المشرف يرى كل شيء
        auth.jwt() ->> 'role' = 'service_role' OR
        -- المستخدم يرى جلساته فقط
        auth.uid() = user_id OR
        -- المشرف المسجل في النظام يرى كل شيء
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- سياسة المواقع: الجميع يقرأ + المشرف يعدل
CREATE POLICY "locations_dual_app_policy" ON public.locations
    FOR SELECT
    USING (true);

CREATE POLICY "locations_admin_policy" ON public.locations
    FOR INSERT, UPDATE, DELETE
    USING (
        -- المشرف فقط يعدل المواقع
        auth.jwt() ->> 'role' = 'service_role' OR
        EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND is_admin = TRUE)
    );

-- ===== 📁 سياسات Storage ذكية =====

-- سياسة Storage للصور
CREATE POLICY "storage_photos_dual_policy" ON storage.objects
    FOR ALL
    USING (
        bucket_id = 'photos' AND (
            -- المشرف يرى كل شيء
            auth.jwt() ->> 'role' = 'service_role' OR
            -- المستخدم المصادق عليه يرى ملفاته
            auth.uid() IS NOT NULL
        )
    );

-- سياسة Storage للفيديوهات
CREATE POLICY "storage_videos_dual_policy" ON storage.objects
    FOR ALL
    USING (
        bucket_id = 'videos' AND (
            -- المشرف يرى كل شيء
            auth.jwt() ->> 'role' = 'service_role' OR
            -- المستخدم المصادق عليه يرى ملفاته
            auth.uid() IS NOT NULL
        )
    );

-- ===== ✅ التحقق من النجاح =====
SELECT 
    '🎉 تم إعداد الصلاحيات للتطبيقين بنجاح!' as status,
    'تطبيق الكاميرا: صلاحيات محدودة للمستخدمين' as camera_app,
    'تطبيق الإدارة: صلاحيات كاملة للمشرف' as admin_app,
    'لا توجد تعارضات بين التطبيقين' as conflicts,
    'النظام جاهز للاستخدام' as result;

-- عرض ملخص السياسات المطبقة
SELECT 
    schemaname,
    tablename,
    COUNT(*) as policies_count,
    rowsecurity as rls_enabled
FROM pg_policies p
JOIN pg_tables t ON p.tablename = t.tablename AND p.schemaname = t.schemaname
WHERE p.schemaname IN ('public', 'storage')
GROUP BY schemaname, tablename, rowsecurity
ORDER BY schemaname, tablename;
