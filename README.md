# 🌙 كاميرا ذاكرة القمر | Moon Memory Camera

تطبيق كاميرا متقدم لتوثيق الإنجازات مع العلامات المائية والموقع والوقت.

## 📱 نظرة عامة

كاميرا ذاكرة القمر هو تطبيق Flutter متقدم مصمم لتوثيق الإنجازات والأحداث المهمة. يضيف التطبيق تلقائياً العلامات المائية والموقع والوقت على الصور، مما يجعلها مثالية للتوثيق الرسمي.

## ✨ الميزات الرئيسية

### 🔐 المصادقة والأمان
- نظام تسجيل دخول آمن مع Supabase
- إدارة الأجهزة المصرح لها
- تشفير البيانات الحساسة
- حماية متغيرات البيئة

### 📸 الكاميرا المتقدمة
- التقاط صور عالية الجودة
- وضع ليلي ونهاري محسن
- تحكم في التعريض والتكبير
- التركيز التلقائي المحسن

### 🎨 معالجة الصور
- إضافة العلامات المائية تلقائياً
- عرض الموقع والوقت بالعربية
- ضغط الصور الذكي
- معالجة متوازية للأداء الأمثل

### 🌍 الموقع والوقت
- تحديد الموقع الدقيق
- عرض العنوان بالعربية
- تحديث الموقع التلقائي
- تنسيق الوقت والتاريخ العربي

### 🌐 دعم متعدد اللغات
- العربية (افتراضي)
- الإنجليزية
- تبديل سهل بين اللغات
- واجهة RTL للعربية

### 💾 التخزين والمشاركة
- رفع آمن إلى Supabase Storage
- نظام تخزين مؤقت ذكي
- مشاركة سهلة للصور
- إدارة الملفات المحسنة

## 🏗️ البنية المعمارية

التطبيق يتبع مبادئ Clean Architecture:

```
lib/
├── core/                 # الوظائف الأساسية
│   ├── config/          # الإعدادات
│   ├── errors/          # معالجة الأخطاء
│   ├── routing/         # التنقل
│   ├── services/        # الخدمات
│   ├── theme/           # التصميم
│   └── utils/           # الأدوات المساعدة
├── features/            # الميزات
│   ├── auth/           # المصادقة
│   ├── camera/         # الكاميرا
│   ├── home/           # الرئيسية
│   └── settings/       # الإعدادات
├── shared/             # المكونات المشتركة
│   ├── theme/          # التصميم المشترك
│   └── widgets/        # المكونات المشتركة
└── main.dart           # نقطة البداية
```

## 🚀 البدء

### المتطلبات
- Flutter 3.6.1 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio / VS Code
- حساب Supabase

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-repo/moon_memory_camera.git
cd moon_memory_camera
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتحرير .env وإضافة بيانات Supabase
```

4. **تشغيل التطبيق**
```bash
flutter run
```

## ⚙️ الإعداد

### إعداد Supabase

1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. إنشاء الجداول المطلوبة:

```sql
-- جدول المستخدمين
CREATE TABLE users (
  id UUID REFERENCES auth.users PRIMARY KEY,
  full_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الأجهزة
CREATE TABLE devices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  device_id TEXT NOT NULL,
  device_name TEXT,
  device_model TEXT,
  device_brand TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الصور
CREATE TABLE photos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  storage_path TEXT NOT NULL,
  image_url TEXT,
  location TEXT,
  date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

3. إعداد Storage buckets:
   - `photos` للصور

### متغيرات البيئة

إنشاء ملف `.env` في جذر المشروع:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
APP_NAME=كاميرا ذاكرة القمر
APP_VERSION=1.0.0
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
flutter test

# اختبارات التكامل
flutter test integration_test/

# إنشاء mocks
flutter packages pub run build_runner build
```

### أنواع الاختبارات
- **Unit Tests**: اختبار الخدمات والوظائف
- **Widget Tests**: اختبار واجهة المستخدم
- **Integration Tests**: اختبار التدفق الكامل

## 📦 البناء والنشر

### Android
```bash
# بناء APK
flutter build apk --release

# بناء App Bundle
flutter build appbundle --release
```

### iOS
```bash
# بناء للـ iOS
flutter build ios --release
```

## 🔧 التطوير

### إضافة ميزة جديدة
1. إنشاء مجلد في `features/`
2. تنظيم الملفات حسب الطبقات
3. إضافة الاختبارات
4. تحديث التوثيق

### معايير الكود
- استخدام Dart Analysis
- تطبيق Flutter Lints
- كتابة تعليقات واضحة
- اتباع نمط التسمية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## 🙏 شكر وتقدير

- فريق Flutter للإطار الرائع
- Supabase للخدمات السحابية
- المجتمع العربي للمطورين

---

صنع بـ ❤️ للمجتمع العربي
