-- 🔧 إصلاح الصلاحيات البسيط - بدون Storage policies معقدة
-- Simple Permissions Fix - Without Complex Storage Policies
-- Date: 2025-01-15

-- ===== 🔓 منح الصلاحيات الأساسية =====

-- منح صلاحيات كاملة للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- منح صلاحيات للمستخدمين المجهولين (للقراءة فقط)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- منح صلاحيات Storage للمستخدمين المصادق عليهم
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- ===== 🔄 تبسيط السياسات =====

-- إزالة جميع السياسات المعقدة
DROP POLICY IF EXISTS photos_simple_policy ON public.photos;
DROP POLICY IF EXISTS videos_simple_policy ON public.videos;
DROP POLICY IF EXISTS devices_simple_policy ON public.devices;
DROP POLICY IF EXISTS sessions_simple_policy ON public.user_sessions;

-- إنشاء سياسات مفتوحة للاختبار
CREATE POLICY photos_open_policy ON public.photos
    FOR ALL
    USING (true);

CREATE POLICY videos_open_policy ON public.videos
    FOR ALL
    USING (true);

CREATE POLICY devices_open_policy ON public.devices
    FOR ALL
    USING (true);

CREATE POLICY sessions_open_policy ON public.user_sessions
    FOR ALL
    USING (true);

-- ===== 🗂️ تبسيط Storage =====

-- جعل Storage buckets عامة للاختبار
UPDATE storage.buckets 
SET public = true 
WHERE id IN ('photos', 'videos', 'avatars');

-- ===== 🔧 إنشاء دالة اختبار الاتصال =====

CREATE OR REPLACE FUNCTION test_database_connection()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'connection_status', 'success',
        'timestamp', NOW(),
        'user_id', auth.uid(),
        'tables_accessible', json_build_object(
            'users', (SELECT COUNT(*) FROM public.users),
            'devices', (SELECT COUNT(*) FROM public.devices),
            'photos', (SELECT COUNT(*) FROM public.photos),
            'videos', (SELECT COUNT(*) FROM public.videos)
        ),
        'storage_buckets', (SELECT COUNT(*) FROM storage.buckets WHERE id IN ('photos', 'videos', 'avatars'))
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== ✅ اختبار شامل =====

-- اختبار الوصول للجداول
SELECT 'اختبار الوصول للجداول:' as test_info;

-- اختبار جدول المستخدمين
SELECT COUNT(*) as users_count FROM public.users;

-- اختبار جدول الأجهزة
SELECT COUNT(*) as devices_count FROM public.devices;

-- اختبار جدول الصور
SELECT COUNT(*) as photos_count FROM public.photos;

-- اختبار جدول الفيديو
SELECT COUNT(*) as videos_count FROM public.videos;

-- اختبار Storage buckets
SELECT id, name, public as is_public 
FROM storage.buckets 
WHERE id IN ('photos', 'videos', 'avatars');

-- اختبار دالة الاتصال
SELECT 'اختبار دالة الاتصال:' as connection_test;
SELECT test_database_connection() as connection_result;

-- ===== 📊 عرض السياسات الحالية =====
SELECT 'السياسات الحالية:' as policies_info;
SELECT schemaname, tablename, policyname, permissive, cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ===== ✅ رسائل النجاح =====
SELECT 'تم تبسيط جميع الصلاحيات! ✅' as status;
SELECT 'جميع الجداول متاحة للمستخدمين المصادق عليهم! 🔓' as access_info;
SELECT 'Storage buckets مفتوحة للاختبار! 🗂️' as storage_info;
SELECT 'النظام جاهز للاختبار النهائي! 🚀' as final_message;
