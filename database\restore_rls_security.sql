-- 🔐 إعادة تفعيل Row Level Security الآمن
-- Restore Secure Row Level Security
-- Date: 2025-01-17
-- يجب تطبيق هذا بعد الانتهاء من الاختبار

-- ===== حذف السياسات المؤقتة =====
-- Remove temporary policies

DROP POLICY IF EXISTS users_public_access ON public.users;
DROP POLICY IF EXISTS photos_public_access ON public.photos;
DROP POLICY IF EXISTS videos_public_access ON public.videos;
DROP POLICY IF EXISTS locations_public_access ON public.locations;

-- ===== إعادة إنشاء السياسات الآمنة =====
-- Recreate secure policies

-- سياسات المستخدمين - يمكن للمستخدم رؤية بياناته فقط
CREATE POLICY users_secure_policy ON public.users
    FOR ALL
    USING (auth.uid() = id);

-- سياسات الصور - يمكن للمستخدم رؤية صوره فقط
CREATE POLICY photos_secure_policy ON public.photos
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسات الفيديوهات - يمكن للمستخدم رؤية فيديوهاته فقط
CREATE POLICY videos_secure_policy ON public.videos
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسات المواقع - قراءة عامة، تعديل للمدراء فقط
CREATE POLICY locations_read_policy ON public.locations
    FOR SELECT
    USING (true); -- يمكن للجميع قراءة المواقع

CREATE POLICY locations_modify_policy ON public.locations
    FOR INSERT, UPDATE, DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND (is_admin = true OR role = 'admin')
        )
    );

-- ===== إعادة تفعيل RLS على الجداول الثانوية =====
-- Re-enable RLS on secondary tables

DO $$
BEGIN
    -- التحقق من وجود الجداول قبل تفعيل RLS
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'devices') THEN
        ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;
        
        -- سياسة الأجهزة - يمكن للمستخدم رؤية أجهزته فقط
        DROP POLICY IF EXISTS devices_secure_policy ON public.devices;
        CREATE POLICY devices_secure_policy ON public.devices
            FOR ALL
            USING (auth.uid() = user_id);
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_sessions') THEN
        ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
        
        -- سياسة الجلسات - يمكن للمستخدم رؤية جلساته فقط
        DROP POLICY IF EXISTS sessions_secure_policy ON public.user_sessions;
        CREATE POLICY sessions_secure_policy ON public.user_sessions
            FOR ALL
            USING (auth.uid() = user_id);
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_logs') THEN
        ALTER TABLE public.admin_logs ENABLE ROW LEVEL SECURITY;
        
        -- سياسة سجلات الإدارة - للمدراء فقط
        DROP POLICY IF EXISTS admin_logs_secure_policy ON public.admin_logs;
        CREATE POLICY admin_logs_secure_policy ON public.admin_logs
            FOR ALL
            USING (
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (is_admin = true OR role = 'admin')
                )
            );
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_stats') THEN
        ALTER TABLE public.system_stats ENABLE ROW LEVEL SECURITY;
        
        -- سياسة إحصائيات النظام - للمدراء فقط
        DROP POLICY IF EXISTS stats_secure_policy ON public.system_stats;
        CREATE POLICY stats_secure_policy ON public.system_stats
            FOR ALL
            USING (
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() 
                    AND (is_admin = true OR role = 'admin')
                )
            );
    END IF;
END $$;

-- ===== رسائل التأكيد =====
SELECT '🔐 تم إعادة تفعيل Row Level Security الآمن' as status;
SELECT '✅ جميع السياسات الأمنية نشطة الآن' as security_status;
SELECT '👤 المستخدمون يمكنهم رؤية بياناتهم فقط' as user_access;
SELECT '👑 المدراء يمكنهم الوصول للإعدادات والإحصائيات' as admin_access;

-- ===== فحص السياسات المطبقة =====
SELECT 'فحص السياسات المطبقة:' as check_title;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
