-- حل مشكلة الأجهزة المكررة
-- Fix Duplicate Devices Issue

-- دالة لتنظيف الأجهزة المكررة والإبقاء على الأحدث فقط
CREATE OR REPLACE FUNCTION cleanup_duplicate_devices()
RETURNS JSON AS $$
DECLARE
    cleanup_result JSON;
    deleted_count INTEGER := 0;
    user_record RECORD;
    device_record RECORD;
    latest_device_id UUID;
BEGIN
    -- إنشاء جدول مؤقت للأجهزة المكررة
    CREATE TEMP TABLE duplicate_devices AS
    SELECT 
        user_id,
        COUNT(*) as device_count,
        array_agg(id ORDER BY created_at DESC) as device_ids,
        array_agg(created_at ORDER BY created_at DESC) as creation_dates
    FROM devices
    GROUP BY user_id
    HAVING COUNT(*) > 1;

    -- معالجة كل مستخدم لديه أجهزة مكررة
    FOR user_record IN SELECT * FROM duplicate_devices LOOP
        -- الحصول على أحدث جهاز (الأول في المصفوفة)
        latest_device_id := user_record.device_ids[1];
        
        -- حذف الأجهزة القديمة (كل شيء عدا الأحدث)
        FOR i IN 2..array_length(user_record.device_ids, 1) LOOP
            DELETE FROM devices 
            WHERE id = user_record.device_ids[i];
            deleted_count := deleted_count + 1;
        END LOOP;
        
        -- تحديث الجهاز المتبقي ليكون نشط
        UPDATE devices 
        SET 
            is_active = true,
            last_verified_at = NOW(),
            updated_at = NOW()
        WHERE id = latest_device_id;
    END LOOP;

    -- إنشاء النتيجة
    SELECT json_build_object(
        'success', true,
        'message', 'تم تنظيف الأجهزة المكررة بنجاح',
        'deleted_devices_count', deleted_count,
        'affected_users', (SELECT COUNT(*) FROM duplicate_devices),
        'cleanup_timestamp', NOW()
    ) INTO cleanup_result;

    -- حذف الجدول المؤقت
    DROP TABLE duplicate_devices;

    RETURN cleanup_result;

EXCEPTION WHEN OTHERS THEN
    -- في حالة الخطأ
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في تنظيف الأجهزة المكررة: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة محسنة للتحقق من الجهاز مع منع التكرار
CREATE OR REPLACE FUNCTION verify_device_single(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    existing_device_id UUID;
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص عدد الأجهزة الحالية للمستخدم
    SELECT COUNT(*) INTO device_count
    FROM devices
    WHERE user_id = p_user_id AND is_active = true;

    -- البحث عن جهاز موجود بنفس البصمة أو Android ID
    -- أو إذا كان المستخدم لديه جهاز واحد فقط (للتعامل مع unknown fingerprints)
    SELECT id INTO existing_device_id
    FROM devices
    WHERE user_id = p_user_id
    AND (
        device_fingerprint = p_device_fingerprint
        OR android_id = p_android_id
        OR (device_count = 1 AND (p_device_fingerprint = 'unknown' OR p_android_id = 'unknown'))
    )
    AND is_active = true
    LIMIT 1;

    IF existing_device_id IS NOT NULL THEN
        -- تحديث الجهاز الموجود
        UPDATE devices
        SET 
            device_fingerprint = p_device_fingerprint,
            android_id = p_android_id,
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            last_verified_at = NOW(),
            updated_at = NOW()
        WHERE id = existing_device_id;

        SELECT json_build_object(
            'success', true,
            'action', 'updated',
            'device_id', existing_device_id,
            'message', 'تم تحديث الجهاز الموجود'
        ) INTO result;

    ELSIF device_count = 0 THEN
        -- إنشاء جهاز جديد (أول جهاز للمستخدم)
        INSERT INTO devices (
            user_id,
            device_fingerprint,
            android_id,
            device_name,
            device_model,
            device_brand,
            trust_level,
            is_active,
            last_verified_at
        ) VALUES (
            p_user_id,
            p_device_fingerprint,
            p_android_id,
            p_device_name,
            p_device_model,
            p_device_brand,
            'high',
            true,
            NOW()
        ) RETURNING id INTO existing_device_id;

        SELECT json_build_object(
            'success', true,
            'action', 'created',
            'device_id', existing_device_id,
            'message', 'تم إنشاء جهاز جديد'
        ) INTO result;

    ELSE
        -- المستخدم لديه جهاز بالفعل - رفض الجهاز الجديد
        SELECT json_build_object(
            'success', false,
            'action', 'rejected',
            'error_code', 'DEVICE_LIMIT_REACHED',
            'message', 'هذا الحساب مرتبط بجهاز آخر. لا يمكن استخدامه على هذا الجهاز.'
        ) INTO result;
    END IF;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في التحقق من الجهاز: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
