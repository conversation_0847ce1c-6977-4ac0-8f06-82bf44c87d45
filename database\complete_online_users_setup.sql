-- ===== 🔥 إعداد شامل للمستخدمين المتصلين =====
-- تاريخ الإنشاء: 2025-01-19
-- الإصدار: شامل مع إنشاء الجداول والدوال

-- ===== 📋 إنشاء جدول الجلسات =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id TEXT, -- استخدام TEXT بدلاً من UUID للمرونة
    
    -- معلومات الجلسة
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيتات
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 minutes'),
    ended_at TIMESTAMP WITH TIME ZONE,
    
    -- حالة الجلسة
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT -- 'logout', 'timeout', 'admin_terminated', etc.
);

-- ===== 📋 فهارس لتحسين الأداء =====
CREATE INDEX IF NOT EXISTS idx_user_sessions_active_lookup 
ON user_sessions (is_active, expires_at, last_activity) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active 
ON user_sessions (user_id, is_active) 
WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_user_sessions_device 
ON user_sessions (device_id, is_active) 
WHERE is_active = TRUE;

-- ===== 🧹 تنظيف الجلسات المنتهية =====
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    -- تحديث الجلسات المنتهية الصلاحية
    UPDATE user_sessions 
    SET 
        is_active = FALSE,
        ended_at = NOW(),
        end_reason = 'timeout'
    WHERE 
        is_active = TRUE 
        AND (
            expires_at < NOW() 
            OR last_activity < NOW() - INTERVAL '30 minutes'
        );
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    RETURN expired_count;
END;
$$;

-- ===== 👥 الحصول على المستخدمين المتصلين الآن =====
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
    user_id UUID,
    username TEXT,
    full_name TEXT,
    location_code TEXT,
    session_id UUID,
    device_info TEXT,
    last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL,
    ip_address INET,
    is_admin BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- تنظيف الجلسات المنتهية أولاً
    PERFORM cleanup_expired_sessions();
    
    -- إرجاع المستخدمين المتصلين
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.username,
        u.full_name,
        u.location_code,
        s.id as session_id,
        s.user_agent as device_info,
        s.last_activity,
        (NOW() - s.started_at) as session_duration,
        s.ip_address,
        COALESCE(u.is_admin, FALSE) as is_admin
    FROM user_sessions s
    JOIN users u ON s.user_id = u.id
    WHERE 
        s.is_active = TRUE
        AND s.expires_at > NOW()
        AND s.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY s.last_activity DESC;
END;
$$;

-- ===== 📊 إحصائيات المستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users_stats()
RETURNS TABLE (
    total_online INTEGER,
    admin_online INTEGER,
    regular_users_online INTEGER,
    active_sessions INTEGER,
    avg_session_duration INTERVAL
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- تنظيف الجلسات المنتهية أولاً
    PERFORM cleanup_expired_sessions();
    
    -- إرجاع الإحصائيات
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT s.user_id)::INTEGER as total_online,
        COUNT(DISTINCT CASE WHEN u.is_admin = TRUE THEN s.user_id END)::INTEGER as admin_online,
        COUNT(DISTINCT CASE WHEN COALESCE(u.is_admin, FALSE) = FALSE THEN s.user_id END)::INTEGER as regular_users_online,
        COUNT(s.id)::INTEGER as active_sessions,
        AVG(NOW() - s.started_at) as avg_session_duration
    FROM user_sessions s
    JOIN users u ON s.user_id = u.id
    WHERE 
        s.is_active = TRUE
        AND s.expires_at > NOW()
        AND s.last_activity > NOW() - INTERVAL '30 minutes';
END;
$$;

-- ===== ⏰ تحديث آخر نشاط للجلسة =====
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- التحقق من وجود الجلسة وتحديثها
    UPDATE user_sessions 
    SET 
        last_activity = NOW(),
        expires_at = NOW() + INTERVAL '30 minutes'
    WHERE 
        id = p_session_id 
        AND is_active = TRUE
        AND expires_at > NOW();
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN updated_rows > 0;
END;
$$;

-- ===== 🛑 إنهاء جلسة مستخدم =====
CREATE OR REPLACE FUNCTION end_user_session(
    p_session_id UUID,
    p_end_reason TEXT DEFAULT 'admin_terminated'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    UPDATE user_sessions 
    SET 
        is_active = FALSE,
        ended_at = NOW(),
        end_reason = p_end_reason
    WHERE 
        id = p_session_id 
        AND is_active = TRUE;
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN updated_rows > 0;
END;
$$;

-- ===== 🧹 تنظيف الجلسات القديمة =====
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE 
        is_active = FALSE 
        AND ended_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

-- ===== ✅ اختبار سريع =====
-- التحقق من إنشاء الجدول
SELECT 'جدول user_sessions تم إنشاؤه بنجاح' as table_status;

-- تنظيف الجلسات المنتهية
SELECT cleanup_expired_sessions() as expired_sessions_cleaned;

-- عرض المستخدمين المتصلين (سيكون فارغ في البداية)
SELECT 'المستخدمون المتصلون:' as info;
SELECT * FROM get_online_users();

-- عرض الإحصائيات
SELECT 'الإحصائيات:' as info;
SELECT * FROM get_online_users_stats();

-- ===== 🎉 رسالة النجاح =====
SELECT 
    '✅ تم إعداد نظام المستخدمين المتصلين بنجاح!' as status,
    'الجدول والدوال جاهزة للاستخدام' as ready,
    'قم بتشغيل التطبيق وسجل دخول لاختبار النظام' as next_step;
