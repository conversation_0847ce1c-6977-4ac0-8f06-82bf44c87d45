-- تحديث جداول الصور والفيديو لدعم نظام المواقع الجديد
-- Update Photos and Videos Tables for New Location System
-- Date: 2025-01-15

-- ===== فحص وإنشاء الجداول المطلوبة =====
-- Check and Create Required Tables

-- إنشاء جدول videos إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    file_name TEXT,
    url TEXT,
    location TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security على جدول videos
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- إن<PERSON>اء سياسة الأمان لجدول videos
DROP POLICY IF EXISTS videos_user_policy ON videos;
CREATE POLICY videos_user_policy ON videos
    FOR ALL
    USING (auth.uid() = user_id);

-- ===== تحديث جدول الصور =====
-- Update Photos Table

-- إنشاء جدول photos إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    storage_path TEXT,
    image_url TEXT,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location TEXT,
    file_name TEXT,
    url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security على جدول photos
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة الأمان لجدول photos
DROP POLICY IF EXISTS photos_user_policy ON photos;
CREATE POLICY photos_user_policy ON photos
    FOR ALL
    USING (auth.uid() = user_id);

-- إضافة الحقول الجديدة لجدول photos
ALTER TABLE photos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED,
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS sort_order INTEGER;

-- إضافة تعليقات توضيحية للحقول الجديدة
COMMENT ON COLUMN photos.location_type IS 'نوع الموقع: U أو C';
COMMENT ON COLUMN photos.location_number IS 'رقم الموقع (مثل: 101, 102, ...)';
COMMENT ON COLUMN photos.full_location_code IS 'كود الموقع الكامل (مثل: U101, C145)';
COMMENT ON COLUMN photos.username IS 'اسم المستخدم الذي التقط الصورة';
COMMENT ON COLUMN photos.capture_timestamp IS 'وقت التقاط الصورة';
COMMENT ON COLUMN photos.upload_timestamp IS 'وقت رفع الصورة';
COMMENT ON COLUMN photos.sort_order IS 'ترتيب الصورة للفرز';

-- إنشاء فهارس للبحث السريع في جدول photos
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_upload_timestamp ON photos(upload_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_sort_order ON photos(sort_order);

-- فهرس مركب للفرز المتقدم
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON photos(location_type, location_number, capture_timestamp, username);

-- ===== تحديث جدول الفيديو =====
-- Update Videos Table

-- إضافة الحقول الجديدة لجدول videos
ALTER TABLE videos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED,
ADD COLUMN IF NOT EXISTS username TEXT,
ADD COLUMN IF NOT EXISTS capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS sort_order INTEGER,
ADD COLUMN IF NOT EXISTS duration_seconds INTEGER,
ADD COLUMN IF NOT EXISTS file_size_mb DECIMAL(10,2);

-- إضافة تعليقات توضيحية للحقول الجديدة
COMMENT ON COLUMN videos.location_type IS 'نوع الموقع: U أو C';
COMMENT ON COLUMN videos.location_number IS 'رقم الموقع (مثل: 101, 102, ...)';
COMMENT ON COLUMN videos.full_location_code IS 'كود الموقع الكامل (مثل: U101, C145)';
COMMENT ON COLUMN videos.username IS 'اسم المستخدم الذي سجل الفيديو';
COMMENT ON COLUMN videos.capture_timestamp IS 'وقت تسجيل الفيديو';
COMMENT ON COLUMN videos.upload_timestamp IS 'وقت رفع الفيديو';
COMMENT ON COLUMN videos.sort_order IS 'ترتيب الفيديو للفرز';
COMMENT ON COLUMN videos.duration_seconds IS 'مدة الفيديو بالثواني';
COMMENT ON COLUMN videos.file_size_mb IS 'حجم الملف بالميجابايت';

-- إنشاء فهارس للبحث السريع في جدول videos
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_upload_timestamp ON videos(upload_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_sort_order ON videos(sort_order);
CREATE INDEX IF NOT EXISTS idx_videos_duration ON videos(duration_seconds);

-- فهرس مركب للفرز المتقدم
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON videos(location_type, location_number, capture_timestamp, username);

-- ===== دوال مساعدة =====
-- Helper Functions

-- دالة لتحديث ترقيم الفرز التلقائي للصور
CREATE OR REPLACE FUNCTION update_photo_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث ترقيم الفرز بناءً على الوقت والموقع
    NEW.sort_order := (
        SELECT COALESCE(MAX(sort_order), 0) + 1
        FROM photos 
        WHERE location_type = NEW.location_type 
        AND location_number = NEW.location_number
        AND user_id = NEW.user_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للفيديو
CREATE OR REPLACE FUNCTION update_video_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث ترقيم الفرز بناءً على الوقت والموقع
    NEW.sort_order := (
        SELECT COALESCE(MAX(sort_order), 0) + 1
        FROM videos 
        WHERE location_type = NEW.location_type 
        AND location_number = NEW.location_number
        AND user_id = NEW.user_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفزات للترقيم التلقائي
CREATE TRIGGER trigger_update_photo_sort_order
    BEFORE INSERT ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_sort_order();

CREATE TRIGGER trigger_update_video_sort_order
    BEFORE INSERT ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_sort_order();

-- ===== دالة لتحديث البيانات الموجودة =====
-- Function to Update Existing Data

-- دالة لتحديث الصور الموجودة من النظام القديم
CREATE OR REPLACE FUNCTION migrate_existing_photos()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    photo_record RECORD;
BEGIN
    -- تحديث الصور التي لا تحتوي على نوع موقع
    FOR photo_record IN 
        SELECT id, location, user_id, created_at
        FROM photos 
        WHERE location_type IS NULL
    LOOP
        -- محاولة استخراج نوع الموقع من الحقل القديم
        IF photo_record.location ~ '^[UC][0-9]+$' THEN
            UPDATE photos SET
                location_type = LEFT(photo_record.location, 1),
                location_number = SUBSTRING(photo_record.location FROM 2),
                capture_timestamp = COALESCE(photo_record.created_at, NOW()),
                upload_timestamp = COALESCE(photo_record.created_at, NOW())
            WHERE id = photo_record.id;
            
            updated_count := updated_count + 1;
        ELSE
            -- للمواقع القديمة، تعيين قيم افتراضية
            UPDATE photos SET
                location_type = 'U',
                location_number = '101',
                capture_timestamp = COALESCE(photo_record.created_at, NOW()),
                upload_timestamp = COALESCE(photo_record.created_at, NOW())
            WHERE id = photo_record.id;
            
            updated_count := updated_count + 1;
        END IF;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الفيديو الموجود من النظام القديم
CREATE OR REPLACE FUNCTION migrate_existing_videos()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    video_record RECORD;
BEGIN
    -- تحديث الفيديو التي لا تحتوي على نوع موقع
    FOR video_record IN 
        SELECT id, location, user_id, created_at
        FROM videos 
        WHERE location_type IS NULL
    LOOP
        -- محاولة استخراج نوع الموقع من الحقل القديم
        IF video_record.location ~ '^[UC][0-9]+$' THEN
            UPDATE videos SET
                location_type = LEFT(video_record.location, 1),
                location_number = SUBSTRING(video_record.location FROM 2),
                capture_timestamp = COALESCE(video_record.created_at, NOW()),
                upload_timestamp = COALESCE(video_record.created_at, NOW())
            WHERE id = video_record.id;
            
            updated_count := updated_count + 1;
        ELSE
            -- للمواقع القديمة، تعيين قيم افتراضية
            UPDATE videos SET
                location_type = 'U',
                location_number = '101',
                capture_timestamp = COALESCE(video_record.created_at, NOW()),
                upload_timestamp = COALESCE(video_record.created_at, NOW())
            WHERE id = video_record.id;
            
            updated_count := updated_count + 1;
        END IF;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- تشغيل دوال الترحيل للبيانات الموجودة
SELECT migrate_existing_photos() as photos_migrated;
SELECT migrate_existing_videos() as videos_migrated;

-- رسالة نجاح
SELECT 'تم تحديث جداول الصور والفيديو بنجاح! ✅' as migration_status;
SELECT 'تم إضافة دعم نظام المواقع الجديد (U/C)' as feature_added;
