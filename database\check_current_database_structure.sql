-- ===== فحص البنية الحالية لقاعدة البيانات =====
-- Check Current Database Structure
-- Date: 2025-01-20

-- ===== 1. فحص جميع الجداول الموجودة =====
SELECT 
    schemaname as المخطط,
    tablename as اسم_الجدول,
    tableowner as الم<PERSON><PERSON><PERSON>,
    hasindexes as يحتوي_فهارس,
    hasrules as يحتوي_قواعد,
    hastriggers as يحتوي_محفزات
FROM pg_tables 
WHERE schemaname IN ('public', 'auth', 'storage')
ORDER BY schemaname, tablename;

-- ===== 2. فحص بنية جدول users (إن وجد) =====
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null,
    column_default as القيمة_الافتراضية,
    character_maximum_length as الحد_الأقصى_للطول
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- ===== 3. فحص بنية جدول auth.users =====
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null,
    column_default as القيمة_الافتراضية
FROM information_schema.columns 
WHERE table_schema = 'auth' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- ===== 4. فحص جداول الصور والفيديوهات =====
-- فحص جدول photos
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'photos'
ORDER BY ordinal_position;

-- فحص جدول videos
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'videos'
ORDER BY ordinal_position;

-- ===== 5. فحص جدول upload_queue =====
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'upload_queue'
ORDER BY ordinal_position;

-- ===== 6. فحص جدول devices =====
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'devices'
ORDER BY ordinal_position;

-- ===== 7. فحص جدول user_sessions =====
SELECT 
    column_name as اسم_العمود,
    data_type as نوع_البيانات,
    is_nullable as يقبل_null
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_sessions'
ORDER BY ordinal_position;

-- ===== 8. فحص العلاقات والمفاتيح الخارجية =====
SELECT 
    tc.table_name as الجدول_الرئيسي,
    kcu.column_name as العمود_الرئيسي,
    ccu.table_name as الجدول_المرجعي,
    ccu.column_name as العمود_المرجعي,
    tc.constraint_name as اسم_القيد
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- ===== 9. فحص الفهارس الموجودة =====
SELECT 
    schemaname as المخطط,
    tablename as الجدول,
    indexname as اسم_الفهرس,
    indexdef as تعريف_الفهرس
FROM pg_indexes 
WHERE schemaname = 'public'
ORDER BY tablename, indexname;

-- ===== 10. فحص الدوال المخصصة =====
SELECT 
    routine_name as اسم_الدالة,
    routine_type as نوع_الدالة,
    data_type as نوع_الإرجاع
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name NOT LIKE 'pg_%'
ORDER BY routine_name;

-- ===== 11. فحص سياسات RLS =====
SELECT 
    schemaname as المخطط,
    tablename as الجدول,
    policyname as اسم_السياسة,
    permissive as مسموح,
    roles as الأدوار,
    cmd as النوع,
    qual as الشرط
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ===== 12. فحص Storage Buckets =====
SELECT 
    id as اسم_البكت,
    name as الاسم,
    public as عام,
    file_size_limit as حد_حجم_الملف,
    allowed_mime_types as الأنواع_المسموحة,
    created_at as تاريخ_الإنشاء
FROM storage.buckets
ORDER BY created_at;

-- ===== 13. عدد السجلات في كل جدول =====
SELECT 'users' as الجدول, COUNT(*) as عدد_السجلات FROM public.users
UNION ALL
SELECT 'photos' as الجدول, COUNT(*) as عدد_السجلات FROM public.photos
UNION ALL
SELECT 'videos' as الجدول, COUNT(*) as عدد_السجلات FROM public.videos
UNION ALL
SELECT 'devices' as الجدول, COUNT(*) as عدد_السجلات FROM public.devices
UNION ALL
SELECT 'user_sessions' as الجدول, COUNT(*) as عدد_السجلات FROM public.user_sessions
UNION ALL
SELECT 'upload_queue' as الجدول, COUNT(*) as عدد_السجلات FROM public.upload_queue;

-- ===== 14. فحص آخر البيانات المدخلة =====
-- آخر 5 مستخدمين
SELECT 'آخر المستخدمين:' as معلومات;
SELECT id, full_name, created_at FROM public.users ORDER BY created_at DESC LIMIT 5;

-- آخر 5 صور
SELECT 'آخر الصور:' as معلومات;
SELECT file_name, location, created_at FROM public.photos ORDER BY created_at DESC LIMIT 5;

-- آخر 5 جلسات
SELECT 'آخر الجلسات:' as معلومات;
SELECT user_id, started_at, is_active FROM public.user_sessions ORDER BY started_at DESC LIMIT 5;

SELECT '=== انتهى فحص قاعدة البيانات ===' as النتيجة;
