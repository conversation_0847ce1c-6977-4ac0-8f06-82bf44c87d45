-- اختبارات شاملة للنظام الجديد
-- Test Queries for Enhanced Device System
-- Date: 2025-01-12

-- 1. اختبار إضافة جهاز جديد
SELECT 'Test 1: Adding new device' as test_name;
SELECT verify_device_enhanced(
    'your-user-id-here'::UUID,  -- ⚠️ ضع user ID حقيقي هنا
    'fingerprint_samsung_s23_12345',
    'android_id_abc123',
    'samsung/beyond1lte/beyond1:13/TP1A.220624.014/G973FXXS9FVL1:user/release-keys',
    92.5,
    'high',
    'Samsung Galaxy S23',
    'SM-G991B',
    'Samsung'
) as result;

-- 2. اختبار تحديث نفس الجهاز
SELECT 'Test 2: Updating existing device' as test_name;
SELECT verify_device_enhanced(
    'your-user-id-here'::UUID,  -- ⚠️ نفس user ID
    'fingerprint_samsung_s23_updated',  -- بصمة محدثة
    'android_id_abc123',  -- نفس Android ID
    'samsung/beyond1lte/beyond1:14/TP1A.220624.015/G973FXXS9FVL2:user/release-keys',
    89.0,
    'medium',
    'Samsung Galaxy S23 Updated',
    'SM-G991B',
    'Samsung'
) as result;

-- 3. اختبار إضافة جهاز ثاني
SELECT 'Test 3: Adding second device' as test_name;
SELECT verify_device_enhanced(
    'your-user-id-here'::UUID,  -- ⚠️ نفس user ID
    'fingerprint_pixel_7_67890',
    'android_id_def456',
    'google/cheetah/cheetah:14/UQ1A.240205.004/11269751:user/release-keys',
    95.0,
    'high',
    'Google Pixel 7 Pro',
    'GE2AE',
    'Google'
) as result;

-- 4. اختبار الوصول للحد الأقصى (إضافة أجهزة أكثر من المسموح)
SELECT 'Test 4: Testing device limit' as test_name;
SELECT verify_device_enhanced(
    'your-user-id-here'::UUID,  -- ⚠️ نفس user ID
    'fingerprint_iphone_15_99999',
    'android_id_ghi789',
    NULL,
    80.0,
    'low',
    'iPhone 15 Pro',  -- هذا مستحيل لكن للاختبار
    'iPhone15,2',
    'Apple'
) as result;

-- 5. اختبار الحصول على قائمة الأجهزة
SELECT 'Test 5: Getting user devices' as test_name;
SELECT get_user_devices('your-user-id-here'::UUID) as result;

-- 6. فحص بنية الجدول النهائية
SELECT 'Test 6: Final table structure' as test_name;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
ORDER BY ordinal_position;

-- 7. فحص البيانات المدخلة
SELECT 'Test 7: Inserted data' as test_name;
SELECT 
    id,
    device_fingerprint,
    android_id,
    device_name,
    confidence_score,
    trust_level,
    last_verified_at,
    created_at
FROM devices 
WHERE user_id = 'your-user-id-here'::UUID
ORDER BY created_at DESC;

-- 8. اختبار حذف جهاز
-- SELECT 'Test 8: Removing device' as test_name;
-- SELECT remove_device(
--     'your-user-id-here'::UUID,
--     'device-id-to-remove'::UUID
-- ) as result;

-- 9. فحص الفهارس
SELECT 'Test 9: Checking indexes' as test_name;
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'devices'
ORDER BY indexname;

-- 10. فحص Row Level Security
SELECT 'Test 10: Checking RLS policies' as test_name;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'devices';

-- ملاحظات للاختبار:
-- ⚠️ تذكر استبدال 'your-user-id-here' بـ user ID حقيقي من جدول auth.users
-- يمكنك الحصول على user ID بهذا الاستعلام:
-- SELECT id, email FROM auth.users LIMIT 5;
