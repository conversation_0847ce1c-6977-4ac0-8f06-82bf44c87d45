import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:easy_localization/easy_localization.dart';

import '../utils/logger.dart';

final permissionsServiceProvider = Provider((ref) => PermissionsService());

class PermissionsService {
  final _logger = getLogger();

  Future<Map<String, dynamic>> requestAllPermissions() async {
    try {
      // طلب إذن الكاميرا
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        _logger.w('Camera permission denied');
        return {
          'success': false,
          'error_type': 'camera_permission',
          'message': 'permissions.camera_required'.tr()
        };
      }

      // طلب إذن الميكروفون
      final microphoneStatus = await Permission.microphone.request();
      if (!microphoneStatus.isGranted) {
        _logger.w('Microphone permission denied');
        return {
          'success': false,
          'error_type': 'microphone_permission',
          'message': 'permissions.microphone_required'.tr()
        };
      }

      // طلب إذن الموقع
      final locationStatus = await Permission.location.request();
      if (!locationStatus.isGranted) {
        _logger.w('Location permission denied');
        return {
          'success': false,
          'error_type': 'location_permission',
          'message': 'permissions.location_required'.tr()
        };
      }

      // التحقق من تفعيل خدمة الموقع
      final isLocationEnabled = await Geolocator.isLocationServiceEnabled();
      if (!isLocationEnabled) {
        _logger.w('Location service is disabled');
        return {
          'success': false,
          'error_type': 'location_service',
          'message': 'permissions.location_service_disabled'.tr()
        };
      }

      _logger.i('All permissions granted successfully');
      return {
        'success': true,
        'message': 'تم منح جميع الأذونات بنجاح'
      };
    } catch (e) {
      _logger.e('Error requesting permissions', error: e);
      return {
        'success': false,
        'error_type': 'general_error',
        'message': 'حدث خطأ أثناء طلب الأذونات'
      };
    }
  }

  Future<Map<String, dynamic>> checkAllPermissions() async {
    try {
      final cameraStatus = await Permission.camera.status;
      final microphoneStatus = await Permission.microphone.status;
      final locationStatus = await Permission.location.status;
      final isLocationEnabled = await Geolocator.isLocationServiceEnabled();

      // فحص كل إذن على حدة لتحديد المشكلة بدقة
      if (!cameraStatus.isGranted) {
        return {
          'success': false,
          'error_type': 'camera_permission',
          'message': 'permissions.camera_required'.tr()
        };
      }

      if (!microphoneStatus.isGranted) {
        return {
          'success': false,
          'error_type': 'microphone_permission',
          'message': 'permissions.microphone_required'.tr()
        };
      }

      if (!locationStatus.isGranted) {
        return {
          'success': false,
          'error_type': 'location_permission',
          'message': 'permissions.location_required'.tr()
        };
      }

      if (!isLocationEnabled) {
        return {
          'success': false,
          'error_type': 'location_service',
          'message': 'permissions.location_service_disabled'.tr()
        };
      }

      return {
        'success': true,
        'message': 'جميع الأذونات متاحة'
      };
    } catch (e) {
      _logger.e('Error checking permissions', error: e);
      return {
        'success': false,
        'error_type': 'general_error',
        'message': 'حدث خطأ أثناء فحص الأذونات'
      };
    }
  }
}
