-- ===== 🚀 الإعداد النهائي الشامل للمستخدمين المتصلين =====
-- تاريخ الإنشاء: 2025-01-19
-- الإصدار: نهائي وشامل - يتضمن كل شيء

-- ===== 📋 إنشاء/تحديث جدول المستخدمين =====
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT,
    location_code TEXT,
    is_admin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الحقول المفقودة إذا لم تكن موجودة
DO $$
BEGIN
    -- إضافة username
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') THEN
        ALTER TABLE users ADD COLUMN username TEXT;
    END IF;

    -- إضافة is_admin
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_admin') THEN
        ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
    END IF;

    -- إضافة full_name
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'full_name') THEN
        ALTER TABLE users ADD COLUMN full_name TEXT;
    END IF;

    -- إضافة location_code
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'location_code') THEN
        ALTER TABLE users ADD COLUMN location_code TEXT;
    END IF;

    -- إضافة last_login
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login') THEN
        ALTER TABLE users ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- ===== 📋 إنشاء جدول الجلسات =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id TEXT,
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 minutes'),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== 📋 فهارس لتحسين الأداء =====
CREATE INDEX IF NOT EXISTS idx_user_sessions_active_lookup 
ON user_sessions (is_active, expires_at, last_activity) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_active 
ON user_sessions (user_id, is_active) WHERE is_active = TRUE;

-- ===== 🧹 دالة تنظيف الجلسات المنتهية =====
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE expired_count INTEGER;
BEGIN
    UPDATE user_sessions SET is_active = FALSE, ended_at = NOW(), end_reason = 'timeout'
    WHERE is_active = TRUE AND (expires_at < NOW() OR last_activity < NOW() - INTERVAL '30 minutes');
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END; $$;

-- ===== 👥 دالة الحصول على المستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
    user_id UUID, username TEXT, full_name TEXT, location_code TEXT,
    session_id UUID, device_info TEXT, last_activity TIMESTAMP WITH TIME ZONE,
    session_duration INTERVAL, ip_address INET, is_admin BOOLEAN
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    RETURN QUERY
    SELECT u.id, u.username, u.full_name, u.location_code, s.id, s.user_agent,
           s.last_activity, (NOW() - s.started_at), s.ip_address, COALESCE(u.is_admin, FALSE)
    FROM user_sessions s JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE AND s.expires_at > NOW() AND s.last_activity > NOW() - INTERVAL '30 minutes'
    ORDER BY s.last_activity DESC;
END; $$;

-- ===== 📊 دالة إحصائيات المستخدمين المتصلين =====
CREATE OR REPLACE FUNCTION get_online_users_stats()
RETURNS TABLE (
    total_online INTEGER, admin_online INTEGER, regular_users_online INTEGER,
    active_sessions INTEGER, avg_session_duration INTERVAL
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    PERFORM cleanup_expired_sessions();
    RETURN QUERY
    SELECT COUNT(DISTINCT s.user_id)::INTEGER,
           COUNT(DISTINCT CASE WHEN u.is_admin = TRUE THEN s.user_id END)::INTEGER,
           COUNT(DISTINCT CASE WHEN COALESCE(u.is_admin, FALSE) = FALSE THEN s.user_id END)::INTEGER,
           COUNT(s.id)::INTEGER, AVG(NOW() - s.started_at)
    FROM user_sessions s JOIN users u ON s.user_id = u.id
    WHERE s.is_active = TRUE AND s.expires_at > NOW() AND s.last_activity > NOW() - INTERVAL '30 minutes';
END; $$;

-- ===== ⏰ دالة تحديث نشاط الجلسة =====
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id UUID)
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions SET last_activity = NOW(), expires_at = NOW() + INTERVAL '30 minutes'
    WHERE id = p_session_id AND is_active = TRUE AND expires_at > NOW();
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== 🛑 دالة إنهاء جلسة مستخدم =====
CREATE OR REPLACE FUNCTION end_user_session(p_session_id UUID, p_end_reason TEXT DEFAULT 'admin_terminated')
RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE updated_rows INTEGER;
BEGIN
    UPDATE user_sessions SET is_active = FALSE, ended_at = NOW(), end_reason = p_end_reason
    WHERE id = p_session_id AND is_active = TRUE;
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    RETURN updated_rows > 0;
END; $$;

-- ===== 🧹 دالة تنظيف الجلسات القديمة =====
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions WHERE is_active = FALSE AND ended_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END; $$;

-- ===== ✅ اختبار النظام =====
SELECT 'تنظيف الجلسات المنتهية...' as step;
SELECT cleanup_expired_sessions() as expired_cleaned;

SELECT 'المستخدمون المتصلون حالياً:' as step;
SELECT * FROM get_online_users();

SELECT 'إحصائيات المتصلين:' as step;
SELECT * FROM get_online_users_stats();

-- ===== 🎉 رسالة النجاح النهائية =====
SELECT '🎉 تم إعداد نظام المستخدمين المتصلين بنجاح!' as status;
SELECT 'الجداول والدوال جاهزة للاستخدام' as ready;
SELECT 'قم بتشغيل التطبيق وسجل دخول لاختبار النظام' as next_step;

-- ===== 📝 أمثلة سريعة للاستخدام =====
/*
-- عرض المستخدمين المتصلين
SELECT * FROM get_online_users();

-- عرض الإحصائيات
SELECT * FROM get_online_users_stats();

-- تنظيف الجلسات المنتهية
SELECT cleanup_expired_sessions();

-- إنهاء جلسة محددة
SELECT end_user_session('session-uuid-here');
*/
