-- 📍 إكمال وتحديث جدول المواقع
-- Complete Locations Table Update
-- Date: 2025-01-16
-- Total Locations: 70 (25 U + 45 C)

-- ===== 🧹 تنظيف البيانات الموجودة =====
-- حذف البيانات القديمة لإعادة إنشائها بشكل صحيح
DELETE FROM public.locations;

-- ===== 📍 إدراج مواقع U (U101-U125) - 25 موقع =====
INSERT INTO public.locations (
    location_code, 
    location_type, 
    location_number, 
    location_name_ar, 
    location_name_en, 
    sort_order, 
    category, 
    department,
    is_active,
    is_available
) VALUES
-- مواقع U من 101 إلى 125
('U101', 'U', '101', 'U101', 'U101', 1, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U102', 'U', '102', 'U102', 'U102', 2, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U103', 'U', '103', 'U103', 'U103', 3, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U104', 'U', '104', 'U104', 'U104', 4, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U105', 'U', '105', 'U105', 'U105', 5, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U106', 'U', '106', 'U106', 'U106', 6, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U107', 'U', '107', 'U107', 'U107', 7, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U108', 'U', '108', 'U108', 'U108', 8, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U109', 'U', '109', 'U109', 'U109', 9, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U110', 'U', '110', 'U110', 'U110', 10, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U111', 'U', '111', 'U111', 'U111', 11, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U112', 'U', '112', 'U112', 'U112', 12, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U113', 'U', '113', 'U113', 'U113', 13, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U114', 'U', '114', 'U114', 'U114', 14, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U115', 'U', '115', 'U115', 'U115', 15, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U116', 'U', '116', 'U116', 'U116', 16, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U117', 'U', '117', 'U117', 'U117', 17, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U118', 'U', '118', 'U118', 'U118', 18, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U119', 'U', '119', 'U119', 'U119', 19, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U120', 'U', '120', 'U120', 'U120', 20, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U121', 'U', '121', 'U121', 'U121', 21, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U122', 'U', '122', 'U122', 'U122', 22, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U123', 'U', '123', 'U123', 'U123', 23, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U124', 'U', '124', 'U124', 'U124', 24, 'U_SECTION', 'U_DEPARTMENT', true, true),
('U125', 'U', '125', 'U125', 'U125', 25, 'U_SECTION', 'U_DEPARTMENT', true, true);

-- ===== 📍 إدراج مواقع C (C101-C145) - 45 موقع =====
INSERT INTO public.locations (
    location_code, 
    location_type, 
    location_number, 
    location_name_ar, 
    location_name_en, 
    sort_order, 
    category, 
    department,
    is_active,
    is_available
) VALUES
-- مواقع C من 101 إلى 145
('C101', 'C', '101', 'C101', 'C101', 101, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C102', 'C', '102', 'C102', 'C102', 102, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C103', 'C', '103', 'C103', 'C103', 103, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C104', 'C', '104', 'C104', 'C104', 104, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C105', 'C', '105', 'C105', 'C105', 105, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C106', 'C', '106', 'C106', 'C106', 106, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C107', 'C', '107', 'C107', 'C107', 107, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C108', 'C', '108', 'C108', 'C108', 108, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C109', 'C', '109', 'C109', 'C109', 109, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C110', 'C', '110', 'C110', 'C110', 110, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C111', 'C', '111', 'C111', 'C111', 111, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C112', 'C', '112', 'C112', 'C112', 112, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C113', 'C', '113', 'C113', 'C113', 113, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C114', 'C', '114', 'C114', 'C114', 114, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C115', 'C', '115', 'C115', 'C115', 115, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C116', 'C', '116', 'C116', 'C116', 116, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C117', 'C', '117', 'C117', 'C117', 117, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C118', 'C', '118', 'C118', 'C118', 118, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C119', 'C', '119', 'C119', 'C119', 119, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C120', 'C', '120', 'C120', 'C120', 120, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C121', 'C', '121', 'C121', 'C121', 121, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C122', 'C', '122', 'C122', 'C122', 122, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C123', 'C', '123', 'C123', 'C123', 123, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C124', 'C', '124', 'C124', 'C124', 124, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C125', 'C', '125', 'C125', 'C125', 125, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C126', 'C', '126', 'C126', 'C126', 126, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C127', 'C', '127', 'C127', 'C127', 127, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C128', 'C', '128', 'C128', 'C128', 128, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C129', 'C', '129', 'C129', 'C129', 129, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C130', 'C', '130', 'C130', 'C130', 130, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C131', 'C', '131', 'C131', 'C131', 131, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C132', 'C', '132', 'C132', 'C132', 132, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C133', 'C', '133', 'C133', 'C133', 133, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C134', 'C', '134', 'C134', 'C134', 134, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C135', 'C', '135', 'C135', 'C135', 135, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C136', 'C', '136', 'C136', 'C136', 136, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C137', 'C', '137', 'C137', 'C137', 137, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C138', 'C', '138', 'C138', 'C138', 138, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C139', 'C', '139', 'C139', 'C139', 139, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C140', 'C', '140', 'C140', 'C140', 140, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C141', 'C', '141', 'C141', 'C141', 141, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C142', 'C', '142', 'C142', 'C142', 142, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C143', 'C', '143', 'C143', 'C143', 143, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C144', 'C', '144', 'C144', 'C144', 144, 'C_SECTION', 'C_DEPARTMENT', true, true),
('C145', 'C', '145', 'C145', 'C145', 145, 'C_SECTION', 'C_DEPARTMENT', true, true);

-- ===== 📊 نظام تحديث الإحصائيات التلقائي =====

-- دالة تحديث إحصائيات موقع واحد
CREATE OR REPLACE FUNCTION update_location_statistics(location_code_param TEXT)
RETURNS VOID AS $$
DECLARE
    photo_count INTEGER;
    video_count INTEGER;
    last_photo_date TIMESTAMP WITH TIME ZONE;
    last_video_date TIMESTAMP WITH TIME ZONE;
    last_used TIMESTAMP WITH TIME ZONE;
BEGIN
    -- حساب عدد الصور
    SELECT COUNT(*), MAX(capture_timestamp)
    INTO photo_count, last_photo_date
    FROM photos
    WHERE full_location_code = location_code_param
    AND status = 'active';

    -- حساب عدد الفيديوهات
    SELECT COUNT(*), MAX(capture_timestamp)
    INTO video_count, last_video_date
    FROM videos
    WHERE full_location_code = location_code_param
    AND status = 'active';

    -- تحديد آخر استخدام
    last_used := GREATEST(
        COALESCE(last_photo_date, '1970-01-01'::TIMESTAMP WITH TIME ZONE),
        COALESCE(last_video_date, '1970-01-01'::TIMESTAMP WITH TIME ZONE)
    );

    -- إذا لم يكن هناك استخدام، اجعل القيمة NULL
    IF last_used = '1970-01-01'::TIMESTAMP WITH TIME ZONE THEN
        last_used := NULL;
    END IF;

    -- تحديث الإحصائيات
    UPDATE locations
    SET
        total_photos = COALESCE(photo_count, 0),
        total_videos = COALESCE(video_count, 0),
        last_used_at = last_used,
        updated_at = NOW()
    WHERE location_code = location_code_param;

END;
$$ LANGUAGE plpgsql;

-- دالة تحديث إحصائيات جميع المواقع
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- تحديث إحصائيات كل موقع
    FOR location_rec IN
        SELECT location_code FROM locations WHERE is_active = true
    LOOP
        PERFORM update_location_statistics(location_rec.location_code);
        updated_count := updated_count + 1;
    END LOOP;

    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع بنجاح';
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات المواقع مع التفاصيل
CREATE OR REPLACE FUNCTION get_locations_statistics_detailed()
RETURNS TABLE (
    location_code TEXT,
    location_type TEXT,
    location_name TEXT,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_status TEXT,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        l.location_code,
        l.location_type,
        l.location_name_ar as location_name,
        l.total_photos,
        l.total_videos,
        (l.total_photos + l.total_videos) as total_files,
        l.last_used_at,
        CASE
            WHEN l.last_used_at IS NULL THEN 'غير مستخدم'
            WHEN l.last_used_at < NOW() - INTERVAL '30 days' THEN 'قديم'
            WHEN l.last_used_at < NOW() - INTERVAL '7 days' THEN 'متوسط'
            ELSE 'نشط'
        END as usage_status,
        l.is_active
    FROM locations l
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔄 Triggers لتحديث الإحصائيات تلقائياً =====

-- دالة trigger لتحديث إحصائيات الموقع عند إضافة/تعديل/حذف صورة
CREATE OR REPLACE FUNCTION trigger_update_location_stats_photos()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث إحصائيات الموقع القديم (في حالة التعديل أو الحذف)
    IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
        IF OLD.full_location_code IS NOT NULL THEN
            PERFORM update_location_statistics(OLD.full_location_code);
        END IF;
    END IF;

    -- تحديث إحصائيات الموقع الجديد (في حالة الإضافة أو التعديل)
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        IF NEW.full_location_code IS NOT NULL THEN
            PERFORM update_location_statistics(NEW.full_location_code);
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- دالة trigger لتحديث إحصائيات الموقع عند إضافة/تعديل/حذف فيديو
CREATE OR REPLACE FUNCTION trigger_update_location_stats_videos()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث إحصائيات الموقع القديم (في حالة التعديل أو الحذف)
    IF TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
        IF OLD.full_location_code IS NOT NULL THEN
            PERFORM update_location_statistics(OLD.full_location_code);
        END IF;
    END IF;

    -- تحديث إحصائيات الموقع الجديد (في حالة الإضافة أو التعديل)
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        IF NEW.full_location_code IS NOT NULL THEN
            PERFORM update_location_statistics(NEW.full_location_code);
        END IF;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إنشاء Triggers للصور
DROP TRIGGER IF EXISTS trigger_photos_location_stats ON photos;
CREATE TRIGGER trigger_photos_location_stats
    AFTER INSERT OR UPDATE OR DELETE ON photos
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_location_stats_photos();

-- إنشاء Triggers للفيديوهات
DROP TRIGGER IF EXISTS trigger_videos_location_stats ON videos;
CREATE TRIGGER trigger_videos_location_stats
    AFTER INSERT OR UPDATE OR DELETE ON videos
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_location_stats_videos();

-- ===== 📊 تحديث الإحصائيات الحالية =====
-- تحديث إحصائيات جميع المواقع بناءً على البيانات الموجودة
SELECT update_all_locations_statistics();

-- ===== 🔍 أوامر التحقق والاختبار =====

-- التحقق من عدد المواقع
SELECT
    'إجمالي المواقع' as description,
    COUNT(*) as count
FROM locations
UNION ALL
SELECT
    'مواقع U' as description,
    COUNT(*) as count
FROM locations WHERE location_type = 'U'
UNION ALL
SELECT
    'مواقع C' as description,
    COUNT(*) as count
FROM locations WHERE location_type = 'C';

-- عرض ملخص المواقع
SELECT
    location_type,
    COUNT(*) as total_locations,
    COUNT(CASE WHEN is_active THEN 1 END) as active_locations,
    COUNT(CASE WHEN is_available THEN 1 END) as available_locations,
    MIN(sort_order) as min_sort_order,
    MAX(sort_order) as max_sort_order
FROM locations
GROUP BY location_type
ORDER BY location_type;

-- عرض أول وآخر موقع لكل نوع
SELECT
    'أول موقع U' as description,
    location_code,
    sort_order
FROM locations
WHERE location_type = 'U'
ORDER BY sort_order
LIMIT 1
UNION ALL
SELECT
    'آخر موقع U' as description,
    location_code,
    sort_order
FROM locations
WHERE location_type = 'U'
ORDER BY sort_order DESC
LIMIT 1
UNION ALL
SELECT
    'أول موقع C' as description,
    location_code,
    sort_order
FROM locations
WHERE location_type = 'C'
ORDER BY sort_order
LIMIT 1
UNION ALL
SELECT
    'آخر موقع C' as description,
    location_code,
    sort_order
FROM locations
WHERE location_type = 'C'
ORDER BY sort_order DESC
LIMIT 1;

-- ===== 📋 تعليقات على الجداول والدوال =====

COMMENT ON TABLE locations IS 'جدول المواقع - يحتوي على 70 موقع (25 U + 45 C)';
COMMENT ON FUNCTION update_location_statistics(TEXT) IS 'تحديث إحصائيات موقع واحد';
COMMENT ON FUNCTION update_all_locations_statistics() IS 'تحديث إحصائيات جميع المواقع';
COMMENT ON FUNCTION get_locations_statistics_detailed() IS 'الحصول على إحصائيات مفصلة لجميع المواقع';

-- ===== ✅ رسالة النجاح =====
SELECT
    '🎉 تم إكمال تحديث جدول المواقع بنجاح!' as message,
    '70 موقع (25 U + 45 C)' as total_locations,
    'نظام الإحصائيات التلقائي مفعل' as statistics_system,
    NOW() as completed_at;
