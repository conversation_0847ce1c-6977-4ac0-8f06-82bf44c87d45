import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

/// خدمة تشفير البيانات الحساسة
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  final _logger = getLogger();
  late final Encrypter _encrypter;
  late final IV _iv;
  bool _isInitialized = false;

  /// تهيئة خدمة التشفير
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // إنشاء مفتاح تشفير فريد للجهاز
      final deviceKey = await _getOrCreateDeviceKey();
      final key = Key.fromBase64(deviceKey);
      
      _encrypter = Encrypter(AES(key));
      _iv = IV.fromSecureRandom(16);
      
      _isInitialized = true;
      _logger.i('🔐 Encryption service initialized successfully');
    } catch (e) {
      _logger.e('❌ Failed to initialize encryption service: $e');
      rethrow;
    }
  }

  /// الحصول على أو إنشاء مفتاح تشفير للجهاز
  Future<String> _getOrCreateDeviceKey() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceKey = prefs.getString('device_encryption_key');
      
      if (deviceKey == null) {
        // إنشاء مفتاح جديد
        final key = Key.fromSecureRandom(32);
        deviceKey = key.base64;
        await prefs.setString('device_encryption_key', deviceKey);
        _logger.i('🔑 New device encryption key created');
      }
      
      return deviceKey;
    } catch (e) {
      _logger.e('❌ Error managing device key: $e');
      rethrow;
    }
  }

  /// تشفير نص
  Future<String> encryptText(String plainText) async {
    await _ensureInitialized();
    
    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      final result = '${_iv.base64}:${encrypted.base64}';
      _logger.d('🔐 Text encrypted successfully');
      return result;
    } catch (e) {
      _logger.e('❌ Encryption failed: $e');
      rethrow;
    }
  }

  /// فك تشفير نص
  Future<String> decryptText(String encryptedText) async {
    await _ensureInitialized();
    
    try {
      final parts = encryptedText.split(':');
      if (parts.length != 2) {
        throw 'Invalid encrypted text format';
      }
      
      final iv = IV.fromBase64(parts[0]);
      final encrypted = Encrypted.fromBase64(parts[1]);
      
      final decrypted = _encrypter.decrypt(encrypted, iv: iv);
      _logger.d('🔓 Text decrypted successfully');
      return decrypted;
    } catch (e) {
      _logger.e('❌ Decryption failed: $e');
      rethrow;
    }
  }

  /// تشفير بيانات JSON
  Future<String> encryptJson(Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    return await encryptText(jsonString);
  }

  /// فك تشفير بيانات JSON
  Future<Map<String, dynamic>> decryptJson(String encryptedData) async {
    final decryptedString = await decryptText(encryptedData);
    return jsonDecode(decryptedString) as Map<String, dynamic>;
  }

  /// تشفير كلمة مرور
  Future<String> hashPassword(String password, String salt) async {
    try {
      final bytes = utf8.encode(password + salt);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      _logger.e('❌ Password hashing failed: $e');
      rethrow;
    }
  }

  /// إنشاء salt عشوائي
  String generateSalt() {
    final bytes = List<int>.generate(32, (i) => 
        DateTime.now().millisecondsSinceEpoch % 256);
    return base64Encode(bytes);
  }

  /// تشفير ملف
  Future<Uint8List> encryptFile(Uint8List fileBytes) async {
    await _ensureInitialized();
    
    try {
      final encrypted = _encrypter.encryptBytes(fileBytes, iv: _iv);
      final result = Uint8List.fromList([
        ..._iv.bytes,
        ...encrypted.bytes,
      ]);
      _logger.d('📁 File encrypted successfully');
      return result;
    } catch (e) {
      _logger.e('❌ File encryption failed: $e');
      rethrow;
    }
  }

  /// فك تشفير ملف
  Future<Uint8List> decryptFile(Uint8List encryptedBytes) async {
    await _ensureInitialized();
    
    try {
      final iv = IV(encryptedBytes.sublist(0, 16));
      final encryptedData = Encrypted(encryptedBytes.sublist(16));
      
      final decrypted = _encrypter.decryptBytes(encryptedData, iv: iv);
      _logger.d('📁 File decrypted successfully');
      return Uint8List.fromList(decrypted);
    } catch (e) {
      _logger.e('❌ File decryption failed: $e');
      rethrow;
    }
  }

  /// حفظ بيانات مشفرة في SharedPreferences
  Future<void> saveEncryptedData(String key, Map<String, dynamic> data) async {
    try {
      final encryptedData = await encryptJson(data);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('encrypted_$key', encryptedData);
      _logger.d('💾 Encrypted data saved: $key');
    } catch (e) {
      _logger.e('❌ Failed to save encrypted data: $e');
      rethrow;
    }
  }

  /// قراءة بيانات مشفرة من SharedPreferences
  Future<Map<String, dynamic>?> loadEncryptedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString('encrypted_$key');
      
      if (encryptedData == null) {
        return null;
      }
      
      final data = await decryptJson(encryptedData);
      _logger.d('💾 Encrypted data loaded: $key');
      return data;
    } catch (e) {
      _logger.e('❌ Failed to load encrypted data: $e');
      return null;
    }
  }

  /// مسح البيانات المشفرة
  Future<void> clearEncryptedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('encrypted_$key');
      _logger.d('🗑️ Encrypted data cleared: $key');
    } catch (e) {
      _logger.e('❌ Failed to clear encrypted data: $e');
    }
  }

  /// التأكد من تهيئة الخدمة
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// مسح جميع مفاتيح التشفير (للطوارئ)
  Future<void> clearAllEncryptionKeys() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('device_encryption_key');
      _isInitialized = false;
      _logger.w('🚨 All encryption keys cleared');
    } catch (e) {
      _logger.e('❌ Failed to clear encryption keys: $e');
    }
  }

  /// فحص سلامة التشفير
  Future<bool> testEncryption() async {
    try {
      await _ensureInitialized();
      
      const testData = 'Test encryption data 🔐';
      final encrypted = await encryptText(testData);
      final decrypted = await decryptText(encrypted);
      
      final isValid = testData == decrypted;
      _logger.i('🧪 Encryption test: ${isValid ? 'PASSED' : 'FAILED'}');
      return isValid;
    } catch (e) {
      _logger.e('❌ Encryption test failed: $e');
      return false;
    }
  }
}
