@echo off
echo ===== اختبار اتصال PostgreSQL =====
echo.

echo جاري اختبار الاتصال...
psql -U postgres -c "SELECT version();"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ PostgreSQL يعمل بنجاح!
    echo.
    echo جاري إنشاء قاعدة بيانات للمشروع...
    psql -U postgres -c "CREATE DATABASE moon_memory_local;"
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم إنشاء قاعدة البيانات moon_memory_local بنجاح!
    ) else (
        echo ⚠️ قاعدة البيانات موجودة مسبقاً أو حدث خطأ
    )
) else (
    echo ❌ فشل في الاتصال بـ PostgreSQL
    echo تأكد من:
    echo 1. تثبيت PostgreSQL بنجاح
    echo 2. تشغيل خدمة PostgreSQL
    echo 3. صحة كلمة المرور
)

echo.
pause
