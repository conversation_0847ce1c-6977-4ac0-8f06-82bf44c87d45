-- 🔧 إصلاح شامل للتخزين والمواقع
-- Complete Storage and Locations Fix
-- Date: 2025-01-19

-- ===== 🧹 تنظيف شامل =====

-- إلغاء تفعيل RLS نهائياً
ALTER TABLE IF EXISTS public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.videos DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.devices DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_sessions DISABLE ROW LEVEL SECURITY;

-- حذف جميع السياسات
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname IN ('public', 'storage')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(policy_record.policyname) || 
                ' ON ' || quote_ident(policy_record.schemaname) || '.' || quote_ident(policy_record.tablename);
    END LOOP;
END $$;

-- ===== 🔓 منح صلاحيات كاملة =====

-- صلاحيات الجداول
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated, anon, service_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA storage TO authenticated, anon, service_role;
GRANT USAGE ON SCHEMA public TO authenticated, anon, service_role;
GRANT USAGE ON SCHEMA storage TO authenticated, anon, service_role;

-- ===== 📁 إعداد Storage buckets =====

-- حذف buckets الموجودة وإعادة إنشاؤها
DELETE FROM storage.buckets WHERE id IN ('photos', 'videos');

-- إنشاء bucket الصور مع MIME types صحيحة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'photos', 
    'photos', 
    true, 
    52428800, -- 50MB
    ARRAY[
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/webp',
        'image/gif',
        'image/bmp',
        'image/tiff'
    ]
);

-- إنشاء bucket الفيديوهات مع MIME types صحيحة
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'videos', 
    'videos', 
    true, 
    209715200, -- 200MB
    ARRAY[
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo',
        'video/webm',
        'video/3gpp',
        'video/x-flv',
        'application/octet-stream' -- للملفات العامة
    ]
);

-- ===== 📍 إعداد المواقع الـ 70 =====

-- حذف المواقع الموجودة
DELETE FROM public.locations;

-- إنشاء المواقع U (من U101 إلى U125)
DO $$
BEGIN
    FOR i IN 101..125 LOOP
        INSERT INTO public.locations (
            location_code,
            location_type,
            location_number,
            location_name_ar,
            location_name_en,
            sort_order,
            is_active,
            is_available
        ) VALUES (
            'U' || i,
            'U',
            i::text,
            'موقع جامعي ' || i,
            'University Location ' || i,
            i,
            true,
            true
        );
    END LOOP;
END $$;

-- إنشاء المواقع C (من C101 إلى C145)
DO $$
BEGIN
    FOR i IN 101..145 LOOP
        INSERT INTO public.locations (
            location_code,
            location_type,
            location_number,
            location_name_ar,
            location_name_en,
            sort_order,
            is_active,
            is_available
        ) VALUES (
            'C' || i,
            'C',
            i::text,
            'موقع مجتمعي ' || i,
            'Community Location ' || i,
            i + 100, -- ترتيب بعد المواقع الجامعية
            true,
            true
        );
    END LOOP;
END $$;

-- ===== ✅ التحقق من النجاح =====

-- فحص buckets
SELECT 
    'Storage Buckets:' as check_type,
    id as bucket_name,
    public as is_public,
    file_size_limit,
    array_length(allowed_mime_types, 1) as mime_types_count
FROM storage.buckets 
WHERE id IN ('photos', 'videos')
ORDER BY id;

-- فحص المواقع
SELECT 
    'Locations Count:' as check_type,
    location_type,
    COUNT(*) as count
FROM public.locations 
GROUP BY location_type
ORDER BY location_type;

-- فحص RLS
SELECT 
    'RLS Status:' as check_type,
    tablename,
    CASE WHEN rowsecurity THEN 'مفعل ❌' ELSE 'معطل ✅' END as status
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('photos', 'videos', 'users', 'locations')
ORDER BY tablename;

-- رسالة النجاح النهائية
SELECT 
    '🎉 تم إعداد النظام بنجاح!' as status,
    'Storage buckets: جاهزة مع MIME types صحيحة' as storage_status,
    'المواقع: 70 موقع (U101-U125, C101-C145)' as locations_status,
    'RLS: معطل نهائياً' as rls_status,
    'التطبيق جاهز للعمل!' as final_result;
