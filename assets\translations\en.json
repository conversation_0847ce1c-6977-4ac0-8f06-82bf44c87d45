{"app": {"name": "Moon Memory Camera", "slogan": "Documenting your achievements with Moon Memory"}, "common": {"camera": "Camera", "gallery": "Gallery", "settings": "Settings", "profile": "Profile", "save": "Save", "cancel": "Cancel", "delete": "Delete", "share": "Share", "error": "Error", "success": "Success", "loading": "Loading...", "user": "User", "ok": "OK", "yes": "Yes", "no": "No"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "password_required": "Please enter your password", "national_id": "National ID", "national_id_required": "Please enter your National ID", "national_id_length": "National ID must be 10 digits", "remember_me": "Remember me", "forgot_password": "Forgot Password?", "register": "Register", "login_failed": "<PERSON><PERSON> failed", "logout_confirm": "Are you sure you want to logout?", "logout_success": "Logged out successfully", "instructions": "App Instructions and Guidelines", "errors": {"device_unauthorized": "This device is not authorized. Please contact your administrator", "invalid_credentials": "Invalid National ID or password", "general_error": "An error occurred during login"}}, "camera": {"saved_to_gallery": "📁 Photo saved to gallery", "save_error": "❌ Error saving photo"}, "settings": {"language": "Language", "dark_mode": "Dark Mode", "notifications": "Notifications", "about": "About", "version": "Version", "language_changed": "Language changed"}, "permissions": {"camera_required": "Camera access is required", "location_required": "Location access is required", "storage_required": "Storage access is required", "microphone_required": "Microphone access is required", "location_service_disabled": "Location service must be enabled in settings", "all_permissions_required": "All required permissions must be granted"}, "errors": {"general": "Something went wrong. Please try again", "network": "Network error", "server": "Server error", "unauthorized": "Unauthorized", "not_found": "Not found"}, "welcome": {"greeting": "Welcome {username}", "select_location": "Select shooting location", "location_type": "Location Type", "location_number": "Location Number", "open_camera": "Open Camera"}, "gallery": {"title": "Gallery", "empty": "No photos or videos"}, "video": {"saved_successfully": "✅ Video saved successfully", "uploaded_successfully": "🚀 Video uploaded successfully", "saved_offline": "📱 Video saved offline - will upload when connected to internet", "saved_to_gallery": "📁 Video saved to gallery", "uploading_background": "☁️ Uploading in background...", "upload_complete": "✅ Upload complete • You can now share", "upload_pending": "⏳ Uploading in background • Wait to share", "processing": "⚙️ Processing video...", "share_ready": "🎉 Video ready to share!", "share_button": "Share", "save_error": "❌ Error saving video"}, "photo": {"saved_offline": "📱 Photo saved offline - will upload when connected to internet", "saved_to_gallery": "📁 Photo saved to gallery", "uploading_background": "☁️ Uploading in background...", "upload_complete": "✅ Upload complete • You can now share", "upload_pending": "⏳ Uploading in background • Wait to share", "processing": "⚙️ Processing photo...", "share_ready": "🎉 Photo ready to share!", "save_error": "❌ Error saving photo", "upload_success": "🚀 Photo uploaded successfully", "share_button": "Share"}, "upload": {"starting": "🚀 Starting upload...", "in_progress": "⏳ Uploading... Please wait", "success": "✅ Upload successful!", "failed": "❌ Upload failed, please try again", "no_internet": "📶 No internet connection - will upload automatically when connected", "saved_locally": "💾 Saved locally - will upload when internet is available", "background_upload": "☁️ Uploading in background, you can continue"}}