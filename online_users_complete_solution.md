# 🎯 مطلوب: تطوير نظام تتبع المستخدمين المتصلين

## 👋 مرحباً عزيزي مطور تطبيق الكاميرا

أنا أعمل على **تطبيق الإدارة** وأنت تعمل على **تطبيق الكاميرا**. نحتاج للتعاون لإنجاز ميزة **عرض المستخدمين المتصلين الآن**.

## 🏗️ تقسيم العمل

### 🖥️ **أنا (مطور تطبيق الإدارة):**
- ✅ **قاعدة البيانات:** أنشأت جميع الجداول والدوال المطلوبة
- 🔄 **صفحة العرض:** سأنشئ صفحة عرض المستخدمين المتصلين
- 📊 **التقارير:** سأضيف تقارير وإحصائيات

### 📱 **أنت (مطور تطبيق الكاميرا):**
- 🎯 **المطلوب منك:** إضافة تتبع الجلسات والأنشطة
- 📡 **إرسال البيانات:** لقاعدة البيانات المشتركة
- 🔄 **التحديث المستمر:** heartbeat كل دقيقة

---

## 🗄️ قاعدة البيانات (تم إنجازها)

### ✅ **ما أنجزته لك:**

#### 📊 **الجداول المُنشأة:**
1. **`user_sessions`** - لتتبع جلسات المستخدمين
2. **`user_activity_log`** - لتسجيل جميع الأنشطة
3. **تحديث جدول `users`** - إضافة أعمدة الحالة والموقع

#### 🛠️ **الدوال الجاهزة للاستخدام:**

```sql
-- 1. بدء جلسة جديدة (استخدمها عند تسجيل الدخول)
SELECT start_user_session(
    user_id::UUID,
    device_id::UUID,
    app_version::TEXT,
    ip_address::INET,
    latitude::DOUBLE PRECISION,
    longitude::DOUBLE PRECISION,
    location_name::TEXT
) as session_id;

-- 2. تحديث آخر نشاط (استخدمها كل دقيقة)
SELECT update_user_last_activity(
    user_id::UUID,
    session_id::UUID,
    latitude::DOUBLE PRECISION,
    longitude::DOUBLE PRECISION,
    location_name::TEXT
);

-- 3. إنهاء الجلسة (استخدمها عند تسجيل الخروج)
SELECT end_user_session(session_id::UUID);
```

#### 📋 **جدول تسجيل الأنشطة:**

```sql
-- إدراج نشاط جديد
INSERT INTO user_activity_log (
    user_id,
    session_id,
    activity_type,  -- 'photo_taken', 'video_recorded', 'heartbeat', etc.
    activity_data,  -- JSON data
    location_lat,
    location_lng,
    file_size_bytes,
    duration_seconds
) VALUES (...);
```

---

## 📱 المطلوب منك في تطبيق الكاميرا

### 🎯 **الهدف:**
إضافة تتبع شامل لجلسات المستخدمين وأنشطتهم ليظهروا في تطبيق الإدارة كـ "متصلين الآن".

### 📦 **Dependencies المطلوبة:**

أضف في `pubspec.yaml`:
```yaml
dependencies:
  geolocator: ^9.0.2  # للحصول على الموقع
  device_info_plus: ^9.1.0  # لمعلومات الجهاز (اختياري)
```

### 1️⃣ **إنشاء خدمة تتبع الجلسات**

**ملف جديد:** `lib/services/session_service.dart`

```dart
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';

class SessionService {
  static final SessionService _instance = SessionService._internal();
  factory SessionService() => _instance;
  SessionService._internal();

  String? _currentSessionId;
  Timer? _heartbeatTimer;
  final SupabaseClient _supabase = Supabase.instance.client;

  /// بدء جلسة جديدة عند تسجيل الدخول
  Future<String?> startSession({
    required String userId,
    required String deviceId,
    String? appVersion,
  }) async {
    try {
      Position? position = await _getCurrentLocation();

      final response = await _supabase.rpc('start_user_session', params: {
        'p_user_id': userId,
        'p_device_id': deviceId,
        'p_app_version': appVersion ?? '1.0.0',
        'p_location_lat': position?.latitude,
        'p_location_lng': position?.longitude,
        'p_location_name': await _getLocationName(position),
      });

      _currentSessionId = response as String?;

      // بدء heartbeat كل دقيقة
      _startHeartbeat(userId);

      print('✅ تم بدء الجلسة: $_currentSessionId');
      return _currentSessionId;
    } catch (e) {
      print('❌ خطأ في بدء الجلسة: $e');
      return null;
    }
  }

  /// تحديث آخر نشاط (heartbeat)
  Future<void> updateActivity(String userId) async {
    if (_currentSessionId == null) return;

    try {
      Position? position = await _getCurrentLocation();

      await _supabase.rpc('update_user_last_activity', params: {
        'p_user_id': userId,
        'p_session_id': _currentSessionId,
        'p_location_lat': position?.latitude,
        'p_location_lng': position?.longitude,
        'p_location_name': await _getLocationName(position),
      });

      print('🔄 تم تحديث النشاط');
    } catch (e) {
      print('❌ خطأ في تحديث النشاط: $e');
    }
  }

  /// إنهاء الجلسة عند تسجيل الخروج
  Future<void> endSession() async {
    if (_currentSessionId == null) return;

    try {
      await _supabase.rpc('end_user_session', params: {
        'p_session_id': _currentSessionId,
      });

      _currentSessionId = null;
      _heartbeatTimer?.cancel();

      print('✅ تم إنهاء الجلسة');
    } catch (e) {
      print('❌ خطأ في إنهاء الجلسة: $e');
    }
  }

  /// تسجيل نشاط معين (تصوير، فيديو، إلخ)
  Future<void> logActivity({
    required String userId,
    required String activityType, // 'photo_taken', 'video_recorded', etc.
    Map<String, dynamic>? activityData,
    int? fileSizeBytes,
    int? durationSeconds,
  }) async {
    try {
      Position? position = await _getCurrentLocation();

      await _supabase.from('user_activity_log').insert({
        'user_id': userId,
        'session_id': _currentSessionId,
        'activity_type': activityType,
        'activity_data': activityData ?? {},
        'location_lat': position?.latitude,
        'location_lng': position?.longitude,
        'file_size_bytes': fileSizeBytes,
        'duration_seconds': durationSeconds,
      });

      print('📝 تم تسجيل النشاط: $activityType');
    } catch (e) {
      print('❌ خطأ في تسجيل النشاط: $e');
    }
  }

  /// بدء heartbeat كل دقيقة
  void _startHeartbeat(String userId) {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      updateActivity(userId);
      logActivity(userId: userId, activityType: 'heartbeat');
    });
  }

  /// الحصول على الموقع الحالي
  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return null;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return null;
      }

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: Duration(seconds: 10),
      );
    } catch (e) {
      print('❌ خطأ في الحصول على الموقع: $e');
      return null;
    }
  }

  /// الحصول على اسم الموقع (يمكن تحسينه لاحقاً)
  Future<String?> _getLocationName(Position? position) async {
    if (position == null) return null;
    // يمكن إضافة خدمة Geocoding هنا لاحقاً
    return 'الموقع الحالي (${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)})';
  }
}
```

### 2️⃣ **تحديث صفحة تسجيل الدخول**

**الملف:** `lib/screens/login_screen.dart`

```dart
// إضافة import في أعلى الملف
import '../services/session_service.dart';

// في دالة تسجيل الدخول الموجودة
Future<void> _login() async {
  try {
    // كود تسجيل الدخول الموجود...
    final response = await Supabase.instance.client.auth.signInWithPassword(
      email: _emailController.text,
      password: _passwordController.text,
    );

    if (response.user != null) {
      // 🆕 إضافة هذا الكود
      String deviceId = await _getDeviceId();

      String? sessionId = await SessionService().startSession(
        userId: response.user!.id,
        deviceId: deviceId,
        appVersion: '1.0.0',
      );

      if (sessionId != null) {
        print('✅ تم بدء الجلسة بنجاح: $sessionId');
      }

      // الانتقال للصفحة الرئيسية (الكود الموجود)
      Navigator.pushReplacementNamed(context, '/home');
    }
  } catch (e) {
    print('❌ خطأ في تسجيل الدخول: $e');
    // معالجة الأخطاء الموجودة...
  }
}

// 🆕 إضافة هذه الدالة
Future<String> _getDeviceId() async {
  try {
    final devices = await Supabase.instance.client
        .from('devices')
        .select('id')
        .eq('user_id', Supabase.instance.client.auth.currentUser!.id)
        .limit(1);

    return devices.isNotEmpty ? devices.first['id'] : '';
  } catch (e) {
    print('❌ خطأ في الحصول على معرف الجهاز: $e');
    return '';
  }
}
```

### 3️⃣ **تحديث الصفحة الرئيسية**

**الملف:** `lib/screens/home_screen.dart`

```dart
// إضافة import في أعلى الملف
import '../services/session_service.dart';

// تحديث الكلاس الموجود
class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {

  @override
  void initState() {
    super.initState();
    // 🆕 إضافة مراقب دورة حياة التطبيق
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // 🆕 إزالة المراقب
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // 🆕 إضافة هذه الدالة لمراقبة حالة التطبيق
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId == null) return;

    switch (state) {
      case AppLifecycleState.resumed:
        SessionService().logActivity(
          userId: userId,
          activityType: 'app_open',
        );
        break;
      case AppLifecycleState.paused:
        SessionService().logActivity(
          userId: userId,
          activityType: 'app_close',
        );
        break;
      default:
        break;
    }
  }

  // 🔄 تحديث دالة التقاط الصورة الموجودة
  Future<void> _takePhoto() async {
    // كود التقاط الصورة الموجود...

    // 🆕 إضافة تسجيل النشاط
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId != null) {
      await SessionService().logActivity(
        userId: userId,
        activityType: 'photo_taken',
        fileSizeBytes: photoFileSize, // استخدم المتغير الموجود
      );
    }
  }

  // 🔄 تحديث دالة تسجيل الفيديو الموجودة
  Future<void> _recordVideo() async {
    // كود تسجيل الفيديو الموجود...

    // 🆕 إضافة تسجيل النشاط
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId != null) {
      await SessionService().logActivity(
        userId: userId,
        activityType: 'video_recorded',
        fileSizeBytes: videoFileSize, // استخدم المتغير الموجود
        durationSeconds: videoDuration, // استخدم المتغير الموجود
      );
    }
  }

  // 🔄 تحديث دالة تسجيل الخروج الموجودة
  Future<void> _logout() async {
    // 🆕 إنهاء الجلسة قبل تسجيل الخروج
    await SessionService().endSession();

    // كود تسجيل الخروج الموجود
    await Supabase.instance.client.auth.signOut();
    Navigator.pushReplacementNamed(context, '/login');
  }
}
```

### 4️⃣ **إضافة أذونات الموقع**

**الملف:** `android/app/src/main/AndroidManifest.xml`

```xml
<!-- إضافة هذه الأذونات -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
```

---

## 🧪 اختبار التطبيق

### ✅ **قائمة التحقق:**

#### 📱 **تطبيق الكاميرا:**
- [ ] إضافة SessionService
- [ ] تحديث صفحة تسجيل الدخول
- [ ] تحديث الصفحة الرئيسية
- [ ] إضافة أذونات الموقع
- [ ] اختبار تسجيل الدخول
- [ ] اختبار التقاط الصورة
- [ ] اختبار تسجيل الفيديو
- [ ] اختبار تسجيل الخروج

#### 🖥️ **تطبيق الإدارة (سأعمل عليه):**
- [ ] إنشاء صفحة المستخدمين المتصلين
- [ ] عرض البيانات من قاعدة البيانات
- [ ] تحديث تلقائي كل 30 ثانية
- [ ] إضافة تفاصيل المستخدم

---

## 🎯 خطة التنفيذ

### 📅 **المرحلة الأولى (الأولوية العالية):**
1. **أنت:** إضافة SessionService في تطبيق الكاميرا
2. **أنت:** تحديث صفحة تسجيل الدخول
3. **أنت:** تحديث الصفحة الرئيسية
4. **أنا:** إنشاء صفحة المستخدمين المتصلين في تطبيق الإدارة

### 📅 **المرحلة الثانية (الاختبار):**
1. **أنت:** اختبار تطبيق الكاميرا
2. **أنا:** اختبار تطبيق الإدارة
3. **معاً:** اختبار التكامل بين التطبيقين

### 📅 **المرحلة الثالثة (التحسين):**
1. **أنا:** إضافة تقارير وإحصائيات
2. **أنت:** تحسين دقة الموقع
3. **معاً:** تحسين الأداء

---

## 📞 التواصل والدعم

### 🤝 **كيفية التعاون:**

1. **عند الانتهاء من كل مرحلة:** أخبرني لأختبر من جانب تطبيق الإدارة
2. **عند مواجهة مشاكل:** أرسل لي رسالة الخطأ وسأساعدك
3. **عند الحاجة لتعديل قاعدة البيانات:** أخبرني وسأقوم بالتعديل

### 🔍 **طرق الاختبار:**

```sql
-- للتحقق من الجلسات النشطة
SELECT * FROM user_sessions WHERE is_active = true;

-- للتحقق من آخر الأنشطة
SELECT * FROM user_activity_log ORDER BY timestamp DESC LIMIT 10;

-- للتحقق من المستخدمين المتصلين
SELECT * FROM v_online_users;
```

### 📱 **رسائل Debug مهمة:**

ابحث عن هذه الرسائل في console:
- ✅ `تم بدء الجلسة: session-id`
- 🔄 `تم تحديث النشاط`
- 📝 `تم تسجيل النشاط: photo_taken`
- ✅ `تم إنهاء الجلسة`

---

## 🚀 ابدأ الآن!

### 🎯 **الخطوة الأولى:**
1. أضف Dependencies في `pubspec.yaml`
2. أنشئ ملف `lib/services/session_service.dart`
3. انسخ الكود المرفق

### 📞 **بعد الانتهاء:**
أخبرني عندما تنتهي من إضافة SessionService وسأبدأ في تطوير صفحة العرض في تطبيق الإدارة.

**نحن فريق واحد لإنجاح هذا المشروع! 🤝**

---

## 📋 ملاحظات مهمة

1. **الأمان:** جميع البيانات محمية في Supabase
2. **الأداء:** Heartbeat كل دقيقة لا يؤثر على الأداء
3. **البطارية:** الموقع يُطلب بدقة متوسطة لتوفير البطارية
4. **الشبكة:** النظام يعمل مع اتصال الإنترنت فقط
5. **الخصوصية:** الموقع يُحفظ بدقة 4 خانات عشرية فقط

**بالتوفيق في التطوير! 🎉**