import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'core/config/supabase_config.dart';
import 'core/theme/app_theme.dart';
import 'core/routing/app_router.dart';
import 'core/services/auto_upload_service.dart';
import 'core/services/enhanced_upload_service.dart';
import 'core/services/migration_service.dart';
import 'core/services/session_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تحميل متغيرات البيئة
  await dotenv.load(fileName: '.env');

  await EasyLocalization.ensureInitialized();

  // التحقق من وجود إعدادات Supabase
  if (!SupabaseConfig.isConfigured) {
    throw Exception('Supabase configuration is missing. Please check your .env file.');
  }

  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );

  // ترحيل الملفات القديمة إلى النظام الجديد
  await MigrationService().runMigrationIfNeeded();

  // بدء خدمة الرفع التلقائي المحسنة
  EnhancedUploadService().startAutoUpload();

  // إبقاء الخدمة القديمة كـ fallback
  AutoUploadService().startAutoUpload();

  // تهيئة خدمة الخلفية للـ Live Tracking (ستبدأ عند تسجيل الدخول)
  // BackgroundLocationService سيتم تهيئتها في SessionService

  // استعادة جلسة المستخدم إذا كانت موجودة
  final sessionManager = SessionManager();
  await sessionManager.restoreSession();

  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('ar'),
      saveLocale: true,
      useOnlyLangCode: true,
      useFallbackTranslations: true,
      child: const ProviderScope(child: MoonMemoryApp()),
    ),
  );
}

class MoonMemoryApp extends StatelessWidget {
  const MoonMemoryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      title: 'app.name'.tr(),
      theme: AppTheme.theme,
      initialRoute: AppRoutes.splash,
      onGenerateRoute: AppRouter.generateRoute,
      builder: (context, child) {
        return Directionality(
          textDirection: context.locale.languageCode == 'ar'
            ? ui.TextDirection.rtl
            : ui.TextDirection.ltr,
          child: child ?? const SizedBox(),
        );
      },
    );
  }
}
