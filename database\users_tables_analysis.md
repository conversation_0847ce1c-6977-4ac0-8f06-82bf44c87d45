# 📊 تحليل الفرق بين جدولي المستخدمين

## 🎯 **المشكلة الحالية**
يوجد تداخل وتضارب بين جدولين للمستخدمين:
- `auth.users` (جدول Supabase الأساسي)
- `public.users` (جدول التطبيق المخصص)

## 📋 **الفرق بين الجدولين**

### 🔐 **جدول `auth.users` (Supabase)**
```sql
-- جدول مدمج في Supabase - لا يمكن تعديل بنيته
CREATE TABLE auth.users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE,
    encrypted_password TEXT,
    email_confirmed_at TIMESTAMP WITH TIME ZONE,
    invited_at TIMESTAMP WITH TIME ZONE,
    confirmation_token TEXT,
    confirmation_sent_at TIMESTAMP WITH TIME ZONE,
    recovery_token TEXT,
    recovery_sent_at TIMESTAMP WITH TIME ZONE,
    email_change_token_new TEXT,
    email_change TEXT,
    email_change_sent_at TIMESTAMP WITH TIME ZONE,
    last_sign_in_at TIMESTAMP WITH TIME ZONE,
    raw_app_meta_data JSONB,
    raw_user_meta_data JSONB,
    is_super_admin BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    phone TEXT,
    phone_confirmed_at TIMESTAMP WITH TIME ZONE,
    phone_change TEXT,
    phone_change_token TEXT,
    phone_change_sent_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    email_change_token_current TEXT,
    email_change_confirm_status SMALLINT,
    banned_until TIMESTAMP WITH TIME ZONE,
    reauthentication_token TEXT,
    reauthentication_sent_at TIMESTAMP WITH TIME ZONE,
    is_sso_user BOOLEAN,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

**الغرض:**
- إدارة المصادقة والأمان
- تخزين كلمات المرور المشفرة
- إدارة الجلسات والتوكنات
- التحقق من البريد الإلكتروني والهاتف

### 👥 **جدول `public.users` (التطبيق)**
```sql
-- جدول مخصص للتطبيق - يمكن تعديله
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    avatar_url TEXT,
    
    -- معلومات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user',
    
    -- إعدادات الحساب
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    
    -- معلومات إضافية
    department TEXT,
    position TEXT,
    notes TEXT,
    
    -- التوقيتات
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);
```

**الغرض:**
- تخزين معلومات التطبيق الخاصة
- إدارة الصلاحيات والأدوار
- تخزين البيانات الشخصية والمهنية
- إعدادات الحساب المخصصة

## ⚠️ **المشاكل الحالية**

### 1. **تضارب في البيانات**
```sql
-- في auth.users
email: "<EMAIL>"
phone: NULL
created_at: "2025-01-15 10:00:00"

-- في public.users
email: "<EMAIL>"  -- مكرر
phone: "+************"              -- مختلف
created_at: "2025-01-15 10:05:00"   -- مختلف
```

### 2. **تعقيد في إدارة الجلسات**
```dart
// مشكلة: أي جدول نستخدم؟
final userId = supabase.auth.currentUser?.id;  // من auth.users
final userInfo = await supabase
    .from('users')                             // من public.users
    .select('*')
    .eq('id', userId)
    .single();
```

### 3. **مشاكل في إنشاء المستخدمين**
```dart
// خطوات معقدة:
// 1. إنشاء في auth.users
final authUser = await supabase.auth.admin.createUser(...);

// 2. إنشاء في public.users
await supabase.from('users').insert({
    'id': authUser.user!.id,  // ربط بـ auth.users
    // باقي البيانات...
});
```

## 🔧 **الحلول المقترحة**

### **الحل الأول: الإبقاء على الجدولين (الموصى به)**
```sql
-- تحسين العلاقة بين الجدولين
-- 1. استخدام auth.users للمصادقة فقط
-- 2. استخدام public.users لبيانات التطبيق
-- 3. إنشاء دوال لتزامن البيانات

CREATE OR REPLACE FUNCTION sync_user_data()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث public.users عند تغيير auth.users
    UPDATE public.users 
    SET 
        email = NEW.email,
        phone = NEW.phone,
        updated_at = NOW()
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger للتزامن التلقائي
CREATE TRIGGER sync_auth_to_public_users
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION sync_user_data();
```

### **الحل الثاني: توحيد الجدولين (غير موصى به)**
```sql
-- ⚠️ مشكلة: لا يمكن تعديل auth.users
-- ⚠️ مشكلة: فقدان ميزات Supabase المدمجة
-- ⚠️ مشكلة: تعقيد في المصادقة
```

## ✅ **التوصية النهائية**

### **احتفظ بالجدولين مع تحسين العلاقة:**

1. **استخدم `auth.users` لـ:**
   - المصادقة والأمان
   - كلمات المرور
   - الجلسات والتوكنات
   - التحقق من البريد/الهاتف

2. **استخدم `public.users` لـ:**
   - بيانات التطبيق
   - الصلاحيات والأدوار
   - المعلومات الشخصية
   - إعدادات الحساب

3. **أنشئ دوال مساعدة:**
```sql
-- دالة إنشاء مستخدم كامل
CREATE OR REPLACE FUNCTION create_complete_user(
    p_email TEXT,
    p_password TEXT,
    p_national_id TEXT,
    p_full_name TEXT
) RETURNS JSON AS $$
DECLARE
    auth_user_id UUID;
    result JSON;
BEGIN
    -- 1. إنشاء في auth.users (باستخدام Admin API)
    -- 2. إنشاء في public.users
    -- 3. إرجاع النتيجة
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🚨 **إصلاح المشاكل الحالية**

### **خطوات الإصلاح:**
1. تنظيف البيانات المتضاربة
2. إنشاء دوال التزامن
3. تحديث كود التطبيق
4. اختبار شامل للنظام
