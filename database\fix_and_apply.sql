-- 🔧 إصلاح وتطبيق التحسينات
-- Fix and Apply Optimizations
-- Date: 2025-01-17

-- ===== فحص الجداول الموجودة =====
SELECT 'فحص الجداول الموجودة...' as step;

-- فحص جدول locations
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'locations' AND table_schema = 'public') THEN
        RAISE NOTICE 'جدول locations موجود';
        
        -- فحص الأعمدة الموجودة
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'location_code' AND table_schema = 'public') THEN
            RAISE NOTICE 'عمود location_code غير موجود - سيتم إضافته';
        END IF;
    ELSE
        RAISE NOTICE 'جدول locations غير موجود - سيتم إنشاؤه';
    END IF;
END $$;

-- ===== إيقاف RLS مؤقتاً =====
SELECT 'إيقاف Row Level Security مؤقتاً...' as step;

DO $$
BEGIN
    -- إيقاف RLS على الجداول الموجودة
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'photos' AND table_schema = 'public') THEN
        ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos' AND table_schema = 'public') THEN
        ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'locations' AND table_schema = 'public') THEN
        ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- ===== إنشاء/تحديث جدول المواقع =====
SELECT 'إنشاء/تحديث جدول المواقع...' as step;

-- حذف الجدول الموجود وإعادة إنشاؤه
DROP TABLE IF EXISTS public.locations CASCADE;

-- إنشاء جدول المواقع الجديد
CREATE TABLE public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    location_code TEXT UNIQUE NOT NULL,
    location_type TEXT NOT NULL CHECK (location_type IN ('U', 'C')),
    location_number TEXT NOT NULL,
    location_name_ar TEXT,
    location_name_en TEXT,
    sort_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

SELECT 'تم إنشاء جدول المواقع الجديد' as status;

-- ===== إضافة المواقع الـ 70 =====
SELECT 'إضافة المواقع الـ 70...' as step;

-- إضافة مواقع U (U101 - U125)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'U' || LPAD(i::text, 3, '0'),
    'U',
    LPAD(i::text, 3, '0'),
    'موقع U' || LPAD(i::text, 3, '0'),
    'Location U' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 125) as i;

-- إضافة مواقع C (C101 - C145)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'C' || LPAD(i::text, 3, '0'),
    'C',
    LPAD(i::text, 3, '0'),
    'موقع C' || LPAD(i::text, 3, '0'),
    'Location C' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 145) as i;

SELECT 'تم إضافة ' || COUNT(*) || ' موقع' as locations_added FROM public.locations;

-- ===== تحديث جداول الصور والفيديوهات =====
SELECT 'تحديث جداول الصور والفيديوهات...' as step;

-- تحديث جدول الصور
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'photos' AND table_schema = 'public') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE public.photos 
        ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
        ADD COLUMN IF NOT EXISTS location_number TEXT,
        ADD COLUMN IF NOT EXISTS full_location_code TEXT,
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
        ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0;
        
        RAISE NOTICE 'تم تحديث جدول photos';
    END IF;
END $$;

-- تحديث جدول الفيديوهات
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos' AND table_schema = 'public') THEN
        -- إضافة الأعمدة الجديدة
        ALTER TABLE public.videos 
        ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
        ADD COLUMN IF NOT EXISTS location_number TEXT,
        ADD COLUMN IF NOT EXISTS full_location_code TEXT,
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
        ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0,
        ADD COLUMN IF NOT EXISTS duration_seconds INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS resolution TEXT;
        
        RAISE NOTICE 'تم تحديث جدول videos';
    END IF;
END $$;

-- ===== إنشاء الفهارس المحسنة =====
SELECT 'إنشاء الفهارس المحسنة...' as step;

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_active ON public.photos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_location_date ON public.photos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_upload_date ON public.photos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس الفيديوهات  
CREATE INDEX IF NOT EXISTS idx_videos_user_active ON public.videos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_location_date ON public.videos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_upload_date ON public.videos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس المواقع
CREATE INDEX IF NOT EXISTS idx_locations_type_sort ON public.locations(location_type, sort_order);
CREATE INDEX IF NOT EXISTS idx_locations_active ON public.locations(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_locations_code ON public.locations(location_code);

SELECT 'تم إنشاء الفهارس المحسنة' as status;

-- ===== إنشاء الدوال المحسنة =====
SELECT 'إنشاء الدوال المحسنة...' as step;

-- دالة تحديث إحصائيات موقع واحد
CREATE OR REPLACE FUNCTION update_location_statistics(location_code_param TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE public.locations 
    SET 
        total_photos = (
            SELECT COUNT(*) FROM public.photos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        total_videos = (
            SELECT COUNT(*) FROM public.videos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        last_used_at = GREATEST(
            (SELECT MAX(capture_timestamp) FROM public.photos WHERE full_location_code = location_code_param),
            (SELECT MAX(capture_timestamp) FROM public.videos WHERE full_location_code = location_code_param)
        ),
        updated_at = NOW()
    WHERE location_code = location_code_param;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث جميع الإحصائيات
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR location_rec IN SELECT location_code FROM public.locations WHERE is_active = true
    LOOP
        PERFORM update_location_statistics(location_rec.location_code);
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع';
END;
$$ LANGUAGE plpgsql;

-- دالة الحصول على المواقع مع الإحصائيات
CREATE OR REPLACE FUNCTION get_locations_with_stats()
RETURNS TABLE(
    location_code TEXT,
    location_type TEXT,
    location_name_ar TEXT,
    location_name_en TEXT,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.location_code,
        l.location_type,
        l.location_name_ar,
        l.location_name_en,
        l.total_photos,
        l.total_videos,
        (l.total_photos + l.total_videos) as total_files,
        l.last_used_at,
        l.is_active
    FROM public.locations l
    WHERE l.is_active = true
    ORDER BY l.location_type, l.sort_order;
END;
$$ LANGUAGE plpgsql;

-- دالة الحصول على الصور مرتبة
CREATE OR REPLACE FUNCTION get_photos_sorted_optimized(
    p_user_id UUID DEFAULT NULL,
    p_location_type TEXT DEFAULT NULL,
    p_sort_by TEXT DEFAULT 'date_desc',
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    file_name TEXT,
    url TEXT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.user_id,
        p.file_name,
        p.url,
        p.location,
        p.location_type,
        p.location_number,
        p.full_location_code,
        p.username,
        p.capture_timestamp,
        p.upload_timestamp
    FROM public.photos p
    WHERE 
        (p_user_id IS NULL OR p.user_id = p_user_id)
        AND (p_location_type IS NULL OR p.location_type = p_location_type)
        AND p.status = 'active'
    ORDER BY 
        CASE WHEN p_sort_by = 'date_desc' THEN p.capture_timestamp END DESC,
        CASE WHEN p_sort_by = 'date_asc' THEN p.capture_timestamp END ASC,
        CASE WHEN p_sort_by = 'location_date' THEN p.location_type END,
        CASE WHEN p_sort_by = 'location_date' THEN p.location_number END,
        CASE WHEN p_sort_by = 'location_date' THEN p.capture_timestamp END DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

SELECT 'تم إنشاء الدوال المحسنة' as status;

-- ===== تحديث البيانات الموجودة =====
SELECT 'تحديث البيانات الموجودة...' as step;

-- تحديث full_location_code للصور
UPDATE public.photos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL;

-- تحديث full_location_code للفيديوهات
UPDATE public.videos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL;

SELECT 'تم تحديث البيانات الموجودة' as status;

-- ===== إعادة تفعيل RLS =====
SELECT 'إعادة تفعيل Row Level Security...' as step;

-- إعادة تفعيل RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- إنشاء السياسات الآمنة
DROP POLICY IF EXISTS users_secure_policy ON public.users;
CREATE POLICY users_secure_policy ON public.users FOR ALL USING (auth.uid() = id);

DROP POLICY IF EXISTS photos_secure_policy ON public.photos;
CREATE POLICY photos_secure_policy ON public.photos FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS videos_secure_policy ON public.videos;
CREATE POLICY videos_secure_policy ON public.videos FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS locations_read_policy ON public.locations;
CREATE POLICY locations_read_policy ON public.locations FOR SELECT USING (true);

SELECT 'تم إعادة تفعيل RLS الآمن' as status;

-- ===== تقرير النتائج النهائي =====
SELECT '🎉 تم إصلاح وتطبيق جميع التحسينات بنجاح!' as final_status;
SELECT COUNT(*) as total_locations FROM public.locations;
SELECT COUNT(*) as total_photos FROM public.photos;
SELECT COUNT(*) as total_videos FROM public.videos;
SELECT COUNT(*) as total_users FROM public.users;
