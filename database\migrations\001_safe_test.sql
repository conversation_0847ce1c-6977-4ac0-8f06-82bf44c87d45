-- اختبار آمن جداً - إضافة الحقول الأساسية فقط
-- Safe Test Migration: 001_safe_test.sql
-- Date: 2025-01-12

-- فحص الجدول الحالي أولاً
SELECT 'Current devices table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
ORDER BY ordinal_position;

-- إضافة الحقول الجديدة واحد تلو الآخر
DO $$
BEGIN
    -- إضافة device_fingerprint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'device_fingerprint'
    ) THEN
        ALTER TABLE devices ADD COLUMN device_fingerprint TEXT;
        RAISE NOTICE 'Added device_fingerprint column';
    ELSE
        RAISE NOTICE 'device_fingerprint column already exists';
    END IF;
    
    -- إضافة android_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'android_id'
    ) THEN
        ALTER TABLE devices ADD COLUMN android_id TEXT;
        RAISE NOTICE 'Added android_id column';
    ELSE
        RAISE NOTICE 'android_id column already exists';
    END IF;
    
    -- إضافة build_fingerprint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'build_fingerprint'
    ) THEN
        ALTER TABLE devices ADD COLUMN build_fingerprint TEXT;
        RAISE NOTICE 'Added build_fingerprint column';
    ELSE
        RAISE NOTICE 'build_fingerprint column already exists';
    END IF;
    
    -- إضافة confidence_score
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'confidence_score'
    ) THEN
        ALTER TABLE devices ADD COLUMN confidence_score DECIMAL(5,2);
        RAISE NOTICE 'Added confidence_score column';
    ELSE
        RAISE NOTICE 'confidence_score column already exists';
    END IF;
    
    -- إضافة trust_level
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'trust_level'
    ) THEN
        ALTER TABLE devices ADD COLUMN trust_level TEXT;
        RAISE NOTICE 'Added trust_level column';
    ELSE
        RAISE NOTICE 'trust_level column already exists';
    END IF;
    
    -- إضافة last_verified_at
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'devices' AND column_name = 'last_verified_at'
    ) THEN
        ALTER TABLE devices ADD COLUMN last_verified_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added last_verified_at column';
    ELSE
        RAISE NOTICE 'last_verified_at column already exists';
    END IF;
END $$;

-- إضافة فهارس أساسية
CREATE INDEX IF NOT EXISTS idx_devices_device_fingerprint ON devices(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_devices_android_id ON devices(android_id);

-- فحص النتيجة النهائية
SELECT 'Updated devices table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
ORDER BY ordinal_position;

-- رسالة نجاح
SELECT 'Migration completed successfully! ✅' as status;
