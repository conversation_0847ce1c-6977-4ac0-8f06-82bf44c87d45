-- إعداد النظام الكامل لتطبيق Moon Memory - نسخة محسنة
-- Complete System Setup for Moon Memory App - Fixed Version
-- Date: 2025-01-15

-- ===== إنشاء جدول المستخدمين المحسن =====
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    avatar_url TEXT,
    
    -- معلومات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user' CHECK (account_type IN ('admin', 'user', 'supervisor')),
    
    -- إعدادات الحساب
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    
    -- معلومات إضافية
    department TEXT,
    position TEXT,
    notes TEXT,
    
    -- التوقيتات
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    -- قيود
    CONSTRAINT valid_national_id CHECK (LENGTH(national_id) = 10 AND national_id ~ '^\d{10}$')
);

-- ===== إنشاء جدول الأجهزة المحسن =====
CREATE TABLE IF NOT EXISTS devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- البصمة الرقمية المتقدمة
    device_fingerprint TEXT NOT NULL UNIQUE,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    
    -- معلومات الجهاز الأساسية
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    
    -- معلومات مجمعة للبصمة
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    
    -- نقاط الثقة والأمان
    confidence_score DECIMAL(5,2) DEFAULT 0 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    trust_level TEXT DEFAULT 'untrusted' CHECK (trust_level IN ('high', 'medium', 'low', 'untrusted', 'suspicious', 'blocked')),
    
    -- إدارة المصادقة والأمان
    auth_attempts INTEGER DEFAULT 0 CHECK (auth_attempts >= 0),
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- التوقيتات
    last_verified_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- قيود إضافية
    CONSTRAINT unique_user_android_id UNIQUE (user_id, android_id),
    CONSTRAINT valid_trust_level CHECK (trust_level IS NOT NULL)
);

-- ===== إنشاء جدول الصور المحسن =====
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    
    -- نظام المواقع الجديد
    location TEXT, -- للتوافق مع النظام القديم
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    
    -- معلومات المستخدم والتقاط
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وفهرسة
    sort_order INTEGER,
    tags TEXT[], -- مصفوفة من العلامات
    description TEXT,
    
    -- معلومات تقنية
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الحقل المُحسوب بعد إنشاء الجدول
-- سيتم إضافته في ملف منفصل لتجنب مشاكل التبعيات

-- ===== إنشاء جدول الفيديو المحسن =====
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    
    -- نظام المواقع الجديد
    location TEXT, -- للتوافق مع النظام القديم
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    
    -- معلومات المستخدم والتقاط
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وفهرسة
    sort_order INTEGER,
    tags TEXT[], -- مصفوفة من العلامات
    description TEXT,
    
    -- معلومات تقنية للفيديو
    resolution TEXT, -- مثل "1920x1080"
    fps INTEGER, -- إطارات في الثانية
    codec TEXT,
    bitrate INTEGER,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    thumbnail_url TEXT,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الحقل المُحسوب بعد إنشاء الجدول
-- سيتم إضافته في ملف منفصل لتجنب مشاكل التبعيات

-- ===== إنشاء جدول جلسات المستخدمين =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    
    -- معلومات الجلسة
    session_token TEXT UNIQUE,
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيتات
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    
    -- حالة الجلسة
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT -- 'logout', 'timeout', 'admin_terminated', etc.
);

-- ===== إنشاء جدول سجلات الإدارة =====
CREATE TABLE IF NOT EXISTS admin_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) NOT NULL,
    target_user_id UUID REFERENCES users(id),
    
    -- معلومات العملية
    action TEXT NOT NULL, -- 'create_user', 'reset_password', 'block_user', etc.
    entity_type TEXT, -- 'user', 'device', 'photo', 'video'
    entity_id UUID,
    
    -- تفاصيل العملية
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    
    -- معلومات إضافية
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيت
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الإحصائيات =====
CREATE TABLE IF NOT EXISTS system_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_date DATE DEFAULT CURRENT_DATE,
    
    -- إحصائيات المستخدمين
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users_today INTEGER DEFAULT 0,
    
    -- إحصائيات الأجهزة
    total_devices INTEGER DEFAULT 0,
    active_devices INTEGER DEFAULT 0,
    blocked_devices INTEGER DEFAULT 0,
    
    -- إحصائيات الملفات
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    photos_uploaded_today INTEGER DEFAULT 0,
    videos_uploaded_today INTEGER DEFAULT 0,
    
    -- إحصائيات التخزين
    total_storage_used_mb BIGINT DEFAULT 0,
    photos_storage_mb BIGINT DEFAULT 0,
    videos_storage_mb BIGINT DEFAULT 0,
    
    -- إحصائيات المواقع
    u_locations_used INTEGER DEFAULT 0,
    c_locations_used INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(stat_date)
);

-- ===== إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- ===== رسائل النجاح =====
SELECT 'تم إنشاء الجداول الأساسية بنجاح! ✅' as status;

-- عرض الجداول المُنشأة
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'devices', 'photos', 'videos', 'user_sessions', 'admin_logs', 'system_stats')
ORDER BY table_name;
