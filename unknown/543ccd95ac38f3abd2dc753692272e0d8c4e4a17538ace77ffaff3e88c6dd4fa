import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import 'encryption_service.dart';

/// خدمة الأمان الشاملة
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final _logger = getLogger();
  final _encryptionService = EncryptionService();
  
  // إعدادات الأمان
  static const int _maxLoginAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);
  static const Duration _sessionTimeout = Duration(hours: 2);

  /// تهيئة خدمة الأمان
  Future<void> initialize() async {
    try {
      await _encryptionService.initialize();
      await _checkSecurityStatus();
      _logger.i('🛡️ Security service initialized');
    } catch (e) {
      _logger.e('❌ Failed to initialize security service: $e');
      rethrow;
    }
  }

  /// فحص حالة الأمان العامة
  Future<Map<String, dynamic>> _checkSecurityStatus() async {
    final status = {
      'encryption_available': await _encryptionService.testEncryption(),
      'device_secured': await _isDeviceSecured(),
      'session_valid': await _isSessionValid(),
      'no_root_access': await _checkRootAccess(),
    };
    
    _logger.i('🔍 Security status: $status');
    return status;
  }

  /// فحص أمان الجهاز
  Future<bool> _isDeviceSecured() async {
    try {
      // فحص وجود قفل الشاشة
      // في التطبيق الحقيقي، استخدم مكتبة مثل local_auth
      return true; // مؤقت
    } catch (e) {
      _logger.w('⚠️ Device security check failed: $e');
      return false;
    }
  }

  /// فحص صلاحية الجلسة
  Future<bool> _isSessionValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionStart = prefs.getInt('session_start_time');
      
      if (sessionStart == null) return false;
      
      final sessionAge = DateTime.now().millisecondsSinceEpoch - sessionStart;
      return sessionAge < _sessionTimeout.inMilliseconds;
    } catch (e) {
      _logger.e('❌ Session validation failed: $e');
      return false;
    }
  }

  /// فحص وجود root access
  Future<bool> _checkRootAccess() async {
    try {
      // فحص ملفات root الشائعة
      final rootFiles = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
      ];
      
      for (final file in rootFiles) {
        if (await File(file).exists()) {
          _logger.w('⚠️ Root access detected: $file');
          return false; // الجهاز مكسور الحماية
        }
      }
      
      return true; // الجهاز آمن
    } catch (e) {
      _logger.w('⚠️ Root check failed: $e');
      return true; // افتراض الأمان عند الفشل
    }
  }

  /// تسجيل محاولة دخول
  Future<bool> recordLoginAttempt(String userId, bool success) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'login_attempts_$userId';
      
      if (success) {
        // مسح محاولات الفشل عند النجاح
        await prefs.remove(key);
        await prefs.remove('lockout_until_$userId');
        await _startSession();
        return true;
      } else {
        // تسجيل محاولة فاشلة
        final attempts = prefs.getInt(key) ?? 0;
        final newAttempts = attempts + 1;
        
        await prefs.setInt(key, newAttempts);
        
        if (newAttempts >= _maxLoginAttempts) {
          // قفل الحساب
          final lockoutUntil = DateTime.now().add(_lockoutDuration);
          await prefs.setInt('lockout_until_$userId', lockoutUntil.millisecondsSinceEpoch);
          _logger.w('🔒 Account locked due to failed attempts: $userId');
          return false;
        }
        
        _logger.w('⚠️ Failed login attempt $newAttempts/$_maxLoginAttempts for: $userId');
        return true;
      }
    } catch (e) {
      _logger.e('❌ Failed to record login attempt: $e');
      return false;
    }
  }

  /// فحص إذا كان الحساب مقفل
  Future<bool> isAccountLocked(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutUntil = prefs.getInt('lockout_until_$userId');
      
      if (lockoutUntil == null) return false;
      
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now < lockoutUntil) {
        final remainingTime = Duration(milliseconds: lockoutUntil - now);
        _logger.w('🔒 Account still locked for: ${remainingTime.inMinutes} minutes');
        return true;
      } else {
        // انتهت مدة القفل
        await prefs.remove('lockout_until_$userId');
        await prefs.remove('login_attempts_$userId');
        return false;
      }
    } catch (e) {
      _logger.e('❌ Failed to check account lock: $e');
      return false;
    }
  }

  /// بدء جلسة جديدة
  Future<void> _startSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('session_start_time', DateTime.now().millisecondsSinceEpoch);
      _logger.i('🚀 New session started');
    } catch (e) {
      _logger.e('❌ Failed to start session: $e');
    }
  }

  /// إنهاء الجلسة
  Future<void> endSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('session_start_time');
      _logger.i('🛑 Session ended');
    } catch (e) {
      _logger.e('❌ Failed to end session: $e');
    }
  }

  /// تشفير البيانات الحساسة
  Future<void> saveSecureData(String key, Map<String, dynamic> data) async {
    try {
      await _encryptionService.saveEncryptedData(key, data);
      _logger.d('🔐 Secure data saved: $key');
    } catch (e) {
      _logger.e('❌ Failed to save secure data: $e');
      rethrow;
    }
  }

  /// قراءة البيانات المشفرة
  Future<Map<String, dynamic>?> loadSecureData(String key) async {
    try {
      final data = await _encryptionService.loadEncryptedData(key);
      if (data != null) {
        _logger.d('🔓 Secure data loaded: $key');
      }
      return data;
    } catch (e) {
      _logger.e('❌ Failed to load secure data: $e');
      return null;
    }
  }

  /// تنظيف البيانات الحساسة
  Future<void> clearSecureData(String key) async {
    try {
      await _encryptionService.clearEncryptedData(key);
      _logger.d('🗑️ Secure data cleared: $key');
    } catch (e) {
      _logger.e('❌ Failed to clear secure data: $e');
    }
  }

  /// إنشاء hash آمن للبيانات
  String createSecureHash(String data, String salt) {
    final bytes = utf8.encode(data + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من سلامة البيانات
  bool verifyDataIntegrity(String data, String expectedHash, String salt) {
    final actualHash = createSecureHash(data, salt);
    return actualHash == expectedHash;
  }

  /// تنظيف البيانات الحساسة من الذاكرة
  void clearSensitiveMemory() {
    // في Dart، لا يمكن التحكم المباشر في الذاكرة
    // لكن يمكن تشغيل garbage collector
    _logger.d('🧹 Memory cleanup requested');
  }

  /// فحص أمان التطبيق الشامل
  Future<Map<String, dynamic>> performSecurityAudit() async {
    final audit = {
      'timestamp': DateTime.now().toIso8601String(),
      'encryption_test': await _encryptionService.testEncryption(),
      'device_secured': await _isDeviceSecured(),
      'session_valid': await _isSessionValid(),
      'no_root_access': await _checkRootAccess(),
      'security_score': 0,
    };
    
    // حساب نقاط الأمان
    int score = 0;
    if (audit['encryption_test'] == true) score += 25;
    if (audit['device_secured'] == true) score += 25;
    if (audit['session_valid'] == true) score += 25;
    if (audit['no_root_access'] == true) score += 25;
    
    audit['security_score'] = score;
    
    _logger.i('🔍 Security audit completed - Score: $score/100');
    return audit;
  }

  /// طوارئ - مسح جميع البيانات
  Future<void> emergencyDataWipe() async {
    try {
      _logger.w('🚨 EMERGENCY DATA WIPE INITIATED');
      
      await _encryptionService.clearAllEncryptionKeys();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      _logger.w('🚨 Emergency data wipe completed');
    } catch (e) {
      _logger.e('❌ Emergency wipe failed: $e');
    }
  }
}
