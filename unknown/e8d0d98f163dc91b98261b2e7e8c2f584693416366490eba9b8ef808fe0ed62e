import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'dart:convert';
import 'advanced_device_fingerprint.dart';
import 'fingerprint_matcher.dart';

/// خدمة المصادقة المحسنة للجهاز
/// تستخدم نظام البصمة الرقمية المتقدم بدلاً من Device ID البسيط
class EnhancedDeviceAuthService {
  final _logger = Logger();
  final _fingerprintEngine = AdvancedDeviceFingerprint();
  final _matcher = FingerprintMatcher();
  
  // مفاتيح التخزين
  static const String _deviceFingerprintKey = 'device_fingerprint_v2';
  static const String _deviceFingerprintDataKey = 'device_fingerprint_data_v2';
  static const String _lastAuthTimestampKey = 'last_auth_timestamp';
  static const String _authAttemptsKey = 'auth_attempts';
  static const String _deviceTrustLevelKey = 'device_trust_level';
  
  // إعدادات الأمان
  static const int _maxAuthAttempts = 5;
  static const Duration _authCooldownDuration = Duration(minutes: 15);


  /// الحصول على البصمة الرقمية الحالية للجهاز
  Future<DeviceAuthResult> getCurrentDeviceFingerprint() async {
    try {
      _logger.i('بدء الحصول على البصمة الرقمية الحالية...');
      
      // إنشاء البصمة الرقمية
      final result = await _fingerprintEngine.generateFingerprint();
      
      if (!result.isValid) {
        _logger.w('البصمة الرقمية غير صالحة: ${result.error}');
        return DeviceAuthResult(
          success: false,
          fingerprint: result.fingerprint.uniqueFingerprint ?? '',
          trustLevel: DeviceTrustLevel.untrusted,
          message: 'فشل في إنشاء بصمة رقمية صالحة',
          error: result.error,
        );
      }
      
      _logger.i('تم إنشاء البصمة الرقمية بنجاح - الثقة: ${result.fingerprint.confidenceScore}%');
      
      return DeviceAuthResult(
        success: true,
        fingerprint: result.fingerprint.uniqueFingerprint!,
        fingerprintData: result.fingerprint,
        trustLevel: _calculateTrustLevel(result.fingerprint.confidenceScore),
        message: 'تم إنشاء البصمة الرقمية بنجاح',
        confidenceScore: result.fingerprint.confidenceScore,
      );
      
    } catch (e) {
      _logger.e('خطأ في الحصول على البصمة الرقمية: $e');
      return DeviceAuthResult(
        success: false,
        fingerprint: '',
        trustLevel: DeviceTrustLevel.untrusted,
        message: 'خطأ في إنشاء البصمة الرقمية',
        error: e.toString(),
      );
    }
  }

  /// ربط الحساب بالجهاز الحالي
  Future<DeviceAuthResult> bindAccountToDevice(String accountId) async {
    try {
      _logger.i('بدء ربط الحساب $accountId بالجهاز...');
      
      // فحص محاولات المصادقة
      if (!await _canAttemptAuth()) {
        return DeviceAuthResult(
          success: false,
          fingerprint: '',
          trustLevel: DeviceTrustLevel.blocked,
          message: 'تم تجاوز الحد الأقصى لمحاولات المصادقة. حاول مرة أخرى لاحقاً.',
        );
      }
      
      // الحصول على البصمة الحالية
      final currentResult = await getCurrentDeviceFingerprint();
      if (!currentResult.success) {
        await _recordAuthAttempt(false);
        return currentResult;
      }
      
      // حفظ البصمة الجديدة
      await _saveDeviceFingerprint(currentResult.fingerprintData!, accountId);
      
      // تسجيل نجاح المصادقة
      await _recordAuthAttempt(true);
      await _updateTrustLevel(currentResult.trustLevel);
      
      _logger.i('تم ربط الحساب بالجهاز بنجاح');
      
      return DeviceAuthResult(
        success: true,
        fingerprint: currentResult.fingerprint,
        fingerprintData: currentResult.fingerprintData,
        trustLevel: currentResult.trustLevel,
        message: 'تم ربط الحساب بالجهاز بنجاح',
        confidenceScore: currentResult.confidenceScore,
        isNewDevice: true,
      );
      
    } catch (e) {
      _logger.e('خطأ في ربط الحساب بالجهاز: $e');
      await _recordAuthAttempt(false);
      return DeviceAuthResult(
        success: false,
        fingerprint: '',
        trustLevel: DeviceTrustLevel.untrusted,
        message: 'خطأ في ربط الحساب بالجهاز',
        error: e.toString(),
      );
    }
  }

  /// التحقق من الجهاز الحالي مقابل البصمة المحفوظة
  Future<DeviceAuthResult> verifyDevice(String accountId) async {
    try {
      _logger.i('بدء التحقق من الجهاز للحساب $accountId...');
      
      // فحص محاولات المصادقة
      if (!await _canAttemptAuth()) {
        return DeviceAuthResult(
          success: false,
          fingerprint: '',
          trustLevel: DeviceTrustLevel.blocked,
          message: 'تم تجاوز الحد الأقصى لمحاولات المصادقة. حاول مرة أخرى لاحقاً.',
        );
      }
      
      // الحصول على البصمة المحفوظة
      final storedFingerprint = await _getStoredDeviceFingerprint();
      if (storedFingerprint == null) {
        _logger.w('لا توجد بصمة محفوظة للجهاز');
        return DeviceAuthResult(
          success: false,
          fingerprint: '',
          trustLevel: DeviceTrustLevel.untrusted,
          message: 'الجهاز غير مسجل. يرجى ربط الحساب بالجهاز أولاً.',
          isNewDevice: true,
        );
      }
      
      // الحصول على البصمة الحالية
      final currentResult = await getCurrentDeviceFingerprint();
      if (!currentResult.success) {
        await _recordAuthAttempt(false);
        return currentResult;
      }
      
      // مقارنة البصمات
      final matchResult = _matcher.compareFingerprints(
        storedFingerprint,
        currentResult.fingerprintData!,
      );
      
      if (matchResult.isMatch) {
        // نجح التحقق
        await _recordAuthAttempt(true);
        await _updateStoredFingerprint(currentResult.fingerprintData!, accountId);
        
        final trustLevel = _determineTrustLevel(matchResult);
        await _updateTrustLevel(trustLevel);
        
        _logger.i('تم التحقق من الجهاز بنجاح - مستوى التطابق: ${matchResult.matchLevel}');
        
        return DeviceAuthResult(
          success: true,
          fingerprint: currentResult.fingerprint,
          fingerprintData: currentResult.fingerprintData,
          trustLevel: trustLevel,
          message: 'تم التحقق من الجهاز بنجاح',
          confidenceScore: matchResult.confidence * 100,
          matchResult: matchResult,
          isNewDevice: false,
        );
        
      } else {
        // فشل التحقق
        await _recordAuthAttempt(false);
        
        _logger.w('فشل التحقق من الجهاز - السبب: ${matchResult.reason}');
        
        return DeviceAuthResult(
          success: false,
          fingerprint: currentResult.fingerprint,
          fingerprintData: currentResult.fingerprintData,
          trustLevel: DeviceTrustLevel.suspicious,
          message: 'فشل التحقق من الجهاز. ${matchResult.reason}',
          confidenceScore: matchResult.confidence * 100,
          matchResult: matchResult,
          isNewDevice: true,
        );
      }
      
    } catch (e) {
      _logger.e('خطأ في التحقق من الجهاز: $e');
      await _recordAuthAttempt(false);
      return DeviceAuthResult(
        success: false,
        fingerprint: '',
        trustLevel: DeviceTrustLevel.untrusted,
        message: 'خطأ في التحقق من الجهاز',
        error: e.toString(),
      );
    }
  }

  /// إعادة تعيين بصمة الجهاز (للاستخدام الإداري)
  Future<bool> resetDeviceFingerprint() async {
    try {
      _logger.i('بدء إعادة تعيين بصمة الجهاز...');
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_deviceFingerprintKey);
      await prefs.remove(_deviceFingerprintDataKey);
      await prefs.remove(_lastAuthTimestampKey);
      await prefs.remove(_authAttemptsKey);
      await prefs.remove(_deviceTrustLevelKey);
      
      _logger.i('تم إعادة تعيين بصمة الجهاز بنجاح');
      return true;
      
    } catch (e) {
      _logger.e('خطأ في إعادة تعيين بصمة الجهاز: $e');
      return false;
    }
  }

  /// الحصول على معلومات الجهاز للعرض
  Future<DeviceInfo?> getDeviceInfo() async {
    try {
      final result = await getCurrentDeviceFingerprint();
      if (!result.success || result.fingerprintData == null) {
        return null;
      }
      
      final data = result.fingerprintData!;
      final trustLevel = await _getCurrentTrustLevel();
      
      return DeviceInfo(
        fingerprint: result.fingerprint,
        manufacturer: data.manufacturer ?? 'غير معروف',
        model: data.deviceModel ?? 'غير معروف',
        androidId: data.androidId ?? 'غير معروف',
        confidenceScore: data.confidenceScore,
        trustLevel: trustLevel,
        lastVerified: await _getLastAuthTimestamp(),
        createdAt: data.createdAt,
      );
      
    } catch (e) {
      _logger.e('خطأ في الحصول على معلومات الجهاز: $e');
      return null;
    }
  }

  // ==================== الطرق المساعدة ====================

  /// حفظ البصمة الرقمية
  Future<void> _saveDeviceFingerprint(DeviceFingerprintData fingerprint, String accountId) async {
    final prefs = await SharedPreferences.getInstance();
    
    // حفظ الهاش
    await prefs.setString(_deviceFingerprintKey, fingerprint.uniqueFingerprint!);
    
    // حفظ البيانات الكاملة
    final fingerprintMap = fingerprint.toMap();
    fingerprintMap['account_id'] = accountId;
    fingerprintMap['created_at'] = DateTime.now().toIso8601String();

    await prefs.setString(_deviceFingerprintDataKey, json.encode(fingerprintMap));
  }

  /// الحصول على البصمة المحفوظة
  Future<DeviceFingerprintData?> _getStoredDeviceFingerprint() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_deviceFingerprintDataKey);

      if (dataString == null) return null;

      // تحويل النص إلى Map باستخدام JSON
      final Map<String, dynamic> dataMap = json.decode(dataString);
      return DeviceFingerprintData.fromMap(dataMap);

    } catch (e) {
      _logger.e('خطأ في الحصول على البصمة المحفوظة: $e');
      return null;
    }
  }

  /// تحديث البصمة المحفوظة
  Future<void> _updateStoredFingerprint(DeviceFingerprintData fingerprint, String accountId) async {
    // تحديث البصمة إذا كان هناك تغييرات طفيفة مقبولة
    await _saveDeviceFingerprint(fingerprint, accountId);
  }

  /// فحص إمكانية محاولة المصادقة
  Future<bool> _canAttemptAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attempts = prefs.getInt(_authAttemptsKey) ?? 0;
      final lastAttemptString = prefs.getString(_lastAuthTimestampKey);
      
      if (attempts >= _maxAuthAttempts && lastAttemptString != null) {
        final lastAttempt = DateTime.parse(lastAttemptString);
        final timeSinceLastAttempt = DateTime.now().difference(lastAttempt);
        
        if (timeSinceLastAttempt < _authCooldownDuration) {
          return false;
        } else {
          // انتهت فترة التبريد - إعادة تعيين المحاولات
          await prefs.setInt(_authAttemptsKey, 0);
        }
      }
      
      return true;
      
    } catch (e) {
      _logger.e('خطأ في فحص محاولات المصادقة: $e');
      return true; // السماح بالمحاولة في حالة الخطأ
    }
  }

  /// تسجيل محاولة المصادقة
  Future<void> _recordAuthAttempt(bool success) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastAuthTimestampKey, DateTime.now().toIso8601String());
      
      if (success) {
        // إعادة تعيين المحاولات عند النجاح
        await prefs.setInt(_authAttemptsKey, 0);
      } else {
        // زيادة عدد المحاولات الفاشلة
        final attempts = prefs.getInt(_authAttemptsKey) ?? 0;
        await prefs.setInt(_authAttemptsKey, attempts + 1);
      }
      
    } catch (e) {
      _logger.e('خطأ في تسجيل محاولة المصادقة: $e');
    }
  }

  /// حساب مستوى الثقة
  DeviceTrustLevel _calculateTrustLevel(double confidenceScore) {
    if (confidenceScore >= 90.0) {
      return DeviceTrustLevel.high;
    } else if (confidenceScore >= 70.0) {
      return DeviceTrustLevel.medium;
    } else if (confidenceScore >= 50.0) {
      return DeviceTrustLevel.low;
    } else {
      return DeviceTrustLevel.untrusted;
    }
  }

  /// تحديد مستوى الثقة من نتيجة المقارنة
  DeviceTrustLevel _determineTrustLevel(FingerprintMatchResult matchResult) {
    switch (matchResult.matchLevel) {
      case MatchLevel.highConfidence:
        return DeviceTrustLevel.high;
      case MatchLevel.mediumConfidence:
        return DeviceTrustLevel.medium;
      case MatchLevel.lowConfidence:
        return DeviceTrustLevel.low;
      default:
        return DeviceTrustLevel.untrusted;
    }
  }

  /// تحديث مستوى الثقة
  Future<void> _updateTrustLevel(DeviceTrustLevel level) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_deviceTrustLevelKey, level.toString());
    } catch (e) {
      _logger.e('خطأ في تحديث مستوى الثقة: $e');
    }
  }

  /// الحصول على مستوى الثقة الحالي
  Future<DeviceTrustLevel> _getCurrentTrustLevel() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final levelString = prefs.getString(_deviceTrustLevelKey);
      
      if (levelString != null) {
        return DeviceTrustLevel.values.firstWhere(
          (level) => level.toString() == levelString,
          orElse: () => DeviceTrustLevel.untrusted,
        );
      }
      
      return DeviceTrustLevel.untrusted;
      
    } catch (e) {
      _logger.e('خطأ في الحصول على مستوى الثقة: $e');
      return DeviceTrustLevel.untrusted;
    }
  }

  /// الحصول على وقت آخر مصادقة
  Future<DateTime?> _getLastAuthTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampString = prefs.getString(_lastAuthTimestampKey);
      
      if (timestampString != null) {
        return DateTime.parse(timestampString);
      }
      
      return null;
      
    } catch (e) {
      _logger.e('خطأ في الحصول على وقت آخر مصادقة: $e');
      return null;
    }
  }
}

/// نتيجة عملية المصادقة
class DeviceAuthResult {
  final bool success;
  final String fingerprint;
  final DeviceFingerprintData? fingerprintData;
  final DeviceTrustLevel trustLevel;
  final String message;
  final double? confidenceScore;
  final FingerprintMatchResult? matchResult;
  final bool isNewDevice;
  final String? error;
  
  DeviceAuthResult({
    required this.success,
    required this.fingerprint,
    this.fingerprintData,
    required this.trustLevel,
    required this.message,
    this.confidenceScore,
    this.matchResult,
    this.isNewDevice = false,
    this.error,
  });
  
  @override
  String toString() {
    return 'DeviceAuthResult(success: $success, trustLevel: $trustLevel, confidence: ${confidenceScore?.toStringAsFixed(1)}%)';
  }
}

/// معلومات الجهاز للعرض
class DeviceInfo {
  final String fingerprint;
  final String manufacturer;
  final String model;
  final String androidId;
  final double confidenceScore;
  final DeviceTrustLevel trustLevel;
  final DateTime? lastVerified;
  final DateTime? createdAt;
  
  DeviceInfo({
    required this.fingerprint,
    required this.manufacturer,
    required this.model,
    required this.androidId,
    required this.confidenceScore,
    required this.trustLevel,
    this.lastVerified,
    this.createdAt,
  });
}

/// مستويات الثقة في الجهاز
enum DeviceTrustLevel {
  high,        // ثقة عالية - جهاز موثوق تماماً
  medium,      // ثقة متوسطة - جهاز موثوق نسبياً
  low,         // ثقة منخفضة - جهاز مشكوك فيه
  untrusted,   // غير موثوق - جهاز غير معروف
  suspicious,  // مشبوه - قد يكون جهاز مختلف
  blocked,     // محجوب - تم حجبه بسبب نشاط مشبوه
}
