-- إعداد سريع لقاعدة البيانات - Quick Database Setup
-- هذا الملف للاختبار السريع فقط - For quick testing only
-- Date: 2025-01-15

-- ===== إنشاء الجداول الأساسية =====

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الصور
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    file_name TEXT,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    location TEXT,
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الحقل المُحسوب بعد إنشاء الجدول
ALTER TABLE photos
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED;

-- جدول الفيديو
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    file_name TEXT,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    location TEXT,
    location_type TEXT CHECK (location_type IN ('U', 'C')),
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    duration_seconds INTEGER,
    file_size_mb DECIMAL(10,2),
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الحقل المُحسوب بعد إنشاء الجدول
ALTER TABLE videos
ADD COLUMN IF NOT EXISTS full_location_code TEXT GENERATED ALWAYS AS (location_type || location_number) STORED;

-- ===== تفعيل Row Level Security =====

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- ===== إنشاء السياسات =====

-- سياسات المستخدمين
DROP POLICY IF EXISTS users_policy ON users;
CREATE POLICY users_policy ON users
    FOR ALL
    USING (auth.uid() = id);

-- سياسات الصور
DROP POLICY IF EXISTS photos_policy ON photos;
CREATE POLICY photos_policy ON photos
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسات الفيديو
DROP POLICY IF EXISTS videos_policy ON videos;
CREATE POLICY videos_policy ON videos
    FOR ALL
    USING (auth.uid() = user_id);

-- ===== إنشاء الفهارس =====

CREATE INDEX IF NOT EXISTS idx_photos_user_id ON photos(user_id);
CREATE INDEX IF NOT EXISTS idx_photos_location_type ON photos(location_type);
CREATE INDEX IF NOT EXISTS idx_photos_location_number ON photos(location_number);
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON photos(location_type, location_number, capture_timestamp, username);

CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_location_type ON videos(location_type);
CREATE INDEX IF NOT EXISTS idx_videos_location_number ON videos(location_number);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON videos(location_type, location_number, capture_timestamp, username);

-- ===== دوال الترقيم التلقائي =====

-- دالة لتحديث ترقيم الفرز التلقائي للصور
CREATE OR REPLACE FUNCTION update_photo_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    NEW.sort_order := (
        SELECT COALESCE(MAX(sort_order), 0) + 1
        FROM photos 
        WHERE location_type = NEW.location_type 
        AND location_number = NEW.location_number
        AND user_id = NEW.user_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث ترقيم الفرز التلقائي للفيديو
CREATE OR REPLACE FUNCTION update_video_sort_order()
RETURNS TRIGGER AS $$
BEGIN
    NEW.sort_order := (
        SELECT COALESCE(MAX(sort_order), 0) + 1
        FROM videos 
        WHERE location_type = NEW.location_type 
        AND location_number = NEW.location_number
        AND user_id = NEW.user_id
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفزات للترقيم التلقائي
DROP TRIGGER IF EXISTS trigger_update_photo_sort_order ON photos;
CREATE TRIGGER trigger_update_photo_sort_order
    BEFORE INSERT ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_sort_order();

DROP TRIGGER IF EXISTS trigger_update_video_sort_order ON videos;
CREATE TRIGGER trigger_update_video_sort_order
    BEFORE INSERT ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_sort_order();

-- ===== إنشاء Storage Buckets =====

INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

-- ===== رسالة النجاح =====

SELECT 'تم إعداد قاعدة البيانات بنجاح! ✅' as status;
SELECT 'جميع الجداول والفهارس والدوال جاهزة للاستخدام' as message;

-- ===== اختبار سريع =====

-- عرض الجداول المُنشأة
SELECT 'الجداول المُنشأة:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'photos', 'videos')
ORDER BY table_name;
