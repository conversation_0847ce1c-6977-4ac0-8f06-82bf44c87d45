-- 🔍 تحليل صحة قاعدة البيانات الحالية
-- Database Health Analysis for Moon Memory System
-- Date: 2025-01-16

-- ===== 📊 فحص أحجام الجداول =====
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ===== 📈 إحصائيات الجداول =====
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- ===== 🔍 فحص الفهارس =====
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    idx_scan as times_used,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- ===== 🐌 الاستعلامات البطيئة =====
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    max_exec_time,
    stddev_exec_time,
    rows
FROM pg_stat_statements 
WHERE mean_exec_time > 100  -- أكثر من 100ms
ORDER BY mean_exec_time DESC
LIMIT 10;

-- ===== 🔒 فحص الأقفال =====
SELECT 
    pg_class.relname,
    pg_locks.locktype,
    pg_locks.mode,
    pg_locks.granted,
    pg_stat_activity.query,
    pg_stat_activity.query_start,
    pg_stat_activity.state
FROM pg_locks
JOIN pg_class ON pg_locks.relation = pg_class.oid
JOIN pg_stat_activity ON pg_locks.pid = pg_stat_activity.pid
WHERE NOT pg_locks.granted
ORDER BY pg_stat_activity.query_start;

-- ===== 📊 إحصائيات الاتصالات =====
SELECT 
    state,
    COUNT(*) as connection_count,
    MAX(now() - query_start) as longest_query_time,
    MAX(now() - state_change) as longest_idle_time
FROM pg_stat_activity 
WHERE pid != pg_backend_pid()
GROUP BY state
ORDER BY connection_count DESC;

-- ===== 🔍 فحص سلامة البيانات =====

-- فحص الصور اليتيمة (بدون مستخدم)
SELECT 'orphaned_photos' as check_type, COUNT(*) as issue_count
FROM photos p
LEFT JOIN users u ON p.username = u.username
WHERE u.username IS NULL AND p.status = 'active';

-- فحص الفيديوهات اليتيمة
SELECT 'orphaned_videos' as check_type, COUNT(*) as issue_count
FROM videos v
LEFT JOIN users u ON v.username = u.username
WHERE u.username IS NULL AND v.status = 'active';

-- فحص المواقع غير الصحيحة
SELECT 'invalid_locations' as check_type, COUNT(*) as issue_count
FROM (
    SELECT full_location_code FROM photos WHERE status = 'active'
    UNION
    SELECT full_location_code FROM videos WHERE status = 'active'
) media
LEFT JOIN locations l ON media.full_location_code = l.location_code
WHERE l.location_code IS NULL;

-- فحص الأجهزة المكررة
SELECT 'duplicate_devices' as check_type, 
       COUNT(*) - COUNT(DISTINCT device_fingerprint) as issue_count
FROM devices
WHERE is_active = true;

-- ===== 📈 تحليل نمو البيانات =====
WITH daily_growth AS (
    SELECT 
        DATE(capture_timestamp) as date,
        COUNT(*) as daily_photos
    FROM photos 
    WHERE capture_timestamp >= NOW() - INTERVAL '30 days'
    GROUP BY DATE(capture_timestamp)
    ORDER BY date
),
growth_trend AS (
    SELECT 
        date,
        daily_photos,
        LAG(daily_photos) OVER (ORDER BY date) as prev_day_photos,
        AVG(daily_photos) OVER (ORDER BY date ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) as week_avg
    FROM daily_growth
)
SELECT 
    date,
    daily_photos,
    prev_day_photos,
    CASE 
        WHEN prev_day_photos > 0 THEN 
            ROUND(((daily_photos - prev_day_photos)::DECIMAL / prev_day_photos * 100), 2)
        ELSE 0 
    END as growth_rate_percent,
    ROUND(week_avg, 1) as week_average
FROM growth_trend
WHERE prev_day_photos IS NOT NULL
ORDER BY date DESC
LIMIT 14;

-- ===== 🎯 تحليل استخدام المواقع =====
SELECT 
    l.location_code,
    l.location_name_ar,
    l.location_type,
    l.sort_order,
    COALESCE(photo_count, 0) as photos,
    COALESCE(video_count, 0) as videos,
    COALESCE(photo_count, 0) + COALESCE(video_count, 0) as total_files,
    l.last_used_at,
    CASE 
        WHEN l.last_used_at IS NULL THEN 'غير مستخدم'
        WHEN l.last_used_at < NOW() - INTERVAL '30 days' THEN 'قديم'
        WHEN l.last_used_at < NOW() - INTERVAL '7 days' THEN 'متوسط'
        ELSE 'نشط'
    END as usage_status
FROM locations l
LEFT JOIN (
    SELECT full_location_code, COUNT(*) as photo_count
    FROM photos 
    WHERE status = 'active'
    GROUP BY full_location_code
) p ON l.location_code = p.full_location_code
LEFT JOIN (
    SELECT full_location_code, COUNT(*) as video_count
    FROM videos 
    WHERE status = 'active'
    GROUP BY full_location_code
) v ON l.location_code = v.full_location_code
ORDER BY l.location_type, l.sort_order;

-- ===== 👥 تحليل نشاط المستخدمين =====
SELECT 
    u.username,
    u.full_name,
    u.created_at as user_since,
    COALESCE(photo_count, 0) as photos,
    COALESCE(video_count, 0) as videos,
    COALESCE(photo_count, 0) + COALESCE(video_count, 0) as total_uploads,
    COALESCE(total_size_mb, 0) as storage_used_mb,
    last_upload,
    CASE 
        WHEN last_upload IS NULL THEN 'لم يرفع ملفات'
        WHEN last_upload < NOW() - INTERVAL '30 days' THEN 'غير نشط'
        WHEN last_upload < NOW() - INTERVAL '7 days' THEN 'نشاط متوسط'
        ELSE 'نشط'
    END as activity_status
FROM users u
LEFT JOIN (
    SELECT 
        username,
        COUNT(*) as photo_count,
        SUM(file_size_bytes) / 1024 / 1024 as photo_size_mb,
        MAX(capture_timestamp) as last_photo
    FROM photos 
    WHERE status = 'active'
    GROUP BY username
) p ON u.username = p.username
LEFT JOIN (
    SELECT 
        username,
        COUNT(*) as video_count,
        SUM(file_size_bytes) / 1024 / 1024 as video_size_mb,
        MAX(capture_timestamp) as last_video
    FROM videos 
    WHERE status = 'active'
    GROUP BY username
) v ON u.username = v.username
CROSS JOIN LATERAL (
    SELECT 
        COALESCE(p.photo_size_mb, 0) + COALESCE(v.video_size_mb, 0) as total_size_mb,
        GREATEST(COALESCE(p.last_photo, '1970-01-01'), COALESCE(v.last_video, '1970-01-01')) as last_upload
) calc
ORDER BY total_uploads DESC, total_size_mb DESC;

-- ===== 🔧 توصيات التحسين =====
WITH recommendations AS (
    SELECT 'index_optimization' as category, 'إنشاء فهارس مركبة للترتيب' as recommendation, 'high' as priority
    UNION ALL
    SELECT 'data_cleanup', 'تنظيف البيانات المحذوفة القديمة', 'medium'
    UNION ALL
    SELECT 'location_data', 'إكمال بيانات جدول المواقع', 'high'
    UNION ALL
    SELECT 'query_optimization', 'تحسين استعلامات الترتيب', 'high'
    UNION ALL
    SELECT 'monitoring', 'إعداد مراقبة الأداء', 'medium'
    UNION ALL
    SELECT 'backup_strategy', 'تحسين استراتيجية النسخ الاحتياطية', 'low'
)
SELECT 
    category,
    recommendation,
    priority,
    CASE priority
        WHEN 'high' THEN '🔴 عالية'
        WHEN 'medium' THEN '🟡 متوسطة'
        WHEN 'low' THEN '🟢 منخفضة'
    END as priority_ar
FROM recommendations
ORDER BY 
    CASE priority
        WHEN 'high' THEN 1
        WHEN 'medium' THEN 2
        WHEN 'low' THEN 3
    END;

-- ===== 📊 ملخص الحالة العامة =====
SELECT 
    'database_health' as metric_category,
    'overall_status' as metric_name,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'
        ) < 50 
        AND (
            SELECT AVG(mean_exec_time) FROM pg_stat_statements WHERE calls > 10
        ) < 200
        THEN 'جيد'
        ELSE 'يحتاج تحسين'
    END as status,
    NOW() as check_time;
