-- 🔄 استعلامات الترتيب المتقدمة لنظام Moon Memory
-- Advanced Sorting Queries for Moon Memory System
-- Date: 2025-01-16
-- Version: 1.0

-- ===== 📊 دالة الترتيب الذكي للصور =====
CREATE OR REPLACE FUNCTION get_photos_sorted(
    p_sort_by TEXT DEFAULT 'location_date', -- location_date, date_location, user_date, size_desc, mixed
    p_location_type TEXT DEFAULT NULL, -- 'U', 'C', or NULL for all
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    file_name TEXT,
    storage_path TEXT,
    image_url TEXT,
    file_size_bytes BIGINT,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    sort_order INTEGER,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.file_name,
        p.storage_path,
        p.image_url,
        p.file_size_bytes,
        p.full_location_code as location_code,
        p.location_type,
        p.location_number,
        p.username,
        p.capture_timestamp,
        p.upload_timestamp,
        p.sort_order,
        COALESCE(l.sort_order, 999) as location_sort_order,
        ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
    FROM public.photos p
    LEFT JOIN public.locations l ON l.location_code = p.full_location_code
    WHERE 
        (p_location_type IS NULL OR p.location_type = p_location_type)
        AND (p_user_id IS NULL OR p.user_id = p_user_id)
        AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
        AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
        AND p.status = 'active'
    ORDER BY 
        CASE 
            -- ترتيب حسب الموقع ثم التاريخ
            WHEN p_sort_by = 'location_date' THEN 
                ROW(
                    p.location_type,
                    COALESCE(l.sort_order, 999),
                    p.capture_timestamp DESC
                )::TEXT
            
            -- ترتيب حسب التاريخ ثم الموقع
            WHEN p_sort_by = 'date_location' THEN 
                ROW(
                    p.capture_timestamp DESC,
                    p.location_type,
                    COALESCE(l.sort_order, 999)
                )::TEXT
            
            -- ترتيب حسب المستخدم ثم التاريخ
            WHEN p_sort_by = 'user_date' THEN 
                ROW(
                    p.username,
                    p.capture_timestamp DESC
                )::TEXT
            
            -- ترتيب حسب الحجم (الأكبر أولاً)
            WHEN p_sort_by = 'size_desc' THEN 
                ROW(
                    p.file_size_bytes DESC,
                    p.capture_timestamp DESC
                )::TEXT
            
            -- ترتيب مختلط متقدم
            WHEN p_sort_by = 'mixed' THEN 
                ROW(
                    p.location_type,
                    COALESCE(l.sort_order, 999),
                    DATE_TRUNC('day', p.capture_timestamp) DESC,
                    p.username,
                    p.capture_timestamp DESC
                )::TEXT
            
            -- الافتراضي: حسب الموقع والتاريخ
            ELSE 
                ROW(
                    p.location_type,
                    COALESCE(l.sort_order, 999),
                    p.capture_timestamp DESC
                )::TEXT
        END
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 🎥 دالة الترتيب الذكي للفيديوهات =====
CREATE OR REPLACE FUNCTION get_videos_sorted(
    p_sort_by TEXT DEFAULT 'location_date',
    p_location_type TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    file_name TEXT,
    storage_path TEXT,
    video_url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    sort_order INTEGER,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2),
    duration_formatted TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.id,
        v.file_name,
        v.storage_path,
        v.video_url,
        v.file_size_bytes,
        v.duration_seconds,
        v.full_location_code as location_code,
        v.location_type,
        v.location_number,
        v.username,
        v.capture_timestamp,
        v.upload_timestamp,
        v.sort_order,
        COALESCE(l.sort_order, 999) as location_sort_order,
        ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb,
        CASE 
            WHEN v.duration_seconds IS NULL THEN 'غير محدد'
            WHEN v.duration_seconds < 60 THEN v.duration_seconds || ' ثانية'
            WHEN v.duration_seconds < 3600 THEN 
                (v.duration_seconds / 60) || ':' || 
                LPAD((v.duration_seconds % 60)::TEXT, 2, '0') || ' دقيقة'
            ELSE 
                (v.duration_seconds / 3600) || ':' || 
                LPAD(((v.duration_seconds % 3600) / 60)::TEXT, 2, '0') || ':' ||
                LPAD((v.duration_seconds % 60)::TEXT, 2, '0') || ' ساعة'
        END as duration_formatted
    FROM public.videos v
    LEFT JOIN public.locations l ON l.location_code = v.full_location_code
    WHERE 
        (p_location_type IS NULL OR v.location_type = p_location_type)
        AND (p_user_id IS NULL OR v.user_id = p_user_id)
        AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
        AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
        AND v.status = 'active'
    ORDER BY 
        CASE 
            WHEN p_sort_by = 'location_date' THEN 
                ROW(
                    v.location_type,
                    COALESCE(l.sort_order, 999),
                    v.capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'date_location' THEN 
                ROW(
                    v.capture_timestamp DESC,
                    v.location_type,
                    COALESCE(l.sort_order, 999)
                )::TEXT
            WHEN p_sort_by = 'user_date' THEN 
                ROW(
                    v.username,
                    v.capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'size_desc' THEN 
                ROW(
                    v.file_size_bytes DESC,
                    v.capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'duration_desc' THEN 
                ROW(
                    v.duration_seconds DESC,
                    v.capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'mixed' THEN 
                ROW(
                    v.location_type,
                    COALESCE(l.sort_order, 999),
                    DATE_TRUNC('day', v.capture_timestamp) DESC,
                    v.username,
                    v.capture_timestamp DESC
                )::TEXT
            ELSE 
                ROW(
                    v.location_type,
                    COALESCE(l.sort_order, 999),
                    v.capture_timestamp DESC
                )::TEXT
        END
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة الترتيب المختلط للصور والفيديوهات =====
CREATE OR REPLACE FUNCTION get_media_mixed_sorted(
    p_sort_by TEXT DEFAULT 'location_date',
    p_location_type TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_media_type TEXT DEFAULT 'all', -- 'photos', 'videos', 'all'
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    media_type TEXT,
    file_name TEXT,
    storage_path TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH combined_media AS (
        -- الصور
        SELECT 
            p.id,
            'photo'::TEXT as media_type,
            p.file_name,
            p.storage_path,
            p.image_url as url,
            p.file_size_bytes,
            NULL::INTEGER as duration_seconds,
            p.full_location_code as location_code,
            p.location_type,
            p.location_number,
            p.username,
            p.capture_timestamp,
            p.upload_timestamp,
            COALESCE(l.sort_order, 999) as location_sort_order,
            ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
        FROM public.photos p
        LEFT JOIN public.locations l ON l.location_code = p.full_location_code
        WHERE 
            (p_media_type = 'all' OR p_media_type = 'photos')
            AND (p_location_type IS NULL OR p.location_type = p_location_type)
            AND (p_user_id IS NULL OR p.user_id = p_user_id)
            AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
            AND p.status = 'active'
        
        UNION ALL
        
        -- الفيديوهات
        SELECT 
            v.id,
            'video'::TEXT as media_type,
            v.file_name,
            v.storage_path,
            v.video_url as url,
            v.file_size_bytes,
            v.duration_seconds,
            v.full_location_code as location_code,
            v.location_type,
            v.location_number,
            v.username,
            v.capture_timestamp,
            v.upload_timestamp,
            COALESCE(l.sort_order, 999) as location_sort_order,
            ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
        FROM public.videos v
        LEFT JOIN public.locations l ON l.location_code = v.full_location_code
        WHERE 
            (p_media_type = 'all' OR p_media_type = 'videos')
            AND (p_location_type IS NULL OR v.location_type = p_location_type)
            AND (p_user_id IS NULL OR v.user_id = p_user_id)
            AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
            AND v.status = 'active'
    )
    SELECT * FROM combined_media
    ORDER BY 
        CASE 
            WHEN p_sort_by = 'location_date' THEN 
                ROW(
                    location_type,
                    location_sort_order,
                    capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'date_location' THEN 
                ROW(
                    capture_timestamp DESC,
                    location_type,
                    location_sort_order
                )::TEXT
            WHEN p_sort_by = 'user_date' THEN 
                ROW(
                    username,
                    capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'size_desc' THEN 
                ROW(
                    file_size_bytes DESC,
                    capture_timestamp DESC
                )::TEXT
            WHEN p_sort_by = 'type_location_date' THEN 
                ROW(
                    media_type,
                    location_type,
                    location_sort_order,
                    capture_timestamp DESC
                )::TEXT
            ELSE 
                ROW(
                    location_type,
                    location_sort_order,
                    capture_timestamp DESC
                )::TEXT
        END
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 📈 دالة إحصائيات المواقع =====
CREATE OR REPLACE FUNCTION get_location_statistics(
    p_location_type TEXT DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
    location_code TEXT,
    location_name_ar TEXT,
    location_type TEXT,
    sort_order INTEGER,
    total_photos INTEGER,
    total_videos INTEGER,
    total_files INTEGER,
    total_size_mb DECIMAL(10,2),
    avg_file_size_mb DECIMAL(10,2),
    last_used_at TIMESTAMP WITH TIME ZONE,
    unique_users INTEGER,
    most_active_user TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH location_stats AS (
        SELECT
            l.location_code,
            l.location_name_ar,
            l.location_type,
            l.sort_order,

            -- إحصائيات الصور
            COUNT(p.id) as photo_count,
            COALESCE(SUM(p.file_size_bytes), 0) as photos_size,

            -- إحصائيات الفيديوهات
            COUNT(v.id) as video_count,
            COALESCE(SUM(v.file_size_bytes), 0) as videos_size,

            -- آخر استخدام
            GREATEST(
                COALESCE(MAX(p.capture_timestamp), '1970-01-01'::timestamp),
                COALESCE(MAX(v.capture_timestamp), '1970-01-01'::timestamp)
            ) as last_activity,

            -- المستخدمون الفريدون
            COUNT(DISTINCT COALESCE(p.username, v.username)) as unique_user_count,

            -- أكثر المستخدمين نشاطاً
            MODE() WITHIN GROUP (ORDER BY COALESCE(p.username, v.username)) as top_user

        FROM public.locations l
        LEFT JOIN public.photos p ON p.full_location_code = l.location_code
            AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
            AND p.status = 'active'
        LEFT JOIN public.videos v ON v.full_location_code = l.location_code
            AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
            AND v.status = 'active'
        WHERE
            (p_location_type IS NULL OR l.location_type = p_location_type)
            AND l.is_active = true
        GROUP BY l.location_code, l.location_name_ar, l.location_type, l.sort_order
    )
    SELECT
        ls.location_code,
        ls.location_name_ar,
        ls.location_type,
        ls.sort_order,
        ls.photo_count::INTEGER as total_photos,
        ls.video_count::INTEGER as total_videos,
        (ls.photo_count + ls.video_count)::INTEGER as total_files,
        ROUND(((ls.photos_size + ls.videos_size)::DECIMAL / 1024 / 1024), 2) as total_size_mb,
        CASE
            WHEN (ls.photo_count + ls.video_count) > 0 THEN
                ROUND(((ls.photos_size + ls.videos_size)::DECIMAL / 1024 / 1024) / (ls.photo_count + ls.video_count), 2)
            ELSE 0
        END as avg_file_size_mb,
        CASE
            WHEN ls.last_activity = '1970-01-01'::timestamp THEN NULL
            ELSE ls.last_activity
        END as last_used_at,
        ls.unique_user_count::INTEGER as unique_users,
        ls.top_user as most_active_user
    FROM location_stats ls
    ORDER BY ls.location_type, ls.sort_order;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔍 دالة البحث المتقدم =====
CREATE OR REPLACE FUNCTION search_media_advanced(
    p_search_term TEXT DEFAULT NULL,
    p_location_codes TEXT[] DEFAULT NULL,
    p_usernames TEXT[] DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_file_size_min_mb DECIMAL DEFAULT NULL,
    p_file_size_max_mb DECIMAL DEFAULT NULL,
    p_media_type TEXT DEFAULT 'all', -- 'photos', 'videos', 'all'
    p_sort_by TEXT DEFAULT 'relevance', -- 'relevance', 'date_desc', 'date_asc', 'size_desc', 'location'
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    media_type TEXT,
    file_name TEXT,
    storage_path TEXT,
    url TEXT,
    file_size_mb DECIMAL(10,2),
    location_code TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    relevance_score DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH search_results AS (
        -- البحث في الصور
        SELECT
            p.id,
            'photo'::TEXT as media_type,
            p.file_name,
            p.storage_path,
            p.image_url as url,
            ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb,
            p.full_location_code as location_code,
            p.username,
            p.capture_timestamp,
            -- حساب نقاط الصلة
            CASE
                WHEN p_search_term IS NULL THEN 100.0
                ELSE (
                    CASE WHEN p.file_name ILIKE '%' || p_search_term || '%' THEN 50.0 ELSE 0.0 END +
                    CASE WHEN p.username ILIKE '%' || p_search_term || '%' THEN 30.0 ELSE 0.0 END +
                    CASE WHEN p.full_location_code ILIKE '%' || p_search_term || '%' THEN 20.0 ELSE 0.0 END
                )
            END as relevance_score
        FROM public.photos p
        WHERE
            (p_media_type = 'all' OR p_media_type = 'photos')
            AND p.status = 'active'
            AND (p_search_term IS NULL OR (
                p.file_name ILIKE '%' || p_search_term || '%' OR
                p.username ILIKE '%' || p_search_term || '%' OR
                p.full_location_code ILIKE '%' || p_search_term || '%'
            ))
            AND (p_location_codes IS NULL OR p.full_location_code = ANY(p_location_codes))
            AND (p_usernames IS NULL OR p.username = ANY(p_usernames))
            AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
            AND (p_file_size_min_mb IS NULL OR (p.file_size_bytes::DECIMAL / 1024 / 1024) >= p_file_size_min_mb)
            AND (p_file_size_max_mb IS NULL OR (p.file_size_bytes::DECIMAL / 1024 / 1024) <= p_file_size_max_mb)

        UNION ALL

        -- البحث في الفيديوهات
        SELECT
            v.id,
            'video'::TEXT as media_type,
            v.file_name,
            v.storage_path,
            v.video_url as url,
            ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb,
            v.full_location_code as location_code,
            v.username,
            v.capture_timestamp,
            -- حساب نقاط الصلة
            CASE
                WHEN p_search_term IS NULL THEN 100.0
                ELSE (
                    CASE WHEN v.file_name ILIKE '%' || p_search_term || '%' THEN 50.0 ELSE 0.0 END +
                    CASE WHEN v.username ILIKE '%' || p_search_term || '%' THEN 30.0 ELSE 0.0 END +
                    CASE WHEN v.full_location_code ILIKE '%' || p_search_term || '%' THEN 20.0 ELSE 0.0 END
                )
            END as relevance_score
        FROM public.videos v
        WHERE
            (p_media_type = 'all' OR p_media_type = 'videos')
            AND v.status = 'active'
            AND (p_search_term IS NULL OR (
                v.file_name ILIKE '%' || p_search_term || '%' OR
                v.username ILIKE '%' || p_search_term || '%' OR
                v.full_location_code ILIKE '%' || p_search_term || '%'
            ))
            AND (p_location_codes IS NULL OR v.full_location_code = ANY(p_location_codes))
            AND (p_usernames IS NULL OR v.username = ANY(p_usernames))
            AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
            AND (p_file_size_min_mb IS NULL OR (v.file_size_bytes::DECIMAL / 1024 / 1024) >= p_file_size_min_mb)
            AND (p_file_size_max_mb IS NULL OR (v.file_size_bytes::DECIMAL / 1024 / 1024) <= p_file_size_max_mb)
    )
    SELECT * FROM search_results
    WHERE relevance_score > 0
    ORDER BY
        CASE
            WHEN p_sort_by = 'relevance' THEN ROW(relevance_score DESC, capture_timestamp DESC)::TEXT
            WHEN p_sort_by = 'date_desc' THEN ROW(capture_timestamp DESC, relevance_score DESC)::TEXT
            WHEN p_sort_by = 'date_asc' THEN ROW(capture_timestamp ASC, relevance_score DESC)::TEXT
            WHEN p_sort_by = 'size_desc' THEN ROW(file_size_mb DESC, capture_timestamp DESC)::TEXT
            WHEN p_sort_by = 'location' THEN ROW(location_code, capture_timestamp DESC)::TEXT
            ELSE ROW(relevance_score DESC, capture_timestamp DESC)::TEXT
        END
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة إحصائيات لوحة المراقبة =====
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS TABLE (
    total_users INTEGER,
    active_users INTEGER,
    total_devices INTEGER,
    trusted_devices INTEGER,
    total_photos INTEGER,
    total_videos INTEGER,
    total_storage_gb DECIMAL(10,2),
    u_locations_used INTEGER,
    c_locations_used INTEGER,
    photos_today INTEGER,
    videos_today INTEGER,
    top_location TEXT,
    top_user TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH stats AS (
        SELECT
            -- إحصائيات المستخدمين
            (SELECT COUNT(*) FROM public.users WHERE is_active = true) as user_count,
            (SELECT COUNT(*) FROM public.users WHERE is_active = true AND last_login > NOW() - INTERVAL '30 days') as active_user_count,

            -- إحصائيات الأجهزة
            (SELECT COUNT(*) FROM public.devices WHERE is_active = true) as device_count,
            (SELECT COUNT(*) FROM public.devices WHERE is_active = true AND trust_level IN ('trusted', 'verified')) as trusted_device_count,

            -- إحصائيات الملفات
            (SELECT COUNT(*) FROM public.photos WHERE status = 'active') as photo_count,
            (SELECT COUNT(*) FROM public.videos WHERE status = 'active') as video_count,

            -- إحصائيات التخزين
            (SELECT COALESCE(SUM(file_size_bytes), 0) FROM public.photos WHERE status = 'active') +
            (SELECT COALESCE(SUM(file_size_bytes), 0) FROM public.videos WHERE status = 'active') as total_storage_bytes,

            -- إحصائيات المواقع
            (SELECT COUNT(DISTINCT full_location_code) FROM public.photos WHERE location_type = 'U' AND status = 'active') as u_loc_count,
            (SELECT COUNT(DISTINCT full_location_code) FROM public.videos WHERE location_type = 'C' AND status = 'active') as c_loc_count,

            -- إحصائيات اليوم
            (SELECT COUNT(*) FROM public.photos WHERE DATE(capture_timestamp) = CURRENT_DATE AND status = 'active') as photos_today_count,
            (SELECT COUNT(*) FROM public.videos WHERE DATE(capture_timestamp) = CURRENT_DATE AND status = 'active') as videos_today_count,

            -- أكثر المواقع استخداماً
            (SELECT full_location_code FROM (
                SELECT full_location_code, COUNT(*) as usage_count
                FROM (
                    SELECT full_location_code FROM public.photos WHERE status = 'active'
                    UNION ALL
                    SELECT full_location_code FROM public.videos WHERE status = 'active'
                ) combined
                GROUP BY full_location_code
                ORDER BY usage_count DESC
                LIMIT 1
            ) top_loc) as most_used_location,

            -- أكثر المستخدمين نشاطاً
            (SELECT username FROM (
                SELECT username, COUNT(*) as activity_count
                FROM (
                    SELECT username FROM public.photos WHERE status = 'active'
                    UNION ALL
                    SELECT username FROM public.videos WHERE status = 'active'
                ) combined
                GROUP BY username
                ORDER BY activity_count DESC
                LIMIT 1
            ) top_usr) as most_active_user
    )
    SELECT
        s.user_count::INTEGER,
        s.active_user_count::INTEGER,
        s.device_count::INTEGER,
        s.trusted_device_count::INTEGER,
        s.photo_count::INTEGER,
        s.video_count::INTEGER,
        ROUND((s.total_storage_bytes::DECIMAL / 1024 / 1024 / 1024), 2) as total_storage_gb,
        s.u_loc_count::INTEGER,
        s.c_loc_count::INTEGER,
        s.photos_today_count::INTEGER,
        s.videos_today_count::INTEGER,
        s.most_used_location,
        s.most_active_user
    FROM stats s;
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة تحليل الاتجاهات الزمنية =====
CREATE OR REPLACE FUNCTION analyze_usage_trends(
    p_period TEXT DEFAULT 'monthly', -- 'daily', 'weekly', 'monthly', 'yearly'
    p_months_back INTEGER DEFAULT 12
)
RETURNS TABLE (
    period_label TEXT,
    period_start DATE,
    period_end DATE,
    total_uploads INTEGER,
    photos_count INTEGER,
    videos_count INTEGER,
    unique_users INTEGER,
    unique_locations INTEGER,
    avg_file_size_mb DECIMAL(10,2),
    growth_rate DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH period_data AS (
        SELECT
            CASE
                WHEN p_period = 'daily' THEN TO_CHAR(capture_timestamp, 'YYYY-MM-DD')
                WHEN p_period = 'weekly' THEN TO_CHAR(capture_timestamp, 'YYYY-"W"WW')
                WHEN p_period = 'monthly' THEN TO_CHAR(capture_timestamp, 'YYYY-MM')
                WHEN p_period = 'yearly' THEN TO_CHAR(capture_timestamp, 'YYYY')
                ELSE TO_CHAR(capture_timestamp, 'YYYY-MM')
            END as period_key,

            CASE
                WHEN p_period = 'daily' THEN DATE_TRUNC('day', capture_timestamp)::DATE
                WHEN p_period = 'weekly' THEN DATE_TRUNC('week', capture_timestamp)::DATE
                WHEN p_period = 'monthly' THEN DATE_TRUNC('month', capture_timestamp)::DATE
                WHEN p_period = 'yearly' THEN DATE_TRUNC('year', capture_timestamp)::DATE
                ELSE DATE_TRUNC('month', capture_timestamp)::DATE
            END as period_start_date,

            'photo' as media_type,
            username,
            full_location_code,
            file_size_bytes
        FROM public.photos
        WHERE status = 'active'
          AND capture_timestamp >= NOW() - INTERVAL '1 month' * p_months_back

        UNION ALL

        SELECT
            CASE
                WHEN p_period = 'daily' THEN TO_CHAR(capture_timestamp, 'YYYY-MM-DD')
                WHEN p_period = 'weekly' THEN TO_CHAR(capture_timestamp, 'YYYY-"W"WW')
                WHEN p_period = 'monthly' THEN TO_CHAR(capture_timestamp, 'YYYY-MM')
                WHEN p_period = 'yearly' THEN TO_CHAR(capture_timestamp, 'YYYY')
                ELSE TO_CHAR(capture_timestamp, 'YYYY-MM')
            END as period_key,

            CASE
                WHEN p_period = 'daily' THEN DATE_TRUNC('day', capture_timestamp)::DATE
                WHEN p_period = 'weekly' THEN DATE_TRUNC('week', capture_timestamp)::DATE
                WHEN p_period = 'monthly' THEN DATE_TRUNC('month', capture_timestamp)::DATE
                WHEN p_period = 'yearly' THEN DATE_TRUNC('year', capture_timestamp)::DATE
                ELSE DATE_TRUNC('month', capture_timestamp)::DATE
            END as period_start_date,

            'video' as media_type,
            username,
            full_location_code,
            file_size_bytes
        FROM public.videos
        WHERE status = 'active'
          AND capture_timestamp >= NOW() - INTERVAL '1 month' * p_months_back
    ),
    aggregated_data AS (
        SELECT
            period_key,
            period_start_date,
            COUNT(*) as total_count,
            COUNT(CASE WHEN media_type = 'photo' THEN 1 END) as photos,
            COUNT(CASE WHEN media_type = 'video' THEN 1 END) as videos,
            COUNT(DISTINCT username) as users,
            COUNT(DISTINCT full_location_code) as locations,
            AVG(file_size_bytes::DECIMAL / 1024 / 1024) as avg_size_mb
        FROM period_data
        GROUP BY period_key, period_start_date
        ORDER BY period_start_date
    ),
    with_growth AS (
        SELECT
            *,
            LAG(total_count) OVER (ORDER BY period_start_date) as prev_count
        FROM aggregated_data
    )
    SELECT
        period_key,
        period_start_date,
        CASE
            WHEN p_period = 'daily' THEN period_start_date
            WHEN p_period = 'weekly' THEN period_start_date + INTERVAL '6 days'
            WHEN p_period = 'monthly' THEN (period_start_date + INTERVAL '1 month' - INTERVAL '1 day')::DATE
            WHEN p_period = 'yearly' THEN (period_start_date + INTERVAL '1 year' - INTERVAL '1 day')::DATE
            ELSE (period_start_date + INTERVAL '1 month' - INTERVAL '1 day')::DATE
        END as period_end_date,
        total_count::INTEGER,
        photos::INTEGER,
        videos::INTEGER,
        users::INTEGER,
        locations::INTEGER,
        ROUND(avg_size_mb, 2),
        CASE
            WHEN prev_count IS NULL OR prev_count = 0 THEN 0.0
            ELSE ROUND(((total_count - prev_count)::DECIMAL / prev_count * 100), 2)
        END as growth_percentage
    FROM with_growth
    ORDER BY period_start_date;
END;
$$ LANGUAGE plpgsql;

-- ===== 🏆 دالة أكثر المستخدمين نشاطاً =====
CREATE OR REPLACE FUNCTION get_top_users(
    p_limit INTEGER DEFAULT 10,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_location_type TEXT DEFAULT NULL
)
RETURNS TABLE (
    username TEXT,
    total_uploads INTEGER,
    photos_count INTEGER,
    videos_count INTEGER,
    total_size_mb DECIMAL(10,2),
    unique_locations INTEGER,
    first_upload TIMESTAMP WITH TIME ZONE,
    last_upload TIMESTAMP WITH TIME ZONE,
    avg_uploads_per_day DECIMAL(8,2),
    favorite_location TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH user_stats AS (
        SELECT
            u.username,
            COUNT(*) as uploads,
            COUNT(CASE WHEN u.media_type = 'photo' THEN 1 END) as photos,
            COUNT(CASE WHEN u.media_type = 'video' THEN 1 END) as videos,
            SUM(u.file_size_bytes) as total_bytes,
            COUNT(DISTINCT u.full_location_code) as locations,
            MIN(u.capture_timestamp) as first_ts,
            MAX(u.capture_timestamp) as last_ts,
            MODE() WITHIN GROUP (ORDER BY u.full_location_code) as top_location
        FROM (
            SELECT username, 'photo' as media_type, file_size_bytes, full_location_code, capture_timestamp
            FROM public.photos
            WHERE status = 'active'
              AND (p_date_from IS NULL OR capture_timestamp >= p_date_from)
              AND (p_date_to IS NULL OR capture_timestamp <= p_date_to)
              AND (p_location_type IS NULL OR LEFT(full_location_code, 1) = p_location_type)

            UNION ALL

            SELECT username, 'video' as media_type, file_size_bytes, full_location_code, capture_timestamp
            FROM public.videos
            WHERE status = 'active'
              AND (p_date_from IS NULL OR capture_timestamp >= p_date_from)
              AND (p_date_to IS NULL OR capture_timestamp <= p_date_to)
              AND (p_location_type IS NULL OR LEFT(full_location_code, 1) = p_location_type)
        ) u
        GROUP BY u.username
    )
    SELECT
        us.username,
        us.uploads::INTEGER,
        us.photos::INTEGER,
        us.videos::INTEGER,
        ROUND((us.total_bytes::DECIMAL / 1024 / 1024), 2),
        us.locations::INTEGER,
        us.first_ts,
        us.last_ts,
        CASE
            WHEN us.first_ts = us.last_ts THEN us.uploads::DECIMAL
            ELSE ROUND(us.uploads::DECIMAL / GREATEST(1, EXTRACT(DAYS FROM (us.last_ts - us.first_ts))), 2)
        END,
        us.top_location
    FROM user_stats us
    ORDER BY us.uploads DESC, us.total_bytes DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ===== 📍 دالة تحليل استخدام المواقع =====
CREATE OR REPLACE FUNCTION analyze_location_usage(
    p_location_type TEXT DEFAULT NULL, -- 'U' or 'C'
    p_time_period TEXT DEFAULT 'all_time' -- 'today', 'week', 'month', 'year', 'all_time'
)
RETURNS TABLE (
    location_code TEXT,
    location_name_ar TEXT,
    location_type TEXT,
    total_files INTEGER,
    photos_count INTEGER,
    videos_count INTEGER,
    unique_users INTEGER,
    total_size_mb DECIMAL(10,2),
    usage_percentage DECIMAL(5,2),
    avg_files_per_user DECIMAL(8,2),
    peak_usage_hour INTEGER,
    last_activity TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    date_filter TIMESTAMP WITH TIME ZONE;
BEGIN
    -- تحديد فلتر التاريخ
    CASE p_time_period
        WHEN 'today' THEN date_filter := CURRENT_DATE;
        WHEN 'week' THEN date_filter := DATE_TRUNC('week', NOW());
        WHEN 'month' THEN date_filter := DATE_TRUNC('month', NOW());
        WHEN 'year' THEN date_filter := DATE_TRUNC('year', NOW());
        ELSE date_filter := '1900-01-01'::TIMESTAMP WITH TIME ZONE;
    END CASE;

    RETURN QUERY
    WITH location_data AS (
        SELECT
            full_location_code,
            'photo' as media_type,
            username,
            file_size_bytes,
            capture_timestamp,
            EXTRACT(HOUR FROM capture_timestamp) as upload_hour
        FROM public.photos
        WHERE status = 'active'
          AND (p_location_type IS NULL OR LEFT(full_location_code, 1) = p_location_type)
          AND capture_timestamp >= date_filter

        UNION ALL

        SELECT
            full_location_code,
            'video' as media_type,
            username,
            file_size_bytes,
            capture_timestamp,
            EXTRACT(HOUR FROM capture_timestamp) as upload_hour
        FROM public.videos
        WHERE status = 'active'
          AND (p_location_type IS NULL OR LEFT(full_location_code, 1) = p_location_type)
          AND capture_timestamp >= date_filter
    ),
    location_stats AS (
        SELECT
            ld.full_location_code,
            COUNT(*) as file_count,
            COUNT(CASE WHEN ld.media_type = 'photo' THEN 1 END) as photo_count,
            COUNT(CASE WHEN ld.media_type = 'video' THEN 1 END) as video_count,
            COUNT(DISTINCT ld.username) as user_count,
            SUM(ld.file_size_bytes) as total_bytes,
            MAX(ld.capture_timestamp) as last_used,
            MODE() WITHIN GROUP (ORDER BY ld.upload_hour) as peak_hour
        FROM location_data ld
        GROUP BY ld.full_location_code
    ),
    total_files AS (
        SELECT SUM(file_count) as grand_total FROM location_stats
    )
    SELECT
        ls.full_location_code,
        COALESCE(l.location_name_ar, 'غير محدد') as location_name,
        LEFT(ls.full_location_code, 1) as loc_type,
        ls.file_count::INTEGER,
        ls.photo_count::INTEGER,
        ls.video_count::INTEGER,
        ls.user_count::INTEGER,
        ROUND((ls.total_bytes::DECIMAL / 1024 / 1024), 2),
        ROUND((ls.file_count::DECIMAL / GREATEST(tf.grand_total, 1) * 100), 2),
        ROUND((ls.file_count::DECIMAL / GREATEST(ls.user_count, 1)), 2),
        ls.peak_hour::INTEGER,
        ls.last_used
    FROM location_stats ls
    CROSS JOIN total_files tf
    LEFT JOIN public.locations l ON l.location_code = ls.full_location_code
    ORDER BY ls.file_count DESC, ls.total_bytes DESC;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔍 دالة البحث الذكي مع التقييم =====
CREATE OR REPLACE FUNCTION smart_search_with_ranking(
    p_search_query TEXT,
    p_search_type TEXT DEFAULT 'all', -- 'filename', 'username', 'location', 'all'
    p_media_type TEXT DEFAULT 'all', -- 'photos', 'videos', 'all'
    p_limit INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    media_type TEXT,
    file_name TEXT,
    username TEXT,
    location_code TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    file_size_mb DECIMAL(10,2),
    relevance_score DECIMAL(8,2),
    match_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH search_results AS (
        -- البحث في الصور
        SELECT
            p.id,
            'photo'::TEXT as type,
            p.file_name,
            p.username,
            p.full_location_code,
            p.capture_timestamp,
            ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as size_mb,

            -- حساب نقاط الصلة المتقدم
            (
                CASE
                    WHEN p_search_type IN ('filename', 'all') AND p.file_name ILIKE '%' || p_search_query || '%' THEN
                        CASE
                            WHEN p.file_name ILIKE p_search_query || '%' THEN 100.0  -- يبدأ بالكلمة
                            WHEN p.file_name ILIKE '%' || p_search_query THEN 80.0   -- ينتهي بالكلمة
                            ELSE 60.0  -- يحتوي على الكلمة
                        END
                    ELSE 0.0
                END +
                CASE
                    WHEN p_search_type IN ('username', 'all') AND p.username ILIKE '%' || p_search_query || '%' THEN
                        CASE
                            WHEN p.username = p_search_query THEN 90.0  -- تطابق تام
                            WHEN p.username ILIKE p_search_query || '%' THEN 70.0  -- يبدأ بالكلمة
                            ELSE 50.0  -- يحتوي على الكلمة
                        END
                    ELSE 0.0
                END +
                CASE
                    WHEN p_search_type IN ('location', 'all') AND p.full_location_code ILIKE '%' || p_search_query || '%' THEN 40.0
                    ELSE 0.0
                END
            ) as relevance,

            -- نوع التطابق
            CASE
                WHEN p.file_name ILIKE '%' || p_search_query || '%' THEN 'filename'
                WHEN p.username ILIKE '%' || p_search_query || '%' THEN 'username'
                WHEN p.full_location_code ILIKE '%' || p_search_query || '%' THEN 'location'
                ELSE 'other'
            END as match_category

        FROM public.photos p
        WHERE (p_media_type = 'all' OR p_media_type = 'photos')
          AND p.status = 'active'
          AND (
              (p_search_type IN ('filename', 'all') AND p.file_name ILIKE '%' || p_search_query || '%') OR
              (p_search_type IN ('username', 'all') AND p.username ILIKE '%' || p_search_query || '%') OR
              (p_search_type IN ('location', 'all') AND p.full_location_code ILIKE '%' || p_search_query || '%')
          )

        UNION ALL

        -- البحث في الفيديوهات
        SELECT
            v.id,
            'video'::TEXT as type,
            v.file_name,
            v.username,
            v.full_location_code,
            v.capture_timestamp,
            ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as size_mb,

            -- حساب نقاط الصلة المتقدم
            (
                CASE
                    WHEN p_search_type IN ('filename', 'all') AND v.file_name ILIKE '%' || p_search_query || '%' THEN
                        CASE
                            WHEN v.file_name ILIKE p_search_query || '%' THEN 100.0
                            WHEN v.file_name ILIKE '%' || p_search_query THEN 80.0
                            ELSE 60.0
                        END
                    ELSE 0.0
                END +
                CASE
                    WHEN p_search_type IN ('username', 'all') AND v.username ILIKE '%' || p_search_query || '%' THEN
                        CASE
                            WHEN v.username = p_search_query THEN 90.0
                            WHEN v.username ILIKE p_search_query || '%' THEN 70.0
                            ELSE 50.0
                        END
                    ELSE 0.0
                END +
                CASE
                    WHEN p_search_type IN ('location', 'all') AND v.full_location_code ILIKE '%' || p_search_query || '%' THEN 40.0
                    ELSE 0.0
                END
            ) as relevance,

            -- نوع التطابق
            CASE
                WHEN v.file_name ILIKE '%' || p_search_query || '%' THEN 'filename'
                WHEN v.username ILIKE '%' || p_search_query || '%' THEN 'username'
                WHEN v.full_location_code ILIKE '%' || p_search_query || '%' THEN 'location'
                ELSE 'other'
            END as match_category

        FROM public.videos v
        WHERE (p_media_type = 'all' OR p_media_type = 'videos')
          AND v.status = 'active'
          AND (
              (p_search_type IN ('filename', 'all') AND v.file_name ILIKE '%' || p_search_query || '%') OR
              (p_search_type IN ('username', 'all') AND v.username ILIKE '%' || p_search_query || '%') OR
              (p_search_type IN ('location', 'all') AND v.full_location_code ILIKE '%' || p_search_query || '%')
          )
    )
    SELECT * FROM search_results
    WHERE relevance > 0
    ORDER BY relevance DESC, capture_timestamp DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- ===== 🔧 دوال الصيانة والتحسين =====

-- دالة تحسين الأداء وإعادة بناء الفهارس
CREATE OR REPLACE FUNCTION optimize_database()
RETURNS TABLE (
    operation TEXT,
    table_name TEXT,
    status TEXT,
    execution_time_ms INTEGER
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    tables_to_optimize TEXT[] := ARRAY['photos', 'videos', 'users', 'devices', 'locations', 'user_sessions'];
    table_name TEXT;
BEGIN
    -- تحليل وتحسين الجداول
    FOREACH table_name IN ARRAY tables_to_optimize
    LOOP
        start_time := clock_timestamp();

        EXECUTE format('ANALYZE %I', table_name);

        end_time := clock_timestamp();

        RETURN QUERY SELECT
            'ANALYZE'::TEXT,
            table_name,
            'SUCCESS'::TEXT,
            EXTRACT(MILLISECONDS FROM (end_time - start_time))::INTEGER;
    END LOOP;

    -- إعادة بناء الفهارس
    start_time := clock_timestamp();
    REINDEX DATABASE CURRENT_DATABASE;
    end_time := clock_timestamp();

    RETURN QUERY SELECT
        'REINDEX'::TEXT,
        'ALL_TABLES'::TEXT,
        'SUCCESS'::TEXT,
        EXTRACT(MILLISECONDS FROM (end_time - start_time))::INTEGER;

    -- تنظيف الجداول
    start_time := clock_timestamp();
    VACUUM ANALYZE;
    end_time := clock_timestamp();

    RETURN QUERY SELECT
        'VACUUM'::TEXT,
        'ALL_TABLES'::TEXT,
        'SUCCESS'::TEXT,
        EXTRACT(MILLISECONDS FROM (end_time - start_time))::INTEGER;

EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT
            'ERROR'::TEXT,
            COALESCE(table_name, 'UNKNOWN')::TEXT,
            SQLERRM::TEXT,
            0::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف البيانات المحذوفة
CREATE OR REPLACE FUNCTION cleanup_deleted_records()
RETURNS TABLE (
    table_name TEXT,
    deleted_count INTEGER,
    cleanup_date TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    photos_deleted INTEGER;
    videos_deleted INTEGER;
    sessions_deleted INTEGER;
BEGIN
    -- حذف الصور المحذوفة نهائياً (أقدم من 30 يوم)
    DELETE FROM public.photos
    WHERE status = 'deleted'
      AND updated_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS photos_deleted = ROW_COUNT;

    -- حذف الفيديوهات المحذوفة نهائياً (أقدم من 30 يوم)
    DELETE FROM public.videos
    WHERE status = 'deleted'
      AND updated_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS videos_deleted = ROW_COUNT;

    -- حذف الجلسات المنتهية الصلاحية (أقدم من 7 أيام)
    DELETE FROM public.user_sessions
    WHERE expires_at < NOW() - INTERVAL '7 days';
    GET DIAGNOSTICS sessions_deleted = ROW_COUNT;

    -- إرجاع النتائج
    RETURN QUERY VALUES
        ('photos'::TEXT, photos_deleted, NOW()),
        ('videos'::TEXT, videos_deleted, NOW()),
        ('user_sessions'::TEXT, sessions_deleted, NOW());
END;
$$ LANGUAGE plpgsql;

-- دالة فحص سلامة البيانات
CREATE OR REPLACE FUNCTION check_data_integrity()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    issue_count INTEGER,
    description TEXT
) AS $$
DECLARE
    orphaned_photos INTEGER;
    orphaned_videos INTEGER;
    invalid_locations INTEGER;
    duplicate_devices INTEGER;
    inactive_users_with_files INTEGER;
BEGIN
    -- فحص الصور اليتيمة (بدون مستخدم)
    SELECT COUNT(*) INTO orphaned_photos
    FROM public.photos p
    LEFT JOIN public.users u ON p.username = u.username
    WHERE u.username IS NULL AND p.status = 'active';

    -- فحص الفيديوهات اليتيمة
    SELECT COUNT(*) INTO orphaned_videos
    FROM public.videos v
    LEFT JOIN public.users u ON v.username = u.username
    WHERE u.username IS NULL AND v.status = 'active';

    -- فحص المواقع غير الصحيحة
    SELECT COUNT(*) INTO invalid_locations
    FROM (
        SELECT full_location_code FROM public.photos WHERE status = 'active'
        UNION
        SELECT full_location_code FROM public.videos WHERE status = 'active'
    ) media
    LEFT JOIN public.locations l ON media.full_location_code = l.location_code
    WHERE l.location_code IS NULL;

    -- فحص الأجهزة المكررة
    SELECT COUNT(*) - COUNT(DISTINCT device_fingerprint) INTO duplicate_devices
    FROM public.devices
    WHERE is_active = true;

    -- فحص المستخدمين غير النشطين مع ملفات
    SELECT COUNT(DISTINCT u.username) INTO inactive_users_with_files
    FROM public.users u
    WHERE u.is_active = false
      AND EXISTS (
          SELECT 1 FROM public.photos p WHERE p.username = u.username AND p.status = 'active'
          UNION
          SELECT 1 FROM public.videos v WHERE v.username = u.username AND v.status = 'active'
      );

    -- إرجاع النتائج
    RETURN QUERY VALUES
        ('orphaned_photos'::TEXT,
         CASE WHEN orphaned_photos = 0 THEN 'OK' ELSE 'WARNING' END,
         orphaned_photos,
         'صور بدون مستخدم مرتبط'::TEXT),
        ('orphaned_videos'::TEXT,
         CASE WHEN orphaned_videos = 0 THEN 'OK' ELSE 'WARNING' END,
         orphaned_videos,
         'فيديوهات بدون مستخدم مرتبط'::TEXT),
        ('invalid_locations'::TEXT,
         CASE WHEN invalid_locations = 0 THEN 'OK' ELSE 'ERROR' END,
         invalid_locations,
         'ملفات بمواقع غير صحيحة'::TEXT),
        ('duplicate_devices'::TEXT,
         CASE WHEN duplicate_devices = 0 THEN 'OK' ELSE 'WARNING' END,
         duplicate_devices,
         'أجهزة مكررة'::TEXT),
        ('inactive_users_with_files'::TEXT,
         CASE WHEN inactive_users_with_files = 0 THEN 'OK' ELSE 'INFO' END,
         inactive_users_with_files,
         'مستخدمين غير نشطين لديهم ملفات'::TEXT);
END;
$$ LANGUAGE plpgsql;

-- دالة إحصائيات الأداء
CREATE OR REPLACE FUNCTION get_performance_stats()
RETURNS TABLE (
    metric_name TEXT,
    metric_value TEXT,
    metric_unit TEXT,
    status TEXT
) AS $$
DECLARE
    db_size BIGINT;
    photos_table_size BIGINT;
    videos_table_size BIGINT;
    total_connections INTEGER;
    avg_query_time DECIMAL;
BEGIN
    -- حجم قاعدة البيانات
    SELECT pg_database_size(current_database()) INTO db_size;

    -- حجم جدول الصور
    SELECT pg_total_relation_size('public.photos') INTO photos_table_size;

    -- حجم جدول الفيديوهات
    SELECT pg_total_relation_size('public.videos') INTO videos_table_size;

    -- عدد الاتصالات النشطة
    SELECT COUNT(*) INTO total_connections
    FROM pg_stat_activity
    WHERE state = 'active';

    -- متوسط وقت الاستعلام (تقديري)
    SELECT COALESCE(AVG(mean_exec_time), 0) INTO avg_query_time
    FROM pg_stat_statements
    WHERE calls > 10;

    -- إرجاع النتائج
    RETURN QUERY VALUES
        ('database_size'::TEXT,
         pg_size_pretty(db_size),
         'bytes'::TEXT,
         CASE WHEN db_size < 1073741824 THEN 'OK' ELSE 'WARNING' END), -- 1GB
        ('photos_table_size'::TEXT,
         pg_size_pretty(photos_table_size),
         'bytes'::TEXT,
         'OK'::TEXT),
        ('videos_table_size'::TEXT,
         pg_size_pretty(videos_table_size),
         'bytes'::TEXT,
         'OK'::TEXT),
        ('active_connections'::TEXT,
         total_connections::TEXT,
         'count'::TEXT,
         CASE WHEN total_connections < 50 THEN 'OK' ELSE 'WARNING' END),
        ('avg_query_time'::TEXT,
         ROUND(avg_query_time, 2)::TEXT,
         'ms'::TEXT,
         CASE WHEN avg_query_time < 100 THEN 'OK' WHEN avg_query_time < 500 THEN 'WARNING' ELSE 'ERROR' END);
END;
$$ LANGUAGE plpgsql;

-- ===== 📋 دوال التقارير المتقدمة =====

-- دالة تقرير شامل للنشاط اليومي
CREATE OR REPLACE FUNCTION generate_daily_activity_report(
    p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    report_section TEXT,
    metric_name TEXT,
    metric_value TEXT,
    comparison_yesterday TEXT,
    trend TEXT
) AS $$
DECLARE
    target_date DATE := p_date;
    yesterday_date DATE := p_date - INTERVAL '1 day';

    today_photos INTEGER;
    today_videos INTEGER;
    today_users INTEGER;
    today_size BIGINT;

    yesterday_photos INTEGER;
    yesterday_videos INTEGER;
    yesterday_users INTEGER;
    yesterday_size BIGINT;
BEGIN
    -- إحصائيات اليوم المحدد
    SELECT
        COUNT(CASE WHEN 'photo' = 'photo' THEN 1 END),
        COUNT(CASE WHEN 'video' = 'video' THEN 1 END),
        COUNT(DISTINCT username),
        SUM(file_size_bytes)
    INTO today_photos, today_videos, today_users, today_size
    FROM (
        SELECT 'photo' as type, username, file_size_bytes
        FROM public.photos
        WHERE DATE(capture_timestamp) = target_date AND status = 'active'

        UNION ALL

        SELECT 'video' as type, username, file_size_bytes
        FROM public.videos
        WHERE DATE(capture_timestamp) = target_date AND status = 'active'
    ) daily_data;

    -- إحصائيات الأمس
    SELECT
        COUNT(CASE WHEN 'photo' = 'photo' THEN 1 END),
        COUNT(CASE WHEN 'video' = 'video' THEN 1 END),
        COUNT(DISTINCT username),
        SUM(file_size_bytes)
    INTO yesterday_photos, yesterday_videos, yesterday_users, yesterday_size
    FROM (
        SELECT 'photo' as type, username, file_size_bytes
        FROM public.photos
        WHERE DATE(capture_timestamp) = yesterday_date AND status = 'active'

        UNION ALL

        SELECT 'video' as type, username, file_size_bytes
        FROM public.videos
        WHERE DATE(capture_timestamp) = yesterday_date AND status = 'active'
    ) yesterday_data;

    -- تعيين القيم الافتراضية للمقارنة
    today_photos := COALESCE(today_photos, 0);
    today_videos := COALESCE(today_videos, 0);
    today_users := COALESCE(today_users, 0);
    today_size := COALESCE(today_size, 0);

    yesterday_photos := COALESCE(yesterday_photos, 0);
    yesterday_videos := COALESCE(yesterday_videos, 0);
    yesterday_users := COALESCE(yesterday_users, 0);
    yesterday_size := COALESCE(yesterday_size, 0);

    -- إرجاع التقرير
    RETURN QUERY VALUES
        ('uploads'::TEXT, 'total_photos'::TEXT, today_photos::TEXT, yesterday_photos::TEXT,
         CASE
             WHEN today_photos > yesterday_photos THEN 'up'
             WHEN today_photos < yesterday_photos THEN 'down'
             ELSE 'stable'
         END),
        ('uploads'::TEXT, 'total_videos'::TEXT, today_videos::TEXT, yesterday_videos::TEXT,
         CASE
             WHEN today_videos > yesterday_videos THEN 'up'
             WHEN today_videos < yesterday_videos THEN 'down'
             ELSE 'stable'
         END),
        ('users'::TEXT, 'active_users'::TEXT, today_users::TEXT, yesterday_users::TEXT,
         CASE
             WHEN today_users > yesterday_users THEN 'up'
             WHEN today_users < yesterday_users THEN 'down'
             ELSE 'stable'
         END),
        ('storage'::TEXT, 'total_size_mb'::TEXT,
         ROUND(today_size::DECIMAL / 1024 / 1024, 2)::TEXT,
         ROUND(yesterday_size::DECIMAL / 1024 / 1024, 2)::TEXT,
         CASE
             WHEN today_size > yesterday_size THEN 'up'
             WHEN today_size < yesterday_size THEN 'down'
             ELSE 'stable'
         END);
END;
$$ LANGUAGE plpgsql;

-- دالة تقرير استخدام التخزين المفصل
CREATE OR REPLACE FUNCTION generate_storage_usage_report()
RETURNS TABLE (
    category TEXT,
    subcategory TEXT,
    file_count INTEGER,
    total_size_mb DECIMAL(10,2),
    avg_file_size_mb DECIMAL(8,2),
    percentage_of_total DECIMAL(5,2)
) AS $$
DECLARE
    total_storage BIGINT;
BEGIN
    -- حساب إجمالي التخزين
    SELECT SUM(size_bytes) INTO total_storage
    FROM (
        SELECT file_size_bytes as size_bytes FROM public.photos WHERE status = 'active'
        UNION ALL
        SELECT file_size_bytes as size_bytes FROM public.videos WHERE status = 'active'
    ) all_files;

    total_storage := COALESCE(total_storage, 1); -- تجنب القسمة على صفر

    RETURN QUERY
    WITH storage_stats AS (
        -- إحصائيات الصور حسب النوع
        SELECT
            'photos' as cat,
            LEFT(full_location_code, 1) as subcat,
            COUNT(*) as files,
            SUM(file_size_bytes) as total_bytes,
            AVG(file_size_bytes) as avg_bytes
        FROM public.photos
        WHERE status = 'active'
        GROUP BY LEFT(full_location_code, 1)

        UNION ALL

        -- إحصائيات الفيديوهات حسب النوع
        SELECT
            'videos' as cat,
            LEFT(full_location_code, 1) as subcat,
            COUNT(*) as files,
            SUM(file_size_bytes) as total_bytes,
            AVG(file_size_bytes) as avg_bytes
        FROM public.videos
        WHERE status = 'active'
        GROUP BY LEFT(full_location_code, 1)

        UNION ALL

        -- إحصائيات حسب المستخدم (أكثر 10 مستخدمين)
        SELECT
            'by_user' as cat,
            username as subcat,
            COUNT(*) as files,
            SUM(file_size_bytes) as total_bytes,
            AVG(file_size_bytes) as avg_bytes
        FROM (
            SELECT username, file_size_bytes FROM public.photos WHERE status = 'active'
            UNION ALL
            SELECT username, file_size_bytes FROM public.videos WHERE status = 'active'
        ) user_files
        GROUP BY username
        ORDER BY SUM(file_size_bytes) DESC
        LIMIT 10
    )
    SELECT
        ss.cat,
        ss.subcat,
        ss.files::INTEGER,
        ROUND((ss.total_bytes::DECIMAL / 1024 / 1024), 2),
        ROUND((ss.avg_bytes::DECIMAL / 1024 / 1024), 2),
        ROUND((ss.total_bytes::DECIMAL / total_storage * 100), 2)
    FROM storage_stats ss
    ORDER BY ss.cat, ss.total_bytes DESC;
END;
$$ LANGUAGE plpgsql;

-- دالة تقرير الأمان والأجهزة
CREATE OR REPLACE FUNCTION generate_security_report()
RETURNS TABLE (
    security_metric TEXT,
    metric_value TEXT,
    risk_level TEXT,
    recommendation TEXT
) AS $$
DECLARE
    suspicious_devices INTEGER;
    blocked_devices INTEGER;
    multiple_device_users INTEGER;
    recent_failed_logins INTEGER;
    inactive_users_with_devices INTEGER;
BEGIN
    -- عدد الأجهزة المشكوك فيها
    SELECT COUNT(*) INTO suspicious_devices
    FROM public.devices
    WHERE trust_level = 'suspicious' AND is_active = true;

    -- عدد الأجهزة المحظورة
    SELECT COUNT(*) INTO blocked_devices
    FROM public.devices
    WHERE trust_level = 'blocked';

    -- المستخدمين مع أجهزة متعددة
    SELECT COUNT(*) INTO multiple_device_users
    FROM (
        SELECT username
        FROM public.devices
        WHERE is_active = true
        GROUP BY username
        HAVING COUNT(*) > 1
    ) multi_device;

    -- محاولات تسجيل الدخول الفاشلة الأخيرة
    SELECT COUNT(*) INTO recent_failed_logins
    FROM public.user_sessions
    WHERE created_at >= NOW() - INTERVAL '24 hours'
      AND expires_at < created_at; -- جلسات فاشلة

    -- مستخدمين غير نشطين مع أجهزة
    SELECT COUNT(DISTINCT d.username) INTO inactive_users_with_devices
    FROM public.devices d
    JOIN public.users u ON d.username = u.username
    WHERE u.is_active = false AND d.is_active = true;

    -- إرجاع التقرير
    RETURN QUERY VALUES
        ('suspicious_devices'::TEXT,
         suspicious_devices::TEXT,
         CASE WHEN suspicious_devices = 0 THEN 'low' WHEN suspicious_devices < 5 THEN 'medium' ELSE 'high' END,
         'مراجعة الأجهزة المشكوك فيها وتحديث مستوى الثقة'::TEXT),
        ('blocked_devices'::TEXT,
         blocked_devices::TEXT,
         'info'::TEXT,
         'مراجعة دورية للأجهزة المحظورة'::TEXT),
        ('multiple_device_users'::TEXT,
         multiple_device_users::TEXT,
         CASE WHEN multiple_device_users < 5 THEN 'low' WHEN multiple_device_users < 20 THEN 'medium' ELSE 'high' END,
         'التحقق من المستخدمين الذين لديهم أجهزة متعددة'::TEXT),
        ('recent_failed_logins'::TEXT,
         recent_failed_logins::TEXT,
         CASE WHEN recent_failed_logins = 0 THEN 'low' WHEN recent_failed_logins < 10 THEN 'medium' ELSE 'high' END,
         'مراقبة محاولات تسجيل الدخول الفاشلة'::TEXT),
        ('inactive_users_with_devices'::TEXT,
         inactive_users_with_devices::TEXT,
         CASE WHEN inactive_users_with_devices = 0 THEN 'low' ELSE 'medium' END,
         'إلغاء تفعيل أجهزة المستخدمين غير النشطين'::TEXT);
END;
$$ LANGUAGE plpgsql;

-- ===== 🎯 ملخص الدوال المتاحة =====
/*
📊 دوال الترتيب والعرض:
- get_photos_sorted(): ترتيب الصور بمعايير متعددة
- get_videos_sorted(): ترتيب الفيديوهات بمعايير متعددة
- get_media_mixed_sorted(): ترتيب مختلط للوسائط
- search_media_advanced(): بحث متقدم في الوسائط

📈 دوال التحليل والإحصائيات:
- get_dashboard_stats(): إحصائيات لوحة المراقبة
- get_location_statistics(): إحصائيات المواقع
- analyze_usage_trends(): تحليل الاتجاهات الزمنية
- get_top_users(): أكثر المستخدمين نشاطاً
- analyze_location_usage(): تحليل استخدام المواقع

🔍 دوال البحث المتقدم:
- smart_search_with_ranking(): بحث ذكي مع تقييم النتائج

🔧 دوال الصيانة والتحسين:
- optimize_database(): تحسين الأداء وإعادة بناء الفهارس
- cleanup_deleted_records(): تنظيف البيانات المحذوفة
- check_data_integrity(): فحص سلامة البيانات
- get_performance_stats(): إحصائيات الأداء

📋 دوال التقارير:
- generate_daily_activity_report(): تقرير النشاط اليومي
- generate_storage_usage_report(): تقرير استخدام التخزين
- generate_security_report(): تقرير الأمان والأجهزة

🎨 ميزات متقدمة:
- دعم الترتيب المختلط الذكي
- تحليل الاتجاهات مع معدلات النمو
- بحث ذكي مع نقاط الصلة
- تقارير أمان شاملة
- فحص سلامة البيانات التلقائي
- تحسين الأداء المتقدم

📝 ملاحظات الاستخدام:
- جميع الدوال تدعم المعاملات الاختيارية للمرونة
- النتائج مرتبة ومحسنة للأداء
- دعم كامل للغة العربية
- معالجة شاملة للأخطاء
- توافق مع PostgreSQL 12+

🔄 آخر تحديث: يناير 2025
👨‍💻 المطور: فريق ذاكرة القمر
*/
