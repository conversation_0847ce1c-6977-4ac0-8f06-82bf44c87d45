-- اختبار مبسط للمستخدم: a505a910-c192-4213-b3fc-63fd59d0659b
-- Simple Test for Real User ID
-- Date: 2025-01-12

-- فحص المستخدم أولاً
SELECT 'User Info Check' as test_name;
SELECT id, email, created_at 
FROM auth.users 
WHERE id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID;

-- فحص الأجهزة الحالية
SELECT 'Current Devices Check' as test_name;
SELECT COUNT(*) as current_device_count
FROM devices 
WHERE user_id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID;

-- اختبار 1: إضافة جهاز Samsung Galaxy
SELECT 'Test 1: Adding Samsung Galaxy S23' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_samsung_s23_test_001',
    'android_id_samsung_test_001',
    'samsung/beyond1lte/beyond1:13/TP1A.220624.014/G973FXXS9FVL1:user/release-keys',
    92.5,
    'high',
    'Samsung Galaxy S23',
    'SM-G991B',
    'Samsung'
);

-- اختبار 2: إضافة جهاز Google Pixel
SELECT 'Test 2: Adding Google Pixel 7 Pro' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_pixel_7_test_002',
    'android_id_pixel_test_002',
    'google/cheetah/cheetah:14/UQ1A.240205.004/11269751:user/release-keys',
    95.0,
    'high',
    'Google Pixel 7 Pro',
    'GE2AE',
    'Google'
);

-- اختبار 3: تحديث الجهاز الأول
SELECT 'Test 3: Updating Samsung device' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_samsung_s23_updated_001',
    'android_id_samsung_test_001', -- نفس Android ID
    'samsung/beyond1lte/beyond1:14/TP1A.220624.015/G973FXXS9FVL2:user/release-keys',
    89.0,
    'medium',
    'Samsung Galaxy S23 Updated',
    'SM-G991B',
    'Samsung'
);

-- اختبار 4: الحصول على قائمة الأجهزة
SELECT 'Test 4: Getting user devices' as test_name;
SELECT get_user_devices('a505a910-c192-4213-b3fc-63fd59d0659b'::UUID);

-- اختبار 5: إضافة جهاز ثالث
SELECT 'Test 5: Adding third device' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_xiaomi_13_test_003',
    'android_id_xiaomi_test_003',
    'xiaomi/marble/marble:13/TKQ1.220829.002/V14.0.3.0.TMRMIXM:user/release-keys',
    88.0,
    'medium',
    'Xiaomi 13 Pro',
    '2210132C',
    'Xiaomi'
);

-- اختبار 6: محاولة إضافة جهاز رابع - يجب أن يفشل
SELECT 'Test 6: Adding fourth device - should fail' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_oneplus_11_test_004',
    'android_id_oneplus_test_004',
    'oneplus/OP594DL1/OP594DL1:13/TP1A.220905.001/Q.************:user/release-keys',
    85.0,
    'medium',
    'OnePlus 11',
    'CPH2449',
    'OnePlus'
);

-- فحص النتائج النهائية
SELECT 'Final Results Check' as test_name;
SELECT 
    device_name,
    device_brand,
    device_model,
    confidence_score,
    trust_level,
    last_verified_at,
    created_at
FROM devices 
WHERE user_id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID
ORDER BY created_at DESC;

-- إحصائيات
SELECT 'Statistics' as test_name;
SELECT 
    COUNT(*) as total_devices,
    ROUND(AVG(confidence_score), 2) as avg_confidence,
    MAX(confidence_score) as max_confidence,
    MIN(confidence_score) as min_confidence,
    COUNT(CASE WHEN trust_level = 'high' THEN 1 END) as high_trust_devices,
    COUNT(CASE WHEN trust_level = 'medium' THEN 1 END) as medium_trust_devices,
    COUNT(CASE WHEN trust_level = 'low' THEN 1 END) as low_trust_devices
FROM devices 
WHERE user_id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID;
