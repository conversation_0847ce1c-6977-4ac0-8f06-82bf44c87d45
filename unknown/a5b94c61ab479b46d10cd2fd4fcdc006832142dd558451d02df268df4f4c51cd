#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة سطر الأوامر الرئيسية لإدارة نظام ذاكرة القمر
Moon Memory Command Line Interface

أداة شاملة لإدارة جميع جوانب النظام من سطر الأوامر:
- إدارة قاعدة البيانات
- النسخ الاحتياطية والاستعادة
- مراقبة النظام
- الصيانة والتحسين
- التقارير والإحصائيات

المطور: فريق ذاكرة القمر
التاريخ: يناير 2025
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Optional

# إضافة مسار الأدوات للاستيراد
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_admin_tool import DatabaseAdmin
from backup_manager import BackupManager
from system_monitor import SystemMonitor

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MoonMemoryCLI:
    """واجهة سطر الأوامر الرئيسية"""
    
    def __init__(self):
        """تهيئة واجهة سطر الأوامر"""
        self.db_admin = None
        self.backup_manager = None
        self.system_monitor = None
        
    def init_components(self, config_file: str = 'db_config.json'):
        """تهيئة المكونات"""
        try:
            self.db_admin = DatabaseAdmin(config_file)
            self.backup_manager = BackupManager(config_file)
            self.system_monitor = SystemMonitor(config_file)
            return True
        except Exception as e:
            logger.error(f"خطأ في تهيئة المكونات: {e}")
            return False
    
    def database_command(self, args):
        """أوامر إدارة قاعدة البيانات"""
        if not self.db_admin.connect():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return 1
        
        try:
            if args.db_action == 'stats':
                return self._show_database_stats()
            elif args.db_action == 'maintenance':
                return self._run_database_maintenance(args.full)
            elif args.db_action == 'optimize':
                return self._optimize_database()
            elif args.db_action == 'integrity':
                return self._check_data_integrity()
            elif args.db_action == 'cleanup':
                return self._cleanup_database()
            else:
                print(f"❌ أمر قاعدة البيانات غير معروف: {args.db_action}")
                return 1
        finally:
            self.db_admin.disconnect()
    
    def _show_database_stats(self):
        """عرض إحصائيات قاعدة البيانات"""
        print("\n🗄️  إحصائيات قاعدة البيانات")
        print("=" * 50)
        
        stats = self.db_admin.get_database_stats()
        
        # الإحصائيات العامة
        print("\n📊 الإحصائيات العامة:")
        for key, value in stats['dashboard'].items():
            print(f"  • {key}: {value}")
        
        # أداء النظام
        print("\n⚡ أداء النظام:")
        for key, data in stats['performance'].items():
            status_icon = "✅" if data['status'] == 'OK' else "⚠️" if data['status'] == 'WARNING' else "❌"
            print(f"  {status_icon} {key}: {data['value']} {data['unit']}")
        
        # أحجام الجداول
        print("\n💾 أكبر الجداول:")
        for table in stats['table_sizes'][:5]:
            print(f"  • {table['tablename']}: {table['size']}")
        
        return 0
    
    def _run_database_maintenance(self, full: bool = False):
        """تشغيل صيانة قاعدة البيانات"""
        print(f"\n🔧 بدء صيانة قاعدة البيانات {'الكاملة' if full else 'العادية'}...")
        
        result = self.db_admin.run_maintenance(full)
        
        if result['status'] == 'success':
            print("✅ تمت الصيانة بنجاح")
            for op in result['operations']:
                print(f"  ✓ {op['operation']}")
            return 0
        else:
            print(f"❌ فشلت الصيانة: {result.get('error', 'خطأ غير معروف')}")
            return 1
    
    def _optimize_database(self):
        """تحسين قاعدة البيانات"""
        print("\n⚡ تحسين قاعدة البيانات...")
        
        query = "SELECT * FROM optimize_database()"
        results = self.db_admin.execute_query(query)
        
        print("✅ تم تحسين قاعدة البيانات:")
        for result in results:
            status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
            print(f"  {status_icon} {result['operation']} على {result['table_name']}: {result['execution_time_ms']}ms")
        
        return 0
    
    def _check_data_integrity(self):
        """فحص سلامة البيانات"""
        print("\n🔍 فحص سلامة البيانات...")
        
        query = "SELECT * FROM check_data_integrity()"
        results = self.db_admin.execute_query(query)
        
        print("📋 نتائج فحص سلامة البيانات:")
        for result in results:
            status_icon = "✅" if result['status'] == 'OK' else "⚠️" if result['status'] == 'WARNING' else "❌"
            print(f"  {status_icon} {result['check_name']}: {result['issue_count']} مشكلة - {result['description']}")
        
        return 0
    
    def _cleanup_database(self):
        """تنظيف قاعدة البيانات"""
        print("\n🧹 تنظيف قاعدة البيانات...")
        
        query = "SELECT * FROM cleanup_deleted_records()"
        results = self.db_admin.execute_query(query)
        
        print("✅ تم تنظيف قاعدة البيانات:")
        for result in results:
            print(f"  • {result['table_name']}: تم حذف {result['deleted_count']} سجل")
        
        return 0
    
    def backup_command(self, args):
        """أوامر النسخ الاحتياطية"""
        try:
            if args.backup_action == 'create':
                return self._create_backup(args.backup_type, args.name, args.compress)
            elif args.backup_action == 'list':
                return self._list_backups()
            elif args.backup_action == 'restore':
                return self._restore_backup(args.name, args.confirm)
            elif args.backup_action == 'schedule':
                return self._setup_backup_schedule()
            elif args.backup_action == 'cleanup':
                return self._cleanup_old_backups()
            else:
                print(f"❌ أمر النسخ الاحتياطية غير معروف: {args.backup_action}")
                return 1
        except Exception as e:
            logger.error(f"خطأ في أمر النسخ الاحتياطية: {e}")
            return 1
    
    def _create_backup(self, backup_type: str, name: str = None, compress: bool = True):
        """إنشاء نسخة احتياطية"""
        print(f"\n💾 إنشاء نسخة احتياطية {backup_type}...")
        
        if backup_type == 'full':
            result = self.backup_manager.create_full_backup(name, compress)
        elif backup_type == 'incremental':
            result = self.backup_manager.create_incremental_backup()
        else:
            print(f"❌ نوع النسخة الاحتياطية غير معروف: {backup_type}")
            return 1
        
        if result['status'] == 'completed':
            print(f"✅ تمت النسخة الاحتياطية بنجاح: {result['name']}")
            if backup_type == 'full':
                total_size_mb = result['total_size'] / (1024 * 1024)
                print(f"  📊 الحجم الإجمالي: {total_size_mb:.2f} ميجابايت")
                print(f"  📁 عدد الملفات: {len(result['files'])}")
            elif backup_type == 'incremental':
                print(f"  🔄 عدد التغييرات: {result['change_count']}")
            return 0
        else:
            print(f"❌ فشلت النسخة الاحتياطية: {result.get('error', 'خطأ غير معروف')}")
            return 1
    
    def _list_backups(self):
        """عرض قائمة النسخ الاحتياطية"""
        print("\n📋 قائمة النسخ الاحتياطية")
        print("=" * 50)
        
        backups = self.backup_manager.list_backups()
        
        # النسخ الكاملة
        print("\n💾 النسخ الكاملة:")
        if backups['full_backups']:
            for backup in backups['full_backups'][:5]:  # أحدث 5 نسخ
                size_mb = backup.get('total_size', 0) / (1024 * 1024)
                status_icon = "✅" if backup['status'] == 'completed' else "❌"
                print(f"  {status_icon} {backup['name']} - {size_mb:.1f}MB - {backup['start_time'][:19]}")
        else:
            print("  لا توجد نسخ كاملة")
        
        # النسخ التزايدية
        print("\n🔄 النسخ التزايدية:")
        if backups['incremental_backups']:
            for backup in backups['incremental_backups'][:10]:  # أحدث 10 نسخ
                status_icon = "✅" if backup['status'] == 'completed' else "❌"
                change_count = backup.get('change_count', 0)
                print(f"  {status_icon} {backup['name']} - {change_count} تغيير - {backup['start_time'][:19]}")
        else:
            print("  لا توجد نسخ تزايدية")
        
        return 0
    
    def _restore_backup(self, backup_name: str, confirm: bool = False):
        """استعادة نسخة احتياطية"""
        if not backup_name:
            print("❌ يجب تحديد اسم النسخة الاحتياطية")
            return 1
        
        if not confirm:
            print("⚠️  تحذير: عملية الاستعادة ستحل محل البيانات الحالية!")
            print("استخدم --confirm لتأكيد العملية")
            return 1
        
        print(f"\n🔄 استعادة النسخة الاحتياطية: {backup_name}")
        
        restore_options = {
            'restore_database': True,
            'restore_media': True,
            'restore_config': False,
            'confirm': True
        }
        
        result = self.backup_manager.restore_backup(backup_name, restore_options)
        
        if result['status'] == 'completed':
            print("✅ تمت الاستعادة بنجاح")
            for op in result['operations']:
                status_icon = "✅" if op['status'] == 'success' else "⚠️" if op['status'] == 'skipped' else "❌"
                print(f"  {status_icon} {op['operation']}")
            return 0
        else:
            print(f"❌ فشلت الاستعادة: {result.get('error', 'خطأ غير معروف')}")
            return 1
    
    def _setup_backup_schedule(self):
        """إعداد جدولة النسخ الاحتياطية"""
        print("\n⏰ إعداد جدولة النسخ الاحتياطية...")
        
        self.backup_manager.setup_scheduled_backups()
        print("✅ تم إعداد الجدولة:")
        print("  • نسخة كاملة يومياً في 2:00 صباحاً")
        print("  • نسخة تزايدية كل 6 ساعات")
        print("  • تنظيف النسخ القديمة أسبوعياً")
        
        return 0
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        print("\n🧹 تنظيف النسخ الاحتياطية القديمة...")
        
        self.backup_manager._cleanup_old_backups()
        print("✅ تم تنظيف النسخ القديمة")
        
        return 0
    
    def monitor_command(self, args):
        """أوامر مراقبة النظام"""
        try:
            if args.monitor_action == 'status':
                return self._show_system_status()
            elif args.monitor_action == 'start':
                return self._start_monitoring(args.interval)
            elif args.monitor_action == 'web':
                return self._start_web_monitor(args.port)
            else:
                print(f"❌ أمر المراقبة غير معروف: {args.monitor_action}")
                return 1
        except Exception as e:
            logger.error(f"خطأ في أمر المراقبة: {e}")
            return 1
    
    def _show_system_status(self):
        """عرض حالة النظام"""
        print("\n🖥️  حالة النظام")
        print("=" * 50)
        
        if not self.system_monitor.connect_database():
            print("❌ فشل في الاتصال بقاعدة البيانات للمراقبة")
            return 1
        
        metrics = self.system_monitor.collect_system_metrics()
        alerts = self.system_monitor.check_thresholds(metrics)
        
        # حالة النظام العامة
        status = 'critical' if any(a['severity'] == 'critical' for a in alerts) else \
                'warning' if any(a['severity'] == 'warning' for a in alerts) else 'ok'
        
        status_icon = "✅" if status == 'ok' else "⚠️" if status == 'warning' else "❌"
        status_text = "طبيعي" if status == 'ok' else "تحذير" if status == 'warning' else "خطر"
        
        print(f"\n{status_icon} الحالة العامة: {status_text}")
        
        # مقاييس النظام
        if metrics.get('system'):
            sys_metrics = metrics['system']
            print(f"\n💻 المعالج: {sys_metrics.get('cpu_percent', 0):.1f}%")
            print(f"🧠 الذاكرة: {sys_metrics.get('memory_percent', 0):.1f}%")
            print(f"💾 القرص: {sys_metrics.get('disk_percent', 0):.1f}%")
        
        # مقاييس قاعدة البيانات
        if metrics.get('database'):
            db_metrics = metrics['database']
            print(f"🗄️  اتصالات قاعدة البيانات: {db_metrics.get('active_connections', 0)}")
            print(f"📊 حجم قاعدة البيانات: {db_metrics.get('size', 'غير معروف')}")
        
        # التنبيهات
        if alerts:
            print(f"\n🚨 التنبيهات ({len(alerts)}):")
            for alert in alerts:
                severity_icon = "⚠️" if alert['severity'] == 'warning' else "❌"
                print(f"  {severity_icon} {alert['message']}")
        else:
            print("\n✅ لا توجد تنبيهات")
        
        return 0
    
    def _start_monitoring(self, interval: int = 60):
        """بدء المراقبة المستمرة"""
        print(f"\n🔍 بدء مراقبة النظام (فترة: {interval} ثانية)")
        print("اضغط Ctrl+C للإيقاف")
        
        try:
            self.system_monitor.start_monitoring(interval)
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف المراقبة")
        
        return 0
    
    def _start_web_monitor(self, port: int = 5000):
        """بدء واجهة الويب للمراقبة"""
        print(f"\n🌐 بدء واجهة الويب للمراقبة على المنفذ {port}")
        
        try:
            from web_monitor import main as web_main
            import sys
            
            # تمرير المعاملات لواجهة الويب
            sys.argv = ['web_monitor.py', '--port', str(port)]
            web_main()
        except ImportError:
            print("❌ واجهة الويب غير متاحة")
            return 1
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف واجهة الويب")
        
        return 0

    def report_command(self, args):
        """أوامر التقارير"""
        try:
            print(f"\n📋 إنشاء تقرير {args.report_type}...")

            if not self.db_admin.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return 1

            try:
                result = self.db_admin.generate_report(args.report_type)

                if result['status'] == 'success':
                    if args.format == 'json':
                        output = json.dumps(result, ensure_ascii=False, indent=2, default=str)
                        if args.output:
                            with open(args.output, 'w', encoding='utf-8') as f:
                                f.write(output)
                            print(f"✅ تم حفظ التقرير في: {args.output}")
                        else:
                            print(output)
                    else:
                        # عرض نصي
                        print(f"✅ تقرير {args.report_type} - {result['generated_at'][:19]}")
                        for section_name, section_data in result['sections'].items():
                            print(f"\n--- {section_name} ---")
                            if isinstance(section_data, list):
                                for item in section_data[:10]:  # أول 10 عناصر
                                    if isinstance(item, dict):
                                        for key, value in item.items():
                                            print(f"  {key}: {value}")
                                        print()
                                    else:
                                        print(f"  {item}")
                            else:
                                print(f"  {section_data}")

                    return 0
                else:
                    print(f"❌ فشل في إنشاء التقرير: {result.get('error', 'خطأ غير معروف')}")
                    return 1
            finally:
                self.db_admin.disconnect()

        except Exception as e:
            logger.error(f"خطأ في أمر التقارير: {e}")
            return 1

    def tools_command(self, args):
        """أدوات إضافية"""
        try:
            if args.tool_action == 'health-check':
                return self._health_check()
            elif args.tool_action == 'export-data':
                return self._export_data(args.file)
            elif args.tool_action == 'import-data':
                return self._import_data(args.file)
            elif args.tool_action == 'reset-passwords':
                return self._reset_passwords()
            elif args.tool_action == 'cleanup-sessions':
                return self._cleanup_sessions()
            else:
                print(f"❌ أداة غير معروفة: {args.tool_action}")
                return 1
        except Exception as e:
            logger.error(f"خطأ في الأدوات الإضافية: {e}")
            return 1

    def _health_check(self):
        """فحص صحة النظام الشامل"""
        print("\n🏥 فحص صحة النظام الشامل")
        print("=" * 50)

        health_score = 100
        issues = []

        # فحص قاعدة البيانات
        print("\n🗄️  فحص قاعدة البيانات...")
        if self.db_admin.connect():
            try:
                # فحص سلامة البيانات
                query = "SELECT * FROM check_data_integrity()"
                results = self.db_admin.execute_query(query)

                for result in results:
                    if result['status'] != 'OK':
                        health_score -= 10
                        issues.append(f"قاعدة البيانات: {result['description']}")

                print("  ✅ قاعدة البيانات متاحة")
            except Exception as e:
                health_score -= 30
                issues.append(f"قاعدة البيانات: خطأ في الاتصال - {e}")
                print(f"  ❌ خطأ في قاعدة البيانات: {e}")
            finally:
                self.db_admin.disconnect()
        else:
            health_score -= 50
            issues.append("قاعدة البيانات: غير متاحة")
            print("  ❌ قاعدة البيانات غير متاحة")

        # فحص النظام
        print("\n🖥️  فحص النظام...")
        try:
            if self.system_monitor.connect_database():
                metrics = self.system_monitor.collect_system_metrics()
                alerts = self.system_monitor.check_thresholds(metrics)

                critical_alerts = [a for a in alerts if a['severity'] == 'critical']
                warning_alerts = [a for a in alerts if a['severity'] == 'warning']

                health_score -= len(critical_alerts) * 15
                health_score -= len(warning_alerts) * 5

                for alert in critical_alerts:
                    issues.append(f"نظام حرج: {alert['message']}")

                for alert in warning_alerts:
                    issues.append(f"نظام تحذير: {alert['message']}")

                print(f"  ✅ النظام يعمل - {len(alerts)} تنبيه")
            else:
                health_score -= 20
                issues.append("النظام: فشل في جمع المقاييس")
                print("  ⚠️ فشل في جمع مقاييس النظام")
        except Exception as e:
            health_score -= 20
            issues.append(f"النظام: خطأ في المراقبة - {e}")
            print(f"  ❌ خطأ في مراقبة النظام: {e}")

        # فحص النسخ الاحتياطية
        print("\n💾 فحص النسخ الاحتياطية...")
        try:
            backups = self.backup_manager.list_backups()

            if not backups['full_backups']:
                health_score -= 25
                issues.append("النسخ الاحتياطية: لا توجد نسخ كاملة")
                print("  ❌ لا توجد نسخ احتياطية كاملة")
            else:
                latest_backup = backups['full_backups'][0]
                backup_date = datetime.fromisoformat(latest_backup['start_time'])
                days_old = (datetime.now() - backup_date).days

                if days_old > 7:
                    health_score -= 15
                    issues.append(f"النسخ الاحتياطية: آخر نسخة قديمة ({days_old} يوم)")
                    print(f"  ⚠️ آخر نسخة احتياطية قديمة: {days_old} يوم")
                else:
                    print(f"  ✅ آخر نسخة احتياطية: {days_old} يوم")
        except Exception as e:
            health_score -= 15
            issues.append(f"النسخ الاحتياطية: خطأ في الفحص - {e}")
            print(f"  ❌ خطأ في فحص النسخ الاحتياطية: {e}")

        # النتيجة النهائية
        print(f"\n📊 نتيجة فحص الصحة: {max(0, health_score)}/100")

        if health_score >= 90:
            print("🟢 النظام في حالة ممتازة")
        elif health_score >= 70:
            print("🟡 النظام في حالة جيدة مع بعض التحذيرات")
        elif health_score >= 50:
            print("🟠 النظام يحتاج إلى صيانة")
        else:
            print("🔴 النظام في حالة حرجة - يحتاج إلى تدخل فوري")

        if issues:
            print(f"\n⚠️ المشاكل المكتشفة ({len(issues)}):")
            for issue in issues:
                print(f"  • {issue}")

        return 0 if health_score >= 70 else 1

def main():
    """الدالة الرئيسية لواجهة سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description='🌙 أداة إدارة نظام ذاكرة القمر الشاملة',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:

📊 إدارة قاعدة البيانات:
  %(prog)s database stats                    # عرض الإحصائيات
  %(prog)s database maintenance             # صيانة عادية
  %(prog)s database maintenance --full      # صيانة كاملة
  %(prog)s database optimize               # تحسين الأداء
  %(prog)s database integrity              # فحص سلامة البيانات

💾 النسخ الاحتياطية:
  %(prog)s backup create full              # نسخة كاملة
  %(prog)s backup create incremental       # نسخة تزايدية
  %(prog)s backup list                     # عرض القائمة
  %(prog)s backup restore backup_name --confirm  # استعادة
  %(prog)s backup schedule                 # إعداد الجدولة

🔍 مراقبة النظام:
  %(prog)s monitor status                  # حالة النظام
  %(prog)s monitor start --interval 30     # مراقبة مستمرة
  %(prog)s monitor web --port 5000         # واجهة ويب

📋 التقارير:
  %(prog)s report daily                    # تقرير يومي
  %(prog)s report storage                  # تقرير التخزين
  %(prog)s report security                 # تقرير الأمان
  %(prog)s report full --output report.json  # تقرير شامل

🛠️ أدوات أخرى:
  %(prog)s tools health-check             # فحص صحة النظام
        """
    )

    # المعاملات العامة
    parser.add_argument(
        '--config',
        default='db_config.json',
        help='ملف إعدادات قاعدة البيانات'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='عرض تفاصيل أكثر'
    )

    parser.add_argument(
        '--quiet',
        action='store_true',
        help='تقليل الرسائل'
    )

    # الأوامر الفرعية
    subparsers = parser.add_subparsers(dest='command', help='الأوامر المتاحة')

    # أوامر قاعدة البيانات
    db_parser = subparsers.add_parser('database', help='إدارة قاعدة البيانات')
    db_parser.add_argument(
        'db_action',
        choices=['stats', 'maintenance', 'optimize', 'integrity', 'cleanup'],
        help='إجراء قاعدة البيانات'
    )
    db_parser.add_argument('--full', action='store_true', help='صيانة كاملة')

    # أوامر النسخ الاحتياطية
    backup_parser = subparsers.add_parser('backup', help='إدارة النسخ الاحتياطية')
    backup_parser.add_argument(
        'backup_action',
        choices=['create', 'list', 'restore', 'schedule', 'cleanup'],
        help='إجراء النسخ الاحتياطية'
    )
    backup_parser.add_argument(
        'backup_type',
        nargs='?',
        choices=['full', 'incremental'],
        help='نوع النسخة الاحتياطية'
    )
    backup_parser.add_argument('--name', help='اسم النسخة الاحتياطية')
    backup_parser.add_argument('--compress', action='store_true', default=True, help='ضغط النسخة')
    backup_parser.add_argument('--confirm', action='store_true', help='تأكيد الاستعادة')

    # أوامر المراقبة
    monitor_parser = subparsers.add_parser('monitor', help='مراقبة النظام')
    monitor_parser.add_argument(
        'monitor_action',
        choices=['status', 'start', 'web'],
        help='إجراء المراقبة'
    )
    monitor_parser.add_argument('--interval', type=int, default=60, help='فترة المراقبة بالثواني')
    monitor_parser.add_argument('--port', type=int, default=5000, help='منفذ واجهة الويب')

    # أوامر التقارير
    report_parser = subparsers.add_parser('report', help='إنشاء التقارير')
    report_parser.add_argument(
        'report_type',
        choices=['daily', 'storage', 'security', 'full'],
        help='نوع التقرير'
    )
    report_parser.add_argument('--output', help='ملف الإخراج (JSON)')
    report_parser.add_argument('--format', choices=['json', 'text'], default='text', help='تنسيق الإخراج')

    # أدوات إضافية
    tools_parser = subparsers.add_parser('tools', help='أدوات إضافية')
    tools_parser.add_argument(
        'tool_action',
        choices=['health-check'],
        help='الأداة المطلوبة'
    )

    # تحليل المعاملات
    args = parser.parse_args()

    # إعداد مستوى التسجيل
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.quiet:
        logging.getLogger().setLevel(logging.WARNING)

    # التحقق من وجود أمر
    if not args.command:
        parser.print_help()
        return 1

    # إنشاء واجهة سطر الأوامر
    cli = MoonMemoryCLI()

    if not cli.init_components(args.config):
        print("❌ فشل في تهيئة النظام")
        return 1

    # تنفيذ الأمر المطلوب
    try:
        if args.command == 'database':
            return cli.database_command(args)
        elif args.command == 'backup':
            return cli.backup_command(args)
        elif args.command == 'monitor':
            return cli.monitor_command(args)
        elif args.command == 'report':
            return cli.report_command(args)
        elif args.command == 'tools':
            return cli.tools_command(args)
        else:
            print(f"❌ أمر غير معروف: {args.command}")
            return 1

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        return 0
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
