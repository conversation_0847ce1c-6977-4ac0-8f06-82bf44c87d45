import 'dart:io';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:logger/logger.dart';

final cacheServiceProvider = Provider<CacheService>((ref) => CacheService());

/// خدمة التخزين المؤقت
class CacheService {
  final _logger = Logger();
  Directory? _cacheDir;

  /// تهيئة مجلد التخزين المؤقت
  Future<void> initialize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      _cacheDir = Directory(path.join(tempDir.path, 'moon_memory_cache'));
      
      if (!await _cacheDir!.exists()) {
        await _cacheDir!.create(recursive: true);
      }
      
      _logger.i('Cache directory initialized: ${_cacheDir!.path}');
      
      // تنظيف الملفات القديمة
      await _cleanExpiredFiles();
    } catch (e) {
      _logger.e('Error initializing cache directory', error: e);
    }
  }

  /// حفظ ملف في التخزين المؤقت
  Future<String?> cacheFile(String key, File file) async {
    try {
      if (_cacheDir == null) await initialize();
      
      final cachedFile = File(path.join(_cacheDir!.path, key));
      await file.copy(cachedFile.path);
      
      // حفظ معلومات الملف
      await _saveCacheMetadata(key, {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'size': await file.length(),
        'path': cachedFile.path,
      });
      
      _logger.d('File cached: $key');
      return cachedFile.path;
    } catch (e) {
      _logger.e('Error caching file', error: e);
      return null;
    }
  }

  /// استرجاع ملف من التخزين المؤقت
  Future<File?> getCachedFile(String key) async {
    try {
      if (_cacheDir == null) await initialize();
      
      final cachedFile = File(path.join(_cacheDir!.path, key));
      
      if (await cachedFile.exists()) {
        // التحقق من انتهاء الصلاحية
        final metadata = await _getCacheMetadata(key);
        if (metadata != null) {
          final timestamp = metadata['timestamp'] as int;
          final expirationTime = DateTime.fromMillisecondsSinceEpoch(timestamp)
              .add(const Duration(hours: 24));
          
          if (DateTime.now().isBefore(expirationTime)) {
            _logger.d('Cache hit: $key');
            return cachedFile;
          } else {
            // الملف منتهي الصلاحية
            await _removeCachedFile(key);
            _logger.d('Cache expired: $key');
          }
        }
      }
      
      _logger.d('Cache miss: $key');
      return null;
    } catch (e) {
      _logger.e('Error getting cached file', error: e);
      return null;
    }
  }

  /// حذف ملف من التخزين المؤقت
  Future<void> removeCachedFile(String key) async {
    await _removeCachedFile(key);
  }

  /// تنظيف جميع الملفات المؤقتة
  Future<void> clearCache() async {
    try {
      if (_cacheDir == null) await initialize();
      
      if (await _cacheDir!.exists()) {
        await _cacheDir!.delete(recursive: true);
        await _cacheDir!.create(recursive: true);
      }
      
      _logger.i('Cache cleared');
    } catch (e) {
      _logger.e('Error clearing cache', error: e);
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    try {
      if (_cacheDir == null) await initialize();
      
      int totalSize = 0;
      if (await _cacheDir!.exists()) {
        await for (final entity in _cacheDir!.list(recursive: true)) {
          if (entity is File) {
            totalSize += await entity.length();
          }
        }
      }
      
      return totalSize;
    } catch (e) {
      _logger.e('Error calculating cache size', error: e);
      return 0;
    }
  }

  /// تنظيف الملفات منتهية الصلاحية
  Future<void> _cleanExpiredFiles() async {
    try {
      if (_cacheDir == null || !await _cacheDir!.exists()) return;
      
      final metadataFile = File(path.join(_cacheDir!.path, '.metadata'));
      if (!await metadataFile.exists()) return;
      
      final metadataContent = await metadataFile.readAsString();
      final metadata = jsonDecode(metadataContent) as Map<String, dynamic>;
      
      final now = DateTime.now();
      final expiredKeys = <String>[];
      
      for (final entry in metadata.entries) {
        final fileMetadata = entry.value as Map<String, dynamic>;
        final timestamp = fileMetadata['timestamp'] as int;
        final expirationTime = DateTime.fromMillisecondsSinceEpoch(timestamp)
            .add(const Duration(hours: 24));
        
        if (now.isAfter(expirationTime)) {
          expiredKeys.add(entry.key);
        }
      }
      
      for (final key in expiredKeys) {
        await _removeCachedFile(key);
      }
      
      if (expiredKeys.isNotEmpty) {
        _logger.i('Cleaned ${expiredKeys.length} expired cache files');
      }
    } catch (e) {
      _logger.e('Error cleaning expired files', error: e);
    }
  }

  /// حفظ معلومات الملف المؤقت
  Future<void> _saveCacheMetadata(String key, Map<String, dynamic> data) async {
    try {
      final metadataFile = File(path.join(_cacheDir!.path, '.metadata'));
      
      Map<String, dynamic> metadata = {};
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        metadata = jsonDecode(content) as Map<String, dynamic>;
      }
      
      metadata[key] = data;
      await metadataFile.writeAsString(jsonEncode(metadata));
    } catch (e) {
      _logger.e('Error saving cache metadata', error: e);
    }
  }

  /// استرجاع معلومات الملف المؤقت
  Future<Map<String, dynamic>?> _getCacheMetadata(String key) async {
    try {
      final metadataFile = File(path.join(_cacheDir!.path, '.metadata'));
      
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        final metadata = jsonDecode(content) as Map<String, dynamic>;
        return metadata[key] as Map<String, dynamic>?;
      }
    } catch (e) {
      _logger.e('Error getting cache metadata', error: e);
    }
    return null;
  }

  /// حذف ملف مؤقت ومعلوماته
  Future<void> _removeCachedFile(String key) async {
    try {
      // حذف الملف
      final cachedFile = File(path.join(_cacheDir!.path, key));
      if (await cachedFile.exists()) {
        await cachedFile.delete();
      }
      
      // حذف المعلومات
      final metadataFile = File(path.join(_cacheDir!.path, '.metadata'));
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        final metadata = jsonDecode(content) as Map<String, dynamic>;
        metadata.remove(key);
        await metadataFile.writeAsString(jsonEncode(metadata));
      }
    } catch (e) {
      _logger.e('Error removing cached file', error: e);
    }
  }
}
