import 'package:flutter_dotenv/flutter_dotenv.dart';

/// إعدادات التطبيق العامة
class AppConfig {
  // معلومات التطبيق
  static String get appName => dotenv.env['APP_NAME'] ?? 'كاميرا ذاكرة القمر';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
  
  // إعدادات الأمان
  static const int maxLoginAttempts = 3;
  static const int sessionTimeoutMinutes = 30;
  static const int maxDevicesPerUser = 3;
  
  // إعدادات الكاميرا
  static const int maxPhotoSizeMB = 10;
  static const int imageQuality = 85;
  static const int maxVideoLengthSeconds = 300; // 5 دقائق
  
  // إعدادات الموقع
  static const int locationUpdateIntervalMinutes = 1;
  static const double locationAccuracyMeters = 10.0;
  
  // إعدادات التخزين
  static const String photosBucket = 'photos';
  static const String videosBucket = 'videos';
  static const int cacheExpirationHours = 24;
  
  // إعدادات الشبكة
  static const int networkTimeoutSeconds = 30;
  static const int retryAttempts = 3;
  
  // إعدادات UI
  static const int animationDurationMs = 300;
  static const double borderRadius = 12.0;
  
  // الألوان
  static const int primaryColorValue = 0xFFD4AF37; // ذهبي
  static const int secondaryColorValue = 0xFF4A4A4A; // رمادي داكن
  
  // التحقق من صحة الإعدادات
  static bool get isValid {
    return appName.isNotEmpty && appVersion.isNotEmpty;
  }
}
