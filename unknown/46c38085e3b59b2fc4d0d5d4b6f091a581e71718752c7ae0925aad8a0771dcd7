import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../core/services/language_service.dart';

class LanguageSettingsScreen extends ConsumerWidget {
  const LanguageSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocale = ref.watch(languageServiceProvider);

    Future<void> changeLanguage(String languageCode) async {
      await context.setLocale(Locale(languageCode));
      if (!context.mounted) return;
      await ref.read(languageServiceProvider.notifier).setLanguage(languageCode);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('settings.language'.tr()),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _LanguageOption(
            title: 'العربية',
            subtitle: 'اللغة العربية',
            value: 'ar',
            groupValue: currentLocale.languageCode,
            onChanged: changeLanguage,
          ),
          const SizedBox(height: 8),
          _LanguageOption(
            title: 'English',
            subtitle: 'English Language',
            value: 'en',
            groupValue: currentLocale.languageCode,
            onChanged: changeLanguage,
          ),
        ],
      ),
    );
  }
}

class _LanguageOption extends StatelessWidget {
  final String title;
  final String subtitle;
  final String value;
  final String groupValue;
  final ValueChanged<String> onChanged;

  const _LanguageOption({
    required this.title,
    required this.subtitle,
    required this.value,
    required this.groupValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = value == groupValue;
    final theme = Theme.of(context);

    return Card(
      elevation: isSelected ? 2 : 0,
      color: isSelected ? theme.colorScheme.primaryContainer : null,
      child: RadioListTile<String>(
        title: Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: isSelected ? FontWeight.bold : null,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected 
              ? theme.colorScheme.primary 
              : theme.textTheme.bodyMedium?.color,
          ),
        ),
        value: value,
        groupValue: groupValue,
        onChanged: (value) {
          if (value != null) {
            onChanged(value);
          }
        },
      ),
    );
  }
}
