-- 🚀 استعلامات الترتيب المحسنة لنظام Moon Memory
-- Optimized Sorting Queries for Moon Memory System
-- Date: 2025-01-16
-- Version: 2.0 - Performance Optimized

-- ===== 📊 دالة الترتيب المحسنة للصور =====
CREATE OR REPLACE FUNCTION get_photos_sorted_optimized(
    p_sort_by TEXT DEFAULT 'location_date',
    p_location_type TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    file_name TEXT,
    storage_path TEXT,
    image_url TEXT,
    file_size_bytes BIGINT,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    sort_order INTEGER,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.file_name,
        p.storage_path,
        p.image_url,
        p.file_size_bytes,
        p.full_location_code as location_code,
        p.location_type,
        p.location_number,
        p.username,
        p.capture_timestamp,
        p.upload_timestamp,
        p.sort_order,
        COALESCE(l.sort_order, 999) as location_sort_order,
        ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
    FROM public.photos p
    LEFT JOIN public.locations l ON l.location_code = p.full_location_code
    WHERE 
        (p_location_type IS NULL OR p.location_type = p_location_type)
        AND (p_user_id IS NULL OR p.user_id = p_user_id)
        AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
        AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
        AND p.status = 'active'
    ORDER BY 
        -- ترتيب محسن بدون استخدام ROW()::TEXT
        CASE WHEN p_sort_by = 'location_date' THEN p.location_type END,
        CASE WHEN p_sort_by = 'location_date' THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by = 'location_date' THEN p.capture_timestamp END DESC,
        
        CASE WHEN p_sort_by = 'date_location' THEN p.capture_timestamp END DESC,
        CASE WHEN p_sort_by = 'date_location' THEN p.location_type END,
        CASE WHEN p_sort_by = 'date_location' THEN COALESCE(l.sort_order, 999) END,
        
        CASE WHEN p_sort_by = 'user_date' THEN p.username END,
        CASE WHEN p_sort_by = 'user_date' THEN p.capture_timestamp END DESC,
        
        CASE WHEN p_sort_by = 'size_desc' THEN p.file_size_bytes END DESC,
        CASE WHEN p_sort_by = 'size_desc' THEN p.capture_timestamp END DESC,
        
        -- ترتيب مختلط محسن
        CASE WHEN p_sort_by = 'mixed' THEN p.location_type END,
        CASE WHEN p_sort_by = 'mixed' THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by = 'mixed' THEN DATE_TRUNC('day', p.capture_timestamp) END DESC,
        CASE WHEN p_sort_by = 'mixed' THEN p.username END,
        CASE WHEN p_sort_by = 'mixed' THEN p.capture_timestamp END DESC,
        
        -- الافتراضي
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'mixed') 
             THEN p.location_type END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'mixed') 
             THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'mixed') 
             THEN p.capture_timestamp END DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 🎥 دالة الترتيب المحسنة للفيديوهات =====
CREATE OR REPLACE FUNCTION get_videos_sorted_optimized(
    p_sort_by TEXT DEFAULT 'location_date',
    p_location_type TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    file_name TEXT,
    storage_path TEXT,
    video_url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    sort_order INTEGER,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2),
    duration_formatted TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.id,
        v.file_name,
        v.storage_path,
        v.video_url,
        v.file_size_bytes,
        v.duration_seconds,
        v.full_location_code as location_code,
        v.location_type,
        v.location_number,
        v.username,
        v.capture_timestamp,
        v.upload_timestamp,
        v.sort_order,
        COALESCE(l.sort_order, 999) as location_sort_order,
        ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb,
        CASE 
            WHEN v.duration_seconds IS NULL THEN 'غير محدد'
            WHEN v.duration_seconds < 60 THEN v.duration_seconds || ' ثانية'
            WHEN v.duration_seconds < 3600 THEN 
                (v.duration_seconds / 60) || ':' || 
                LPAD((v.duration_seconds % 60)::TEXT, 2, '0') || ' دقيقة'
            ELSE 
                (v.duration_seconds / 3600) || ':' || 
                LPAD(((v.duration_seconds % 3600) / 60)::TEXT, 2, '0') || ':' ||
                LPAD((v.duration_seconds % 60)::TEXT, 2, '0') || ' ساعة'
        END as duration_formatted
    FROM public.videos v
    LEFT JOIN public.locations l ON l.location_code = v.full_location_code
    WHERE 
        (p_location_type IS NULL OR v.location_type = p_location_type)
        AND (p_user_id IS NULL OR v.user_id = p_user_id)
        AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
        AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
        AND v.status = 'active'
    ORDER BY 
        -- ترتيب محسن للفيديوهات
        CASE WHEN p_sort_by = 'location_date' THEN v.location_type END,
        CASE WHEN p_sort_by = 'location_date' THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by = 'location_date' THEN v.capture_timestamp END DESC,
        
        CASE WHEN p_sort_by = 'date_location' THEN v.capture_timestamp END DESC,
        CASE WHEN p_sort_by = 'date_location' THEN v.location_type END,
        CASE WHEN p_sort_by = 'date_location' THEN COALESCE(l.sort_order, 999) END,
        
        CASE WHEN p_sort_by = 'user_date' THEN v.username END,
        CASE WHEN p_sort_by = 'user_date' THEN v.capture_timestamp END DESC,
        
        CASE WHEN p_sort_by = 'size_desc' THEN v.file_size_bytes END DESC,
        CASE WHEN p_sort_by = 'size_desc' THEN v.capture_timestamp END DESC,
        
        -- ترتيب خاص بالفيديوهات: حسب المدة
        CASE WHEN p_sort_by = 'duration_desc' THEN v.duration_seconds END DESC,
        CASE WHEN p_sort_by = 'duration_desc' THEN v.capture_timestamp END DESC,
        
        CASE WHEN p_sort_by = 'duration_asc' THEN v.duration_seconds END ASC,
        CASE WHEN p_sort_by = 'duration_asc' THEN v.capture_timestamp END DESC,
        
        -- ترتيب مختلط محسن
        CASE WHEN p_sort_by = 'mixed' THEN v.location_type END,
        CASE WHEN p_sort_by = 'mixed' THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by = 'mixed' THEN DATE_TRUNC('day', v.capture_timestamp) END DESC,
        CASE WHEN p_sort_by = 'mixed' THEN v.username END,
        CASE WHEN p_sort_by = 'mixed' THEN v.capture_timestamp END DESC,
        
        -- الافتراضي
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'duration_desc', 'duration_asc', 'mixed') 
             THEN v.location_type END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'duration_desc', 'duration_asc', 'mixed') 
             THEN COALESCE(l.sort_order, 999) END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'duration_desc', 'duration_asc', 'mixed') 
             THEN v.capture_timestamp END DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ===== 📊 دالة الترتيب المختلط المحسنة =====
CREATE OR REPLACE FUNCTION get_media_mixed_sorted_optimized(
    p_sort_by TEXT DEFAULT 'location_date',
    p_location_type TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_media_type TEXT DEFAULT 'all',
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    media_type TEXT,
    file_name TEXT,
    storage_path TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location_code TEXT,
    location_type TEXT,
    location_number TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE,
    location_sort_order INTEGER,
    file_size_mb DECIMAL(10,2)
) AS $$
BEGIN
    -- استخدام CTE محسن بدلاً من UNION ALL المعقد
    RETURN QUERY
    WITH combined_media AS (
        -- الصور مع تحسين الاستعلام
        SELECT
            p.id,
            'photo'::TEXT as media_type,
            p.file_name,
            p.storage_path,
            p.image_url as url,
            p.file_size_bytes,
            NULL::INTEGER as duration_seconds,
            p.full_location_code as location_code,
            p.location_type,
            p.location_number,
            p.username,
            p.capture_timestamp,
            p.upload_timestamp,
            COALESCE(l.sort_order, 999) as location_sort_order,
            ROUND((p.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
        FROM public.photos p
        LEFT JOIN public.locations l ON l.location_code = p.full_location_code
        WHERE
            (p_media_type = 'all' OR p_media_type = 'photos')
            AND (p_location_type IS NULL OR p.location_type = p_location_type)
            AND (p_user_id IS NULL OR p.user_id = p_user_id)
            AND (p_date_from IS NULL OR p.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR p.capture_timestamp <= p_date_to)
            AND p.status = 'active'

        UNION ALL

        -- الفيديوهات مع تحسين الاستعلام
        SELECT
            v.id,
            'video'::TEXT as media_type,
            v.file_name,
            v.storage_path,
            v.video_url as url,
            v.file_size_bytes,
            v.duration_seconds,
            v.full_location_code as location_code,
            v.location_type,
            v.location_number,
            v.username,
            v.capture_timestamp,
            v.upload_timestamp,
            COALESCE(l.sort_order, 999) as location_sort_order,
            ROUND((v.file_size_bytes::DECIMAL / 1024 / 1024), 2) as file_size_mb
        FROM public.videos v
        LEFT JOIN public.locations l ON l.location_code = v.full_location_code
        WHERE
            (p_media_type = 'all' OR p_media_type = 'videos')
            AND (p_location_type IS NULL OR v.location_type = p_location_type)
            AND (p_user_id IS NULL OR v.user_id = p_user_id)
            AND (p_date_from IS NULL OR v.capture_timestamp >= p_date_from)
            AND (p_date_to IS NULL OR v.capture_timestamp <= p_date_to)
            AND v.status = 'active'
    )
    SELECT * FROM combined_media
    ORDER BY
        -- ترتيب محسن بدون ROW()::TEXT
        CASE WHEN p_sort_by = 'location_date' THEN location_type END,
        CASE WHEN p_sort_by = 'location_date' THEN location_sort_order END,
        CASE WHEN p_sort_by = 'location_date' THEN capture_timestamp END DESC,

        CASE WHEN p_sort_by = 'date_location' THEN capture_timestamp END DESC,
        CASE WHEN p_sort_by = 'date_location' THEN location_type END,
        CASE WHEN p_sort_by = 'date_location' THEN location_sort_order END,

        CASE WHEN p_sort_by = 'user_date' THEN username END,
        CASE WHEN p_sort_by = 'user_date' THEN capture_timestamp END DESC,

        CASE WHEN p_sort_by = 'size_desc' THEN file_size_bytes END DESC,
        CASE WHEN p_sort_by = 'size_desc' THEN capture_timestamp END DESC,

        CASE WHEN p_sort_by = 'type_location_date' THEN media_type END,
        CASE WHEN p_sort_by = 'type_location_date' THEN location_type END,
        CASE WHEN p_sort_by = 'type_location_date' THEN location_sort_order END,
        CASE WHEN p_sort_by = 'type_location_date' THEN capture_timestamp END DESC,

        -- الافتراضي
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'type_location_date')
             THEN location_type END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'type_location_date')
             THEN location_sort_order END,
        CASE WHEN p_sort_by NOT IN ('location_date', 'date_location', 'user_date', 'size_desc', 'type_location_date')
             THEN capture_timestamp END DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;
