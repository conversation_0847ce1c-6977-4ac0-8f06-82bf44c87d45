-- دوال الإدارة المتقدمة لتطبيق Moon Memory
-- Advanced Admin Functions for Moon Memory App
-- Date: 2025-01-15

-- ===== دوال إدارة المستخدمين =====

-- دالة إنشاء مستخدم جديد
CREATE OR REPLACE FUNCTION create_user(
    p_national_id TEXT,
    p_full_name TEXT,
    p_email TEXT DEFAULT NULL,
    p_phone TEXT DEFAULT NULL,
    p_password TEXT DEFAULT NULL,
    p_department TEXT DEFAULT NULL,
    p_position TEXT DEFAULT NULL,
    p_max_devices INTEGER DEFAULT 3,
    p_storage_quota_mb INTEGER DEFAULT 1000
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
    generated_email TEXT;
    generated_password TEXT;
    auth_user_data JSON;
BEGIN
    -- التحقق من صحة الرقم الوطني
    IF LENGTH(p_national_id) != 10 OR p_national_id !~ '^\d{10}$' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'رقم الهوية يجب أن يكون 10 أرقام'
        );
    END IF;
    
    -- التحقق من عدم وجود المستخدم
    IF EXISTS (SELECT 1 FROM users WHERE national_id = p_national_id) THEN
        RETURN json_build_object(
            'success', false,
            'error', 'مستخدم بهذا الرقم الوطني موجود بالفعل'
        );
    END IF;
    
    -- إنشاء البريد الإلكتروني إذا لم يتم توفيره
    generated_email := COALESCE(p_email, p_national_id || '@moon-memory.com');
    
    -- إنشاء كلمة مرور إذا لم يتم توفيرها
    generated_password := COALESCE(p_password, p_national_id);
    
    -- إنشاء المستخدم في Auth (محاكاة - يجب استخدام Supabase Admin API)
    -- هذا مثال على البيانات المطلوبة
    auth_user_data := json_build_object(
        'email', generated_email,
        'password', generated_password,
        'user_metadata', json_build_object(
            'full_name', p_full_name,
            'national_id', p_national_id
        )
    );
    
    -- إنشاء UUID جديد للمستخدم
    new_user_id := gen_random_uuid();
    
    -- إدراج المستخدم في جدول users
    INSERT INTO users (
        id,
        national_id,
        full_name,
        email,
        phone,
        department,
        position,
        max_devices,
        storage_quota_mb,
        created_by
    ) VALUES (
        new_user_id,
        p_national_id,
        p_full_name,
        generated_email,
        p_phone,
        p_department,
        p_position,
        p_max_devices,
        p_storage_quota_mb,
        auth.uid()
    );
    
    RETURN json_build_object(
        'success', true,
        'user_id', new_user_id,
        'email', generated_email,
        'password', generated_password,
        'message', 'تم إنشاء المستخدم بنجاح'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في إنشاء المستخدم: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة إعادة تعيين كلمة المرور
CREATE OR REPLACE FUNCTION reset_user_password(
    p_user_id UUID,
    p_new_password TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    user_record RECORD;
    generated_password TEXT;
BEGIN
    -- التحقق من وجود المستخدم
    SELECT * INTO user_record FROM users WHERE id = p_user_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'المستخدم غير موجود'
        );
    END IF;
    
    -- إنشاء كلمة مرور جديدة إذا لم يتم توفيرها
    generated_password := COALESCE(p_new_password, user_record.national_id);
    
    -- تحديث كلمة المرور في Auth (محاكاة)
    -- يجب استخدام Supabase Admin API
    
    -- تحديث تاريخ تغيير كلمة المرور
    UPDATE users 
    SET password_changed_at = NOW()
    WHERE id = p_user_id;
    
    RETURN json_build_object(
        'success', true,
        'new_password', generated_password,
        'message', 'تم إعادة تعيين كلمة المرور بنجاح'
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في إعادة تعيين كلمة المرور: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تفعيل/إيقاف المستخدم
CREATE OR REPLACE FUNCTION toggle_user_status(
    p_user_id UUID,
    p_is_active BOOLEAN
)
RETURNS JSON AS $$
DECLARE
    user_record RECORD;
BEGIN
    -- التحقق من وجود المستخدم
    SELECT * INTO user_record FROM users WHERE id = p_user_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'المستخدم غير موجود'
        );
    END IF;
    
    -- منع إيقاف المشرف لنفسه
    IF p_user_id = auth.uid() AND NOT p_is_active THEN
        RETURN json_build_object(
            'success', false,
            'error', 'لا يمكن إيقاف حسابك الخاص'
        );
    END IF;
    
    -- تحديث حالة المستخدم
    UPDATE users 
    SET is_active = p_is_active
    WHERE id = p_user_id;
    
    -- إيقاف جميع أجهزة المستخدم إذا تم إيقاف الحساب
    IF NOT p_is_active THEN
        UPDATE devices 
        SET is_active = FALSE
        WHERE user_id = p_user_id;
        
        -- إنهاء جميع الجلسات النشطة
        UPDATE user_sessions 
        SET is_active = FALSE, ended_at = NOW(), end_reason = 'admin_disabled'
        WHERE user_id = p_user_id AND is_active = TRUE;
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'message', CASE 
            WHEN p_is_active THEN 'تم تفعيل المستخدم بنجاح'
            ELSE 'تم إيقاف المستخدم بنجاح'
        END
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في تغيير حالة المستخدم: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الحصول على قائمة المستخدمين مع الإحصائيات
CREATE OR REPLACE FUNCTION get_users_list(
    p_page INTEGER DEFAULT 1,
    p_limit INTEGER DEFAULT 20,
    p_search TEXT DEFAULT NULL,
    p_status TEXT DEFAULT 'all' -- 'all', 'active', 'inactive'
)
RETURNS JSON AS $$
DECLARE
    users_data JSON;
    total_count INTEGER;
    offset_value INTEGER;
BEGIN
    offset_value := (p_page - 1) * p_limit;
    
    -- حساب العدد الإجمالي
    SELECT COUNT(*) INTO total_count
    FROM users u
    WHERE (p_search IS NULL OR 
           u.full_name ILIKE '%' || p_search || '%' OR 
           u.national_id ILIKE '%' || p_search || '%' OR
           u.email ILIKE '%' || p_search || '%')
    AND (p_status = 'all' OR 
         (p_status = 'active' AND u.is_active = TRUE) OR
         (p_status = 'inactive' AND u.is_active = FALSE));
    
    -- جلب البيانات
    SELECT json_agg(
        json_build_object(
            'id', u.id,
            'national_id', u.national_id,
            'full_name', u.full_name,
            'email', u.email,
            'phone', u.phone,
            'department', u.department,
            'position', u.position,
            'is_active', u.is_active,
            'is_admin', u.is_admin,
            'account_type', u.account_type,
            'max_devices', u.max_devices,
            'storage_quota_mb', u.storage_quota_mb,
            'last_login', u.last_login,
            'created_at', u.created_at,
            'device_count', COALESCE(d.device_count, 0),
            'active_devices', COALESCE(d.active_devices, 0),
            'photo_count', COALESCE(p.photo_count, 0),
            'video_count', COALESCE(v.video_count, 0),
            'storage_used_mb', COALESCE(
                (p.photos_size + v.videos_size) / 1024 / 1024, 0
            )
        ) ORDER BY u.created_at DESC
    ) INTO users_data
    FROM users u
    LEFT JOIN (
        SELECT user_id, 
               COUNT(*) as device_count,
               COUNT(*) FILTER (WHERE is_active = TRUE) as active_devices
        FROM devices 
        GROUP BY user_id
    ) d ON u.id = d.user_id
    LEFT JOIN (
        SELECT user_id, 
               COUNT(*) as photo_count,
               COALESCE(SUM(file_size_bytes), 0) as photos_size
        FROM photos 
        WHERE status = 'active'
        GROUP BY user_id
    ) p ON u.id = p.user_id
    LEFT JOIN (
        SELECT user_id, 
               COUNT(*) as video_count,
               COALESCE(SUM(file_size_bytes), 0) as videos_size
        FROM videos 
        WHERE status = 'active'
        GROUP BY user_id
    ) v ON u.id = v.user_id
    WHERE (p_search IS NULL OR 
           u.full_name ILIKE '%' || p_search || '%' OR 
           u.national_id ILIKE '%' || p_search || '%' OR
           u.email ILIKE '%' || p_search || '%')
    AND (p_status = 'all' OR 
         (p_status = 'active' AND u.is_active = TRUE) OR
         (p_status = 'inactive' AND u.is_active = FALSE))
    LIMIT p_limit OFFSET offset_value;
    
    RETURN json_build_object(
        'success', true,
        'data', COALESCE(users_data, '[]'::json),
        'pagination', json_build_object(
            'page', p_page,
            'limit', p_limit,
            'total', total_count,
            'pages', CEIL(total_count::DECIMAL / p_limit)
        )
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في جلب قائمة المستخدمين: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== دوال إدارة الصور والفيديو =====

-- دالة الحصول على الصور مع الترتيب المطلوب
CREATE OR REPLACE FUNCTION get_photos_admin(
    p_page INTEGER DEFAULT 1,
    p_limit INTEGER DEFAULT 50,
    p_location_type TEXT DEFAULT NULL,
    p_location_number TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    photos_data JSON;
    total_count INTEGER;
    offset_value INTEGER;
BEGIN
    offset_value := (p_page - 1) * p_limit;

    -- حساب العدد الإجمالي
    SELECT COUNT(*) INTO total_count
    FROM photos p
    JOIN users u ON p.user_id = u.id
    WHERE p.status = 'active'
    AND (p_location_type = p_location_type OR p_location_type IS NULL)
    AND (p_location_number = p_location_number OR p_location_number IS NULL)
    AND (p_user_id = p.user_id OR p_user_id IS NULL)
    AND (p_date_from IS NULL OR DATE(p.capture_timestamp) >= p_date_from)
    AND (p_date_to IS NULL OR DATE(p.capture_timestamp) <= p_date_to);

    -- جلب البيانات مع الترتيب المطلوب
    SELECT json_agg(
        json_build_object(
            'id', p.id,
            'file_name', p.file_name,
            'image_url', p.image_url,
            'url', p.url,
            'location', p.location,
            'location_type', p.location_type,
            'location_number', p.location_number,
            'full_location_code', p.full_location_code,
            'username', p.username,
            'user_full_name', u.full_name,
            'user_national_id', u.national_id,
            'capture_timestamp', p.capture_timestamp,
            'upload_timestamp', p.upload_timestamp,
            'sort_order', p.sort_order,
            'file_size_bytes', p.file_size_bytes,
            'tags', p.tags,
            'description', p.description,
            'created_at', p.created_at
        ) ORDER BY
            p.location_type ASC,
            p.location_number ASC,
            p.capture_timestamp ASC,
            p.username ASC
    ) INTO photos_data
    FROM photos p
    JOIN users u ON p.user_id = u.id
    WHERE p.status = 'active'
    AND (p_location_type = p_location_type OR p_location_type IS NULL)
    AND (p_location_number = p_location_number OR p_location_number IS NULL)
    AND (p_user_id = p.user_id OR p_user_id IS NULL)
    AND (p_date_from IS NULL OR DATE(p.capture_timestamp) >= p_date_from)
    AND (p_date_to IS NULL OR DATE(p.capture_timestamp) <= p_date_to)
    LIMIT p_limit OFFSET offset_value;

    RETURN json_build_object(
        'success', true,
        'data', COALESCE(photos_data, '[]'::json),
        'pagination', json_build_object(
            'page', p_page,
            'limit', p_limit,
            'total', total_count,
            'pages', CEIL(total_count::DECIMAL / p_limit)
        )
    );

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في جلب الصور: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الحصول على الفيديو مع الترتيب المطلوب
CREATE OR REPLACE FUNCTION get_videos_admin(
    p_page INTEGER DEFAULT 1,
    p_limit INTEGER DEFAULT 50,
    p_location_type TEXT DEFAULT NULL,
    p_location_number TEXT DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    videos_data JSON;
    total_count INTEGER;
    offset_value INTEGER;
BEGIN
    offset_value := (p_page - 1) * p_limit;

    -- حساب العدد الإجمالي
    SELECT COUNT(*) INTO total_count
    FROM videos v
    JOIN users u ON v.user_id = u.id
    WHERE v.status = 'active'
    AND (p_location_type IS NULL OR v.location_type = p_location_type)
    AND (p_location_number IS NULL OR v.location_number = p_location_number)
    AND (p_user_id IS NULL OR v.user_id = p_user_id)
    AND (p_date_from IS NULL OR DATE(v.capture_timestamp) >= p_date_from)
    AND (p_date_to IS NULL OR DATE(v.capture_timestamp) <= p_date_to);

    -- جلب البيانات مع الترتيب المطلوب
    SELECT json_agg(
        json_build_object(
            'id', v.id,
            'file_name', v.file_name,
            'video_url', v.video_url,
            'url', v.url,
            'location', v.location,
            'location_type', v.location_type,
            'location_number', v.location_number,
            'full_location_code', v.full_location_code,
            'username', v.username,
            'user_full_name', u.full_name,
            'user_national_id', u.national_id,
            'capture_timestamp', v.capture_timestamp,
            'upload_timestamp', v.upload_timestamp,
            'sort_order', v.sort_order,
            'duration_seconds', v.duration_seconds,
            'file_size_bytes', v.file_size_bytes,
            'resolution', v.resolution,
            'fps', v.fps,
            'tags', v.tags,
            'description', v.description,
            'thumbnail_url', v.thumbnail_url,
            'created_at', v.created_at
        ) ORDER BY
            v.location_type ASC,
            v.location_number ASC,
            v.capture_timestamp ASC,
            v.username ASC
    ) INTO videos_data
    FROM videos v
    JOIN users u ON v.user_id = u.id
    WHERE v.status = 'active'
    AND (p_location_type IS NULL OR v.location_type = p_location_type)
    AND (p_location_number IS NULL OR v.location_number = p_location_number)
    AND (p_user_id IS NULL OR v.user_id = p_user_id)
    AND (p_date_from IS NULL OR DATE(v.capture_timestamp) >= p_date_from)
    AND (p_date_to IS NULL OR DATE(v.capture_timestamp) <= p_date_to)
    LIMIT p_limit OFFSET offset_value;

    RETURN json_build_object(
        'success', true,
        'data', COALESCE(videos_data, '[]'::json),
        'pagination', json_build_object(
            'page', p_page,
            'limit', p_limit,
            'total', total_count,
            'pages', CEIL(total_count::DECIMAL / p_limit)
        )
    );

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في جلب الفيديو: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الحصول على الإحصائيات الشاملة
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    stats_data JSON;
BEGIN
    SELECT json_build_object(
        'users', json_build_object(
            'total', (SELECT COUNT(*) FROM users),
            'active', (SELECT COUNT(*) FROM users WHERE is_active = TRUE),
            'inactive', (SELECT COUNT(*) FROM users WHERE is_active = FALSE),
            'admins', (SELECT COUNT(*) FROM users WHERE is_admin = TRUE),
            'new_today', (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURRENT_DATE),
            'new_this_week', (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'),
            'new_this_month', (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE - INTERVAL '30 days')
        ),
        'devices', json_build_object(
            'total', (SELECT COUNT(*) FROM devices),
            'active', (SELECT COUNT(*) FROM devices WHERE is_active = TRUE),
            'blocked', (SELECT COUNT(*) FROM devices WHERE is_blocked = TRUE),
            'high_trust', (SELECT COUNT(*) FROM devices WHERE trust_level = 'high'),
            'medium_trust', (SELECT COUNT(*) FROM devices WHERE trust_level = 'medium'),
            'low_trust', (SELECT COUNT(*) FROM devices WHERE trust_level = 'low')
        ),
        'photos', json_build_object(
            'total', (SELECT COUNT(*) FROM photos WHERE status = 'active'),
            'today', (SELECT COUNT(*) FROM photos WHERE DATE(created_at) = CURRENT_DATE),
            'this_week', (SELECT COUNT(*) FROM photos WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'),
            'this_month', (SELECT COUNT(*) FROM photos WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'),
            'u_locations', (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'U' AND status = 'active'),
            'c_locations', (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'C' AND status = 'active'),
            'total_size_mb', (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active')
        ),
        'videos', json_build_object(
            'total', (SELECT COUNT(*) FROM videos WHERE status = 'active'),
            'today', (SELECT COUNT(*) FROM videos WHERE DATE(created_at) = CURRENT_DATE),
            'this_week', (SELECT COUNT(*) FROM videos WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'),
            'this_month', (SELECT COUNT(*) FROM videos WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'),
            'total_duration_hours', (SELECT COALESCE(SUM(duration_seconds), 0) / 3600 FROM videos WHERE status = 'active'),
            'total_size_mb', (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active')
        ),
        'storage', json_build_object(
            'total_used_mb', (
                SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024
                FROM (
                    SELECT file_size_bytes FROM photos WHERE status = 'active'
                    UNION ALL
                    SELECT file_size_bytes FROM videos WHERE status = 'active'
                ) combined
            ),
            'photos_mb', (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM photos WHERE status = 'active'),
            'videos_mb', (SELECT COALESCE(SUM(file_size_bytes), 0) / 1024 / 1024 FROM videos WHERE status = 'active')
        ),
        'activity', json_build_object(
            'active_sessions', (SELECT COUNT(*) FROM user_sessions WHERE is_active = TRUE),
            'logins_today', (SELECT COUNT(*) FROM user_sessions WHERE DATE(started_at) = CURRENT_DATE),
            'last_photo_upload', (SELECT MAX(created_at) FROM photos),
            'last_video_upload', (SELECT MAX(created_at) FROM videos),
            'last_user_login', (SELECT MAX(last_login) FROM users)
        )
    ) INTO stats_data;

    RETURN json_build_object(
        'success', true,
        'data', stats_data,
        'generated_at', NOW()
    );

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', 'خطأ في جلب الإحصائيات: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
