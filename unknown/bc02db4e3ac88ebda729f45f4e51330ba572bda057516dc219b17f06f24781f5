import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class VideoWatermarkOverlay extends StatelessWidget {
  final String username;
  final String location;
  final String? geoLocation;
  final Size videoSize;

  const VideoWatermarkOverlay({
    super.key,
    required this.username,
    required this.location,
    this.geoLocation,
    required this.videoSize,
  });

  String _formatDate(DateTime date) {
    final List<String> arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];  
    return '${date.day} ${arabicMonths[date.month - 1]} ${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour % 12 == 0 ? 12 : time.hour % 12;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.hour < 12 ? 'ص' : 'م';
    final dayNightIndicator = time.hour >= 6 && time.hour < 18 ? '☀️' : '🌙';
    return '$hour:$minute $period $dayNightIndicator';
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final dateStr = _formatDate(now);
    final timeStr = _formatTime(now);

    // أحجام مضبوطة يدوياً لتناسب الفيديو
    final double iconSize = videoSize.width * 0.08;    // أصغر من الصور
    const double locationFontSize = 14.0;              // حجم مناسب للفيديو
    const double dateFontSize = 16.0;                  // حجم مناسب للفيديو
    const double timeFontSize = 18.0;                  // حجم مناسب للفيديو
    const double usernameFontSize = 13.0;              // حجم مناسب للفيديو
    const double geoLocationFontSize = 12.0;           // حجم مناسب للموقع الجغرافي





    return Stack(
      children: [
        // الشعار الأيمن
        Positioned(
          top: 16,
          right: 16,
          child: Image.asset(
            'assets/images/icon_right.png',
            width: iconSize,
            height: iconSize,
          ),
        ),

        // الشعار الأيسر
        Positioned(
          top: 16,
          left: 16,
          child: Image.asset(
            'assets/images/icon_left.png',
            width: iconSize,
            height: iconSize,
          ),
        ),

        // معلومات الموقع والتاريخ والوقت (أقصى أسفل اليمين)
        Positioned(
          bottom: 24,
          right: 24,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              // موقع التصوير (في الأعلى)
              Text(
                location,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: locationFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 3,
                      offset: Offset(1, 1),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // الموقع الجغرافي (تحت موقع التصوير)
              if (geoLocation != null && geoLocation!.isNotEmpty && geoLocation != 'جاري تحديد الموقع...')
                Text(
                  geoLocation!,
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: geoLocationFontSize, // حجم ثابت للموقع الجغرافي
                    fontWeight: FontWeight.w500,
                    shadows: const [
                      Shadow(
                        color: Colors.black,
                        blurRadius: 3,
                        offset: Offset(1, 1),
                      ),
                    ],
                  ),
                  textDirection: TextDirection.rtl,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

              // التاريخ
              Text(
                dateStr,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: dateFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 3,
                      offset: Offset(1, 1),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
              ),

              // الوقت
              Text(
                timeStr,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: timeFontSize,
                  fontWeight: FontWeight.bold,
                  shadows: const [
                    Shadow(
                      color: Colors.black,
                      blurRadius: 3,
                      offset: Offset(1, 1),
                    ),
                  ],
                ),
                textDirection: TextDirection.rtl,
              ),
            ],
          ),
        ),

        // اسم المستخدم (أقصى أسفل اليسار)
        Positioned(
          bottom: 24,
          left: 24,
          child: Text(
            username,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: usernameFontSize,
              fontWeight: FontWeight.bold,
              shadows: const [
                Shadow(
                  color: Colors.black,
                  blurRadius: 3,
                  offset: Offset(1, 1),
                ),
              ],
            ),
            textDirection: TextDirection.rtl,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
