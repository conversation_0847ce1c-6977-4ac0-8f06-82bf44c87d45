-- إعد<PERSON> قاعدة البيانات الأساسية - الحد الأدنى
-- Minimal Database Setup
-- Date: 2025-01-15

-- ===== إنشاء جدول المستخدمين =====
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user',
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    department TEXT,
    position TEXT,
    notes TEXT,
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUI<PERSON>
);

-- ===== إنشاء جدول الأجهزة =====
CREATE TABLE IF NOT EXISTS devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_fingerprint TEXT NOT NULL,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    confidence_score DECIMAL(5,2) DEFAULT 0,
    trust_level TEXT DEFAULT 'untrusted',
    auth_attempts INTEGER DEFAULT 0,
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    last_verified_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الصور =====
CREATE TABLE IF NOT EXISTS photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الفيديو =====
CREATE TABLE IF NOT EXISTS videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    resolution TEXT,
    fps INTEGER,
    codec TEXT,
    bitrate INTEGER,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    thumbnail_url TEXT,
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الجلسات =====
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== إنشاء جدول سجلات الإدارة =====
CREATE TABLE IF NOT EXISTS admin_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) NOT NULL,
    target_user_id UUID REFERENCES users(id),
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id UUID,
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء جدول الإحصائيات =====
CREATE TABLE IF NOT EXISTS system_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_date DATE DEFAULT CURRENT_DATE,
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users_today INTEGER DEFAULT 0,
    total_devices INTEGER DEFAULT 0,
    active_devices INTEGER DEFAULT 0,
    blocked_devices INTEGER DEFAULT 0,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    photos_uploaded_today INTEGER DEFAULT 0,
    videos_uploaded_today INTEGER DEFAULT 0,
    total_storage_used_mb BIGINT DEFAULT 0,
    photos_storage_mb BIGINT DEFAULT 0,
    videos_storage_mb BIGINT DEFAULT 0,
    u_locations_used INTEGER DEFAULT 0,
    c_locations_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- ===== رسائل النجاح =====
SELECT 'تم إنشاء الجداول الأساسية بنجاح! ✅' as status;

-- عرض الجداول المُنشأة
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('users', 'devices', 'photos', 'videos', 'user_sessions', 'admin_logs', 'system_stats')
ORDER BY table_name;

SELECT 'النظام الأساسي جاهز! 🚀' as final_message;
