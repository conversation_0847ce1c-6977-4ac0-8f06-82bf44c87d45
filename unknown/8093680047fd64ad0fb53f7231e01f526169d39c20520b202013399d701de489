import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';

class GalleryScreen extends ConsumerStatefulWidget {
  const GalleryScreen({super.key});

  @override
  ConsumerState<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends ConsumerState<GalleryScreen> {
  List<FileSystemEntity> _mediaFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMediaFiles();
  }

  Future<void> _loadMediaFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      List<FileSystemEntity> allFiles = [];

      // تحميل الملفات من مجلد gallery (المعرض الرئيسي)
      final galleryDir = Directory('${directory.path}/gallery');
      if (await galleryDir.exists()) {
        final galleryFiles = await galleryDir.list().toList();
        allFiles.addAll(galleryFiles);
      }

      // تحميل الملفات من مجلد offline_media (الملفات المحفوظة بدون إنترنت - للتوافق مع النظام القديم)
      final offlineDir = Directory('${directory.path}/offline_media');
      if (await offlineDir.exists()) {
        final offlineFiles = await offlineDir.list().toList();
        allFiles.addAll(offlineFiles);
      }

      // ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
      allFiles.sort((a, b) {
        final aStat = a.statSync();
        final bStat = b.statSync();
        return bStat.modified.compareTo(aStat.modified);
      });

      setState(() {
        _mediaFiles = allFiles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isVideo(String path) {
    return path.toLowerCase().endsWith('.mp4') || 
           path.toLowerCase().endsWith('.mov') ||
           path.toLowerCase().endsWith('.avi');
  }

  bool _isImage(String path) {
    return path.toLowerCase().endsWith('.jpg') ||
           path.toLowerCase().endsWith('.jpeg') ||
           path.toLowerCase().endsWith('.png');
  }

  bool _isOfflineFile(String path) {
    return path.contains('/offline_media/');
  }

  bool _isGalleryFile(String path) {
    return path.contains('/gallery/');
  }

  IconData _getUploadStatusIcon(String path) {
    if (_isOfflineFile(path)) {
      return Icons.cloud_upload; // ملف محفوظ بدون إنترنت
    } else if (_isGalleryFile(path)) {
      return Icons.cloud_queue; // ملف في المعرض، قيد الرفع
    } else {
      return Icons.cloud_done; // ملف مرفوع
    }
  }

  Color _getUploadStatusColor(String path) {
    if (_isOfflineFile(path)) {
      return Colors.orange; // ملف محفوظ بدون إنترنت
    } else if (_isGalleryFile(path)) {
      return Colors.blue; // ملف في المعرض، قيد الرفع
    } else {
      return Colors.green; // ملف مرفوع
    }
  }

  Widget _buildMediaItem(FileSystemEntity file) {
    final isVideo = _isVideo(file.path);
    final isImage = _isImage(file.path);
    
    if (!isVideo && !isImage) return const SizedBox.shrink();

    return GestureDetector(
      onTap: () {
        _showMediaPreview(file);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              if (isImage)
                Image.file(
                  File(file.path),
                  fit: BoxFit.cover,
                )
              else if (isVideo)
                Container(
                  color: Colors.black,
                  child: const Center(
                    child: Icon(
                      Icons.play_circle_outline,
                      color: Colors.white,
                      size: 50,
                    ),
                  ),
                ),
              
              // مؤشر نوع الملف
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isVideo ? Icons.videocam : Icons.camera_alt,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      // مؤشر حالة الرفع
                      Icon(
                        _getUploadStatusIcon(file.path),
                        color: _getUploadStatusColor(file.path),
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMediaPreview(FileSystemEntity file) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MediaPreviewScreen(filePath: file.path),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'gallery.title'.tr(),
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFD4AF37),
              ),
            )
          : _mediaFiles.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.photo_library_outlined,
                        size: 80,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'gallery.empty'.tr(),
                        style: GoogleFonts.cairo(
                          color: Colors.grey[400],
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(16),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1,
                    ),
                    itemCount: _mediaFiles.length,
                    itemBuilder: (context, index) {
                      return _buildMediaItem(_mediaFiles[index]);
                    },
                  ),
                ),
    );
  }
}

class MediaPreviewScreen extends StatefulWidget {
  final String filePath;

  const MediaPreviewScreen({super.key, required this.filePath});

  @override
  State<MediaPreviewScreen> createState() => _MediaPreviewScreenState();
}

class _MediaPreviewScreenState extends State<MediaPreviewScreen> {
  VideoPlayerController? _videoController;
  bool _isVideo = false;

  @override
  void initState() {
    super.initState();
    _isVideo = widget.filePath.toLowerCase().endsWith('.mp4') ||
               widget.filePath.toLowerCase().endsWith('.mov') ||
               widget.filePath.toLowerCase().endsWith('.avi');
    
    if (_isVideo) {
      _videoController = VideoPlayerController.file(File(widget.filePath))
        ..initialize().then((_) {
          setState(() {});
        });
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // مشاركة الملف
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () {
              // حذف الملف
            },
          ),
        ],
      ),
      body: Center(
        child: _isVideo
            ? _videoController != null && _videoController!.value.isInitialized
                ? AspectRatio(
                    aspectRatio: _videoController!.value.aspectRatio,
                    child: VideoPlayer(_videoController!),
                  )
                : const CircularProgressIndicator(color: Color(0xFFD4AF37))
            : Image.file(
                File(widget.filePath),
                fit: BoxFit.contain,
              ),
      ),
      floatingActionButton: _isVideo && _videoController != null
          ? FloatingActionButton(
              backgroundColor: const Color(0xFFD4AF37),
              onPressed: () {
                setState(() {
                  if (_videoController!.value.isPlaying) {
                    _videoController!.pause();
                  } else {
                    _videoController!.play();
                  }
                });
              },
              child: Icon(
                _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
              ),
            )
          : null,
    );
  }
}
