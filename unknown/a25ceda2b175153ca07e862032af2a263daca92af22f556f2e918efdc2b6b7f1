-- 🔧 إصلاح السياسات الأمنية - حل مشكلة التكرار اللانهائي
-- Fix Security Policies - Solve Infinite Recursion
-- Date: 2025-01-15

-- ===== 🗑️ حذف السياسات المعطلة =====
DROP POLICY IF EXISTS users_policy ON public.users;
DROP POLICY IF EXISTS devices_policy ON public.devices;
DROP POLICY IF EXISTS photos_policy ON public.photos;
DROP POLICY IF EXISTS videos_policy ON public.videos;
DROP POLICY IF EXISTS sessions_policy ON public.user_sessions;
DROP POLICY IF EXISTS admin_logs_policy ON public.admin_logs;
DROP POLICY IF EXISTS stats_policy ON public.system_stats;

-- ===== 🛡️ إنشاء سياسات مبسطة وآمنة =====

-- سياسة المستخدمين - مبسطة
CREATE POLICY users_simple_policy ON public.users
    FOR ALL
    USING (auth.uid() = id);

-- سياسة الأجهزة - مبسطة
CREATE POLICY devices_simple_policy ON public.devices
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسة الصور - مبسطة
CREATE POLICY photos_simple_policy ON public.photos
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسة الفيديو - مبسطة
CREATE POLICY videos_simple_policy ON public.videos
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسة الجلسات - مبسطة
CREATE POLICY sessions_simple_policy ON public.user_sessions
    FOR ALL
    USING (auth.uid() = user_id);

-- سياسات الإدارة - مفتوحة مؤقتاً للاختبار
CREATE POLICY admin_logs_open_policy ON public.admin_logs
    FOR ALL
    USING (true);

CREATE POLICY stats_open_policy ON public.system_stats
    FOR ALL
    USING (true);

-- ===== 🔧 إنشاء دالة فحص المشرف منفصلة =====
CREATE OR REPLACE FUNCTION is_admin_user(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    admin_status BOOLEAN := FALSE;
BEGIN
    SELECT is_admin INTO admin_status
    FROM public.users
    WHERE id = user_uuid;
    
    RETURN COALESCE(admin_status, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 🔄 إنشاء سياسات متقدمة (اختيارية) =====

-- يمكن استخدام هذه السياسات لاحقاً عندما نحتاج صلاحيات المشرف
/*
-- سياسة المستخدمين مع صلاحيات المشرف
CREATE POLICY users_admin_policy ON public.users
    FOR ALL
    USING (
        auth.uid() = id OR 
        is_admin_user(auth.uid())
    );
*/

-- ===== ✅ اختبار الاتصال =====

-- اختبار بسيط للتأكد من عمل السياسات
SELECT 'تم إصلاح السياسات بنجاح! ✅' as status;

-- اختبار الدالة الجديدة
SELECT 'اختبار دالة فحص المشرف:' as test_info;
SELECT is_admin_user(auth.uid()) as is_current_user_admin;

-- عرض السياسات الحالية
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

SELECT 'النظام جاهز للاختبار مرة أخرى! 🚀' as final_message;
