import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'auto_upload_service.dart';

final systemHealthServiceProvider = Provider<SystemHealthService>((ref) {
  return SystemHealthService();
});

/// خدمة مراقبة صحة النظام والتشخيص
class SystemHealthService {
  final _logger = Logger();
  final _supabase = Supabase.instance.client;
  Timer? _healthCheckTimer;

  /// بدء مراقبة صحة النظام
  void startHealthMonitoring() {
    _logger.i('Starting system health monitoring');
    
    // فحص دوري كل 5 دقائق
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _performHealthCheck();
    });
    
    // فحص فوري عند البدء
    _performHealthCheck();
  }

  /// إيقاف مراقبة صحة النظام
  void stopHealthMonitoring() {
    _logger.i('Stopping system health monitoring');
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  /// إجراء فحص شامل لصحة النظام
  Future<Map<String, dynamic>> _performHealthCheck() async {
    final healthReport = <String, dynamic>{};
    
    try {
      // فحص الاتصال بالإنترنت
      healthReport['internet'] = await _checkInternetConnection();
      
      // فحص الاتصال بـ Supabase
      healthReport['supabase'] = await _checkSupabaseConnection();
      
      // فحص حالة الرفع التلقائي
      healthReport['auto_upload'] = await _checkAutoUploadStatus();
      
      // فحص مساحة التخزين المحلي
      healthReport['storage'] = await _checkLocalStorage();
      
      // فحص الملفات المعلقة
      healthReport['pending_files'] = await _checkPendingFiles();
      
      // فحص حالة المصادقة
      healthReport['auth'] = await _checkAuthStatus();
      
      healthReport['timestamp'] = DateTime.now().toIso8601String();
      healthReport['overall_status'] = _calculateOverallStatus(healthReport);
      
      _logger.i('Health check completed: ${healthReport['overall_status']}');
      
      // حفظ التقرير محلياً
      await _saveHealthReport(healthReport);
      
      return healthReport;
      
    } catch (e) {
      _logger.e('Error during health check: $e');
      healthReport['error'] = e.toString();
      healthReport['overall_status'] = 'error';
      return healthReport;
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<Map<String, dynamic>> _checkInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      final hasConnection = !connectivityResult.contains(ConnectivityResult.none);
      
      if (hasConnection) {
        // اختبار الاتصال الفعلي
        final result = await InternetAddress.lookup('google.com');
        final isReachable = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
        
        return {
          'status': isReachable ? 'connected' : 'limited',
          'type': connectivityResult.first.name,
          'reachable': isReachable,
        };
      } else {
        return {
          'status': 'disconnected',
          'type': 'none',
          'reachable': false,
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'reachable': false,
      };
    }
  }

  /// فحص الاتصال بـ Supabase
  Future<Map<String, dynamic>> _checkSupabaseConnection() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // اختبار بسيط للاتصال
      await _supabase.from('users').select('id').limit(1);
      
      stopwatch.stop();
      
      return {
        'status': 'connected',
        'response_time_ms': stopwatch.elapsedMilliseconds,
        'healthy': stopwatch.elapsedMilliseconds < 5000, // أقل من 5 ثواني
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'healthy': false,
      };
    }
  }

  /// فحص حالة الرفع التلقائي
  Future<Map<String, dynamic>> _checkAutoUploadStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];
      final uploadedFiles = prefs.getStringList('uploaded_files') ?? [];
      
      return {
        'status': 'active',
        'pending_count': pendingUploads.length,
        'uploaded_count': uploadedFiles.length,
        'auto_delete_enabled': AutoUploadService.autoDeleteEnabled,
        'healthy': pendingUploads.length < 100, // أقل من 100 ملف معلق
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'healthy': false,
      };
    }
  }

  /// فحص مساحة التخزين المحلي
  Future<Map<String, dynamic>> _checkLocalStorage() async {
    try {
      final directory = Directory('/data/data/com.example.moon_memory_camera');
      
      if (await directory.exists()) {
        final size = await _calculateDirectorySize(directory);
        
        return {
          'status': 'available',
          'size_mb': (size / 1024 / 1024).round(),
          'healthy': size < 500 * 1024 * 1024, // أقل من 500 ميجا
        };
      } else {
        return {
          'status': 'not_found',
          'healthy': false,
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'healthy': false,
      };
    }
  }

  /// حساب حجم المجلد
  Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          size += await entity.length();
        }
      }
    } catch (e) {
      _logger.w('Error calculating directory size: $e');
    }
    return size;
  }

  /// فحص الملفات المعلقة
  Future<Map<String, dynamic>> _checkPendingFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];
      
      int oldFiles = 0;
      int failedFiles = 0;
      final now = DateTime.now();
      
      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = Map<String, dynamic>.from(
            Map<String, dynamic>.from(
              Map<String, dynamic>.from(uploadJson as Map)
            )
          );
          
          final createdAt = DateTime.parse(uploadData['created_at'] as String);
          final age = now.difference(createdAt);
          
          if (age.inHours > 24) oldFiles++;
          if (uploadData['status'] == 'failed') failedFiles++;
        } catch (e) {
          failedFiles++;
        }
      }
      
      return {
        'status': 'checked',
        'total_pending': pendingUploads.length,
        'old_files': oldFiles,
        'failed_files': failedFiles,
        'healthy': oldFiles < 10 && failedFiles < 5,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'healthy': false,
      };
    }
  }

  /// فحص حالة المصادقة
  Future<Map<String, dynamic>> _checkAuthStatus() async {
    try {
      final user = _supabase.auth.currentUser;
      
      if (user != null) {
        final session = _supabase.auth.currentSession;
        final expiresAt = session?.expiresAt;
        
        return {
          'status': 'authenticated',
          'user_id': user.id,
          'expires_at': expiresAt,
          'healthy': expiresAt == null || 
                    DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000)
                        .isAfter(DateTime.now().add(const Duration(hours: 1))),
        };
      } else {
        return {
          'status': 'not_authenticated',
          'healthy': false,
        };
      }
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'healthy': false,
      };
    }
  }

  /// حساب الحالة العامة للنظام
  String _calculateOverallStatus(Map<String, dynamic> healthReport) {
    final checks = [
      'internet', 'supabase', 'auto_upload', 'storage', 'pending_files', 'auth'
    ];
    
    int healthyCount = 0;
    int totalChecks = 0;
    
    for (final check in checks) {
      if (healthReport.containsKey(check)) {
        totalChecks++;
        final checkData = healthReport[check] as Map<String, dynamic>;
        if (checkData['healthy'] == true) {
          healthyCount++;
        }
      }
    }
    
    if (totalChecks == 0) return 'unknown';
    
    final healthPercentage = (healthyCount / totalChecks) * 100;
    
    if (healthPercentage >= 90) return 'excellent';
    if (healthPercentage >= 75) return 'good';
    if (healthPercentage >= 50) return 'fair';
    return 'poor';
  }

  /// حفظ تقرير الصحة محلياً
  Future<void> _saveHealthReport(Map<String, dynamic> report) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportJson = report.toString();
      await prefs.setString('last_health_report', reportJson);
      await prefs.setString('last_health_check', DateTime.now().toIso8601String());
    } catch (e) {
      _logger.e('Error saving health report: $e');
    }
  }

  /// الحصول على آخر تقرير صحة
  Future<Map<String, dynamic>?> getLastHealthReport() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportJson = prefs.getString('last_health_report');
      final lastCheck = prefs.getString('last_health_check');
      
      if (reportJson != null && lastCheck != null) {
        return {
          'report': reportJson,
          'last_check': lastCheck,
        };
      }
    } catch (e) {
      _logger.e('Error getting last health report: $e');
    }
    return null;
  }

  /// إجراء فحص فوري وإرجاع النتيجة
  Future<Map<String, dynamic>> performImmediateHealthCheck() async {
    return await _performHealthCheck();
  }

  /// تنظيف الملفات القديمة والمعطلة
  Future<void> cleanupOldFiles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingUploads = prefs.getStringList('pending_uploads') ?? [];
      final cleanUploads = <String>[];
      final now = DateTime.now();
      
      for (final uploadJson in pendingUploads) {
        try {
          final uploadData = Map<String, dynamic>.from(
            Map<String, dynamic>.from(uploadJson as Map)
          );
          
          final createdAt = DateTime.parse(uploadData['created_at'] as String);
          final age = now.difference(createdAt);
          
          // الاحتفاظ بالملفات الأحدث من 7 أيام فقط
          if (age.inDays <= 7) {
            cleanUploads.add(uploadJson);
          } else {
            // حذف الملف المحلي إذا كان قديماً
            final filePath = uploadData['file_path'] as String?;
            if (filePath != null) {
              final file = File(filePath);
              if (await file.exists()) {
                await file.delete();
                _logger.i('Deleted old file: $filePath');
              }
            }
          }
        } catch (e) {
          _logger.w('Error processing upload data: $e');
        }
      }
      
      await prefs.setStringList('pending_uploads', cleanUploads);
      _logger.i('Cleanup completed: removed ${pendingUploads.length - cleanUploads.length} old files');
      
    } catch (e) {
      _logger.e('Error during cleanup: $e');
    }
  }
}
