#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إدارة قاعدة بيانات ذاكرة القمر
Moon Memory Database Administration Tool

هذه الأداة توفر واجهة سطر أوامر شاملة لإدارة قاعدة البيانات
تشمل: الصيانة، التحسين، التقارير، النسخ الاحتياطي، والمراقبة

المطور: فريق ذاكرة القمر
التاريخ: يناير 2025
"""

import os
import sys
import json
import argparse
import logging
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import subprocess
import time

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_admin.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseAdmin:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self, config_file: str = 'db_config.json'):
        """تهيئة الاتصال بقاعدة البيانات"""
        self.config = self._load_config(config_file)
        self.connection = None
        self.cursor = None
        
    def _load_config(self, config_file: str) -> Dict:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # إعدادات افتراضية
            default_config = {
                "host": "localhost",
                "port": 5432,
                "database": "moon_memory",
                "user": "postgres",
                "password": "password",
                "backup_path": "./backups",
                "maintenance_schedule": {
                    "auto_vacuum": True,
                    "auto_analyze": True,
                    "cleanup_interval_days": 7
                }
            }
            logger.warning(f"ملف الإعدادات {config_file} غير موجود، استخدام الإعدادات الافتراضية")
            return default_config
    
    def connect(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            self.cursor = self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("تم قطع الاتصال بقاعدة البيانات")
    
    def execute_query(self, query: str, params: Tuple = None) -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            self.cursor.execute(query, params)
            if self.cursor.description:
                return self.cursor.fetchall()
            return []
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            self.connection.rollback()
            raise
    
    def get_database_stats(self) -> Dict:
        """الحصول على إحصائيات قاعدة البيانات"""
        logger.info("جاري جمع إحصائيات قاعدة البيانات...")
        
        stats = {}
        
        # إحصائيات عامة
        query = "SELECT * FROM get_dashboard_stats()"
        results = self.execute_query(query)
        stats['dashboard'] = {row['stat_name']: row['stat_value'] for row in results}
        
        # إحصائيات الأداء
        query = "SELECT * FROM get_performance_stats()"
        results = self.execute_query(query)
        stats['performance'] = {row['metric_name']: {
            'value': row['metric_value'],
            'unit': row['metric_unit'],
            'status': row['status']
        } for row in results}
        
        # حجم الجداول
        query = """
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        """
        results = self.execute_query(query)
        stats['table_sizes'] = results
        
        return stats
    
    def run_maintenance(self, full_maintenance: bool = False) -> Dict:
        """تشغيل عمليات الصيانة"""
        logger.info("بدء عمليات الصيانة...")
        
        maintenance_results = {
            'start_time': datetime.now().isoformat(),
            'operations': []
        }
        
        try:
            # تحسين قاعدة البيانات
            logger.info("تشغيل تحسين قاعدة البيانات...")
            query = "SELECT * FROM optimize_database()"
            results = self.execute_query(query)
            maintenance_results['operations'].append({
                'operation': 'optimize_database',
                'status': 'success',
                'details': results
            })
            
            # تنظيف البيانات المحذوفة
            logger.info("تنظيف البيانات المحذوفة...")
            query = "SELECT * FROM cleanup_deleted_records()"
            results = self.execute_query(query)
            maintenance_results['operations'].append({
                'operation': 'cleanup_deleted_records',
                'status': 'success',
                'details': results
            })
            
            # فحص سلامة البيانات
            logger.info("فحص سلامة البيانات...")
            query = "SELECT * FROM check_data_integrity()"
            results = self.execute_query(query)
            maintenance_results['operations'].append({
                'operation': 'check_data_integrity',
                'status': 'success',
                'details': results
            })
            
            if full_maintenance:
                # إعادة بناء الفهارس (للصيانة الكاملة فقط)
                logger.info("إعادة بناء الفهارس...")
                query = "SELECT * FROM auto_reindex_tables()"
                results = self.execute_query(query)
                maintenance_results['operations'].append({
                    'operation': 'reindex_tables',
                    'status': 'success',
                    'details': results
                })
            
            self.connection.commit()
            maintenance_results['end_time'] = datetime.now().isoformat()
            maintenance_results['status'] = 'success'
            
            logger.info("تمت عمليات الصيانة بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في عمليات الصيانة: {e}")
            maintenance_results['status'] = 'error'
            maintenance_results['error'] = str(e)
            self.connection.rollback()
        
        return maintenance_results
    
    def create_backup(self, backup_name: str = None) -> Dict:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if not backup_name:
            backup_name = f"moon_memory_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_path = os.path.join(self.config.get('backup_path', './backups'), f"{backup_name}.sql")
        
        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        logger.info(f"إنشاء نسخة احتياطية: {backup_path}")
        
        try:
            # استخدام pg_dump لإنشاء النسخة الاحتياطية
            cmd = [
                'pg_dump',
                '-h', self.config['host'],
                '-p', str(self.config['port']),
                '-U', self.config['user'],
                '-d', self.config['database'],
                '-f', backup_path,
                '--verbose',
                '--no-password'
            ]
            
            # تعيين متغير البيئة لكلمة المرور
            env = os.environ.copy()
            env['PGPASSWORD'] = self.config['password']
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                file_size = os.path.getsize(backup_path)
                logger.info(f"تم إنشاء النسخة الاحتياطية بنجاح - الحجم: {file_size} بايت")
                
                return {
                    'status': 'success',
                    'backup_path': backup_path,
                    'file_size': file_size,
                    'created_at': datetime.now().isoformat()
                }
            else:
                logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {result.stderr}")
                return {
                    'status': 'error',
                    'error': result.stderr
                }
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_report(self, report_type: str = 'daily') -> Dict:
        """إنشاء تقرير شامل"""
        logger.info(f"إنشاء تقرير {report_type}...")
        
        report = {
            'report_type': report_type,
            'generated_at': datetime.now().isoformat(),
            'sections': {}
        }
        
        try:
            if report_type == 'daily':
                # تقرير النشاط اليومي
                query = "SELECT * FROM generate_daily_activity_report()"
                results = self.execute_query(query)
                report['sections']['daily_activity'] = results
                
            elif report_type == 'storage':
                # تقرير استخدام التخزين
                query = "SELECT * FROM generate_storage_usage_report()"
                results = self.execute_query(query)
                report['sections']['storage_usage'] = results
                
            elif report_type == 'security':
                # تقرير الأمان
                query = "SELECT * FROM generate_security_report()"
                results = self.execute_query(query)
                report['sections']['security'] = results
                
            elif report_type == 'full':
                # تقرير شامل
                queries = {
                    'daily_activity': "SELECT * FROM generate_daily_activity_report()",
                    'storage_usage': "SELECT * FROM generate_storage_usage_report()",
                    'security': "SELECT * FROM generate_security_report()",
                    'performance': "SELECT * FROM get_performance_stats()"
                }
                
                for section, query in queries.items():
                    results = self.execute_query(query)
                    report['sections'][section] = results
            
            report['status'] = 'success'
            logger.info(f"تم إنشاء تقرير {report_type} بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير: {e}")
            report['status'] = 'error'
            report['error'] = str(e)
        
        return report
    
    def monitor_system(self, duration_minutes: int = 5) -> Dict:
        """مراقبة النظام لفترة محددة"""
        logger.info(f"بدء مراقبة النظام لمدة {duration_minutes} دقائق...")
        
        monitoring_data = {
            'start_time': datetime.now().isoformat(),
            'duration_minutes': duration_minutes,
            'samples': []
        }
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        while datetime.now() < end_time:
            try:
                # جمع عينة من البيانات
                sample = {
                    'timestamp': datetime.now().isoformat(),
                    'performance': {}
                }
                
                # إحصائيات الأداء
                query = "SELECT * FROM get_performance_stats()"
                results = self.execute_query(query)
                for row in results:
                    sample['performance'][row['metric_name']] = {
                        'value': row['metric_value'],
                        'status': row['status']
                    }
                
                monitoring_data['samples'].append(sample)
                
                # انتظار دقيقة واحدة
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"خطأ في مراقبة النظام: {e}")
                break
        
        monitoring_data['end_time'] = datetime.now().isoformat()
        logger.info("انتهت مراقبة النظام")

        return monitoring_data

def main():
    """الدالة الرئيسية لأداة الإدارة"""
    parser = argparse.ArgumentParser(
        description='أداة إدارة قاعدة بيانات ذاكرة القمر',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  %(prog)s stats                    # عرض إحصائيات قاعدة البيانات
  %(prog)s maintenance             # تشغيل الصيانة العادية
  %(prog)s maintenance --full      # تشغيل الصيانة الكاملة
  %(prog)s backup                  # إنشاء نسخة احتياطية
  %(prog)s backup --name my_backup # إنشاء نسخة احتياطية باسم محدد
  %(prog)s report daily            # إنشاء تقرير يومي
  %(prog)s report full             # إنشاء تقرير شامل
  %(prog)s monitor --duration 10   # مراقبة النظام لمدة 10 دقائق
        """
    )

    parser.add_argument(
        'command',
        choices=['stats', 'maintenance', 'backup', 'report', 'monitor'],
        help='الأمر المراد تنفيذه'
    )

    parser.add_argument(
        'subcommand',
        nargs='?',
        help='الأمر الفرعي (للتقارير: daily, storage, security, full)'
    )

    parser.add_argument(
        '--config',
        default='db_config.json',
        help='ملف إعدادات قاعدة البيانات (افتراضي: db_config.json)'
    )

    parser.add_argument(
        '--full',
        action='store_true',
        help='تشغيل الصيانة الكاملة (للصيانة فقط)'
    )

    parser.add_argument(
        '--name',
        help='اسم النسخة الاحتياطية (للنسخ الاحتياطية فقط)'
    )

    parser.add_argument(
        '--duration',
        type=int,
        default=5,
        help='مدة المراقبة بالدقائق (افتراضي: 5)'
    )

    parser.add_argument(
        '--output',
        help='ملف الإخراج للتقارير (JSON)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='عرض تفاصيل أكثر'
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # إنشاء كائن إدارة قاعدة البيانات
    admin = DatabaseAdmin(args.config)

    if not admin.connect():
        logger.error("فشل في الاتصال بقاعدة البيانات")
        sys.exit(1)

    try:
        result = None

        if args.command == 'stats':
            # عرض الإحصائيات
            result = admin.get_database_stats()
            print("\n=== إحصائيات قاعدة البيانات ===")

            print("\n📊 إحصائيات عامة:")
            for key, value in result['dashboard'].items():
                print(f"  {key}: {value}")

            print("\n⚡ أداء النظام:")
            for key, data in result['performance'].items():
                status_icon = "✅" if data['status'] == 'OK' else "⚠️" if data['status'] == 'WARNING' else "❌"
                print(f"  {status_icon} {key}: {data['value']} {data['unit']}")

            print("\n💾 أحجام الجداول:")
            for table in result['table_sizes'][:5]:  # أكبر 5 جداول
                print(f"  {table['tablename']}: {table['size']}")

        elif args.command == 'maintenance':
            # تشغيل الصيانة
            result = admin.run_maintenance(args.full)

            if result['status'] == 'success':
                print("\n✅ تمت عمليات الصيانة بنجاح")
                for op in result['operations']:
                    print(f"  ✓ {op['operation']}")
            else:
                print(f"\n❌ فشلت عمليات الصيانة: {result.get('error', 'خطأ غير معروف')}")

        elif args.command == 'backup':
            # إنشاء نسخة احتياطية
            result = admin.create_backup(args.name)

            if result['status'] == 'success':
                size_mb = result['file_size'] / (1024 * 1024)
                print(f"\n✅ تم إنشاء النسخة الاحتياطية بنجاح")
                print(f"  📁 المسار: {result['backup_path']}")
                print(f"  📊 الحجم: {size_mb:.2f} ميجابايت")
            else:
                print(f"\n❌ فشل في إنشاء النسخة الاحتياطية: {result.get('error', 'خطأ غير معروف')}")

        elif args.command == 'report':
            # إنشاء تقرير
            report_type = args.subcommand or 'daily'
            result = admin.generate_report(report_type)

            if result['status'] == 'success':
                print(f"\n📋 تقرير {report_type} - {result['generated_at']}")

                for section_name, section_data in result['sections'].items():
                    print(f"\n--- {section_name} ---")
                    if isinstance(section_data, list) and section_data:
                        for item in section_data[:5]:  # أول 5 عناصر
                            print(f"  {item}")
                    else:
                        print(f"  {section_data}")
            else:
                print(f"\n❌ فشل في إنشاء التقرير: {result.get('error', 'خطأ غير معروف')}")

        elif args.command == 'monitor':
            # مراقبة النظام
            print(f"\n🔍 بدء مراقبة النظام لمدة {args.duration} دقائق...")
            result = admin.monitor_system(args.duration)

            print(f"\n📈 تم جمع {len(result['samples'])} عينة")
            if result['samples']:
                latest = result['samples'][-1]
                print("\nآخر عينة:")
                for metric, data in latest['performance'].items():
                    status_icon = "✅" if data['status'] == 'OK' else "⚠️" if data['status'] == 'WARNING' else "❌"
                    print(f"  {status_icon} {metric}: {data['value']}")

        # حفظ النتيجة في ملف إذا تم تحديد مسار الإخراج
        if args.output and result:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            print(f"\n💾 تم حفظ النتائج في: {args.output}")

    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف العملية بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        sys.exit(1)
    finally:
        admin.disconnect()

if __name__ == '__main__':
    main()
