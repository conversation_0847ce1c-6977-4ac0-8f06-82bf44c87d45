#!/bin/bash
# سكريبت تثبيت أدوات إدارة نظام ذاكرة القمر
# Moon Memory System Administration Tools Installation Script

set -e

echo "🌙 مرحباً بك في مثبت أدوات إدارة ذاكرة القمر"
echo "=================================================="

# التحقق من Python
echo "🔍 التحقق من Python..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت. يرجى تثبيت Python 3.8 أو أحدث."
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ تم العثور على Python $PYTHON_VERSION"

# التحقق من pip
echo "🔍 التحقق من pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip غير مثبت. يرجى تثبيت pip."
    exit 1
fi
echo "✅ pip متاح"

# التحقق من PostgreSQL
echo "🔍 التحقق من PostgreSQL..."
if ! command -v psql &> /dev/null; then
    echo "⚠️ PostgreSQL غير مثبت أو غير متاح في PATH"
    echo "   يرجى تثبيت PostgreSQL للحصول على الوظائف الكاملة"
else
    echo "✅ PostgreSQL متاح"
fi

# إنشاء بيئة افتراضية
echo "🔧 إنشاء بيئة افتراضية..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ تم إنشاء البيئة الافتراضية"
else
    echo "✅ البيئة الافتراضية موجودة"
fi

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# ترقية pip
echo "⬆️ ترقية pip..."
pip install --upgrade pip

# تثبيت المتطلبات
echo "📦 تثبيت المتطلبات..."
pip install -r requirements.txt

# إنشاء مجلدات العمل
echo "📁 إنشاء مجلدات العمل..."
mkdir -p backups/full
mkdir -p backups/incremental
mkdir -p backups/media
mkdir -p logs
mkdir -p reports
mkdir -p templates

# إعداد الأذونات
echo "🔒 إعداد الأذونات..."
chmod +x moon_memory_cli.py
chmod +x database_admin_tool.py
chmod +x web_monitor.py
chmod 600 db_config.json

# إنشاء ملف إعدادات افتراضي إذا لم يكن موجوداً
if [ ! -f "db_config.json" ]; then
    echo "⚙️ إنشاء ملف إعدادات افتراضي..."
    cat > db_config.json << 'EOF'
{
  "host": "localhost",
  "port": 5432,
  "database": "moon_memory",
  "user": "postgres",
  "password": "CHANGE_THIS_PASSWORD",
  "backup_path": "./backups",
  "maintenance_schedule": {
    "auto_vacuum": true,
    "auto_analyze": true,
    "cleanup_interval_days": 7
  },
  "monitoring": {
    "performance_threshold": {
      "query_time_ms": 500,
      "connection_count": 50,
      "database_size_gb": 10
    },
    "alerts": {
      "email_enabled": false,
      "email_recipients": [],
      "webhook_url": null
    }
  }
}
EOF
    chmod 600 db_config.json
    echo "✅ تم إنشاء ملف الإعدادات - يرجى تحديث كلمة المرور"
fi

# إنشاء سكريبت تشغيل سريع
echo "🚀 إنشاء سكريبت تشغيل سريع..."
cat > moon_memory << 'EOF'
#!/bin/bash
# سكريبت تشغيل سريع لأدوات ذاكرة القمر

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# تفعيل البيئة الافتراضية
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# تشغيل الأداة الرئيسية
python3 moon_memory_cli.py "$@"
EOF

chmod +x moon_memory

# اختبار التثبيت
echo "🧪 اختبار التثبيت..."
if python3 moon_memory_cli.py --help > /dev/null 2>&1; then
    echo "✅ تم اختبار الأداة الرئيسية بنجاح"
else
    echo "❌ فشل في اختبار الأداة الرئيسية"
    exit 1
fi

# إنشاء ملف خدمة systemd (اختياري)
echo "🔧 إنشاء ملف خدمة systemd..."
CURRENT_DIR=$(pwd)
USER=$(whoami)

cat > moon-memory-monitor.service << EOF
[Unit]
Description=Moon Memory System Monitor
After=network.target postgresql.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$CURRENT_DIR
Environment=PATH=$CURRENT_DIR/venv/bin
ExecStart=$CURRENT_DIR/venv/bin/python $CURRENT_DIR/moon_memory_cli.py monitor start --interval 60
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "📋 ملخص التثبيت:"
echo "=================="
echo "✅ تم تثبيت جميع المتطلبات"
echo "✅ تم إنشاء البيئة الافتراضية"
echo "✅ تم إعداد مجلدات العمل"
echo "✅ تم إنشاء ملف الإعدادات"
echo "✅ تم إنشاء سكريبت التشغيل السريع"

echo ""
echo "🚀 الخطوات التالية:"
echo "==================="
echo "1. تحديث إعدادات قاعدة البيانات في db_config.json"
echo "2. اختبار الاتصال: ./moon_memory database stats"
echo "3. فحص صحة النظام: ./moon_memory tools health-check"
echo "4. إنشاء نسخة احتياطية: ./moon_memory backup create full"
echo "5. بدء المراقبة: ./moon_memory monitor web"

echo ""
echo "📖 للمساعدة:"
echo "============="
echo "• عرض المساعدة: ./moon_memory --help"
echo "• قراءة الوثائق: cat README.md"
echo "• فحص السجلات: tail -f *.log"

echo ""
echo "🎉 تم التثبيت بنجاح! مرحباً بك في عالم ذاكرة القمر 🌙"
