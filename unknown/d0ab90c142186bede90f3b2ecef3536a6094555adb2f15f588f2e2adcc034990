-- 🚀 إعداد قاعدة البيانات المثالية لتطبيق Moon Memory
-- Perfect Database Setup for Moon Memory App
-- Date: 2025-01-15
-- Version: 2.0 - Clean & Perfect

-- ===== 👥 جدول المستخدمين =====
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    national_id TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    
    -- إعدادات الحساب
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    account_type TEXT DEFAULT 'user',
    
    -- حدود الحساب
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    
    -- معلومات إضافية
    department TEXT,
    position TEXT,
    notes TEXT,
    
    -- التوقيتات
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.users(id)
);

-- ===== 📱 جدول الأجهزة =====
CREATE TABLE public.devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- البصمة الرقمية
    device_fingerprint TEXT NOT NULL UNIQUE,
    android_id TEXT NOT NULL,
    build_fingerprint TEXT,
    
    -- معلومات الجهاز
    device_name TEXT,
    device_model TEXT,
    device_brand TEXT,
    device_product TEXT,
    device_hardware TEXT,
    
    -- معلومات تقنية إضافية
    hardware_info TEXT,
    system_info TEXT,
    screen_info TEXT,
    cpu_info TEXT,
    storage_info TEXT,
    raw_fingerprint_data TEXT,
    
    -- مستوى الثقة والأمان
    confidence_score DECIMAL(5,2) DEFAULT 0,
    trust_level TEXT DEFAULT 'untrusted',
    
    -- إدارة المصادقة
    auth_attempts INTEGER DEFAULT 0,
    last_auth_attempt TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- التوقيتات
    last_verified_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== 📸 جدول الصور =====
CREATE TABLE public.photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    image_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    
    -- نظام المواقع
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    
    -- معلومات المستخدم
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وتصنيف
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    
    -- معلومات تقنية
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== 🎥 جدول الفيديو =====
CREATE TABLE public.videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    
    -- معلومات الملف
    file_name TEXT NOT NULL,
    storage_path TEXT,
    video_url TEXT,
    url TEXT,
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    
    -- نظام المواقع
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    
    -- معلومات المستخدم
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- ترتيب وتصنيف
    sort_order INTEGER,
    tags TEXT[],
    description TEXT,
    
    -- معلومات تقنية للفيديو
    resolution TEXT,
    fps INTEGER,
    codec TEXT,
    bitrate INTEGER,
    camera_settings JSONB,
    gps_coordinates POINT,
    weather_info JSONB,
    
    -- حالة الملف
    status TEXT DEFAULT 'active',
    is_processed BOOLEAN DEFAULT FALSE,
    processing_info JSONB,
    thumbnail_url TEXT,
    
    -- التوقيتات
    date_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== 🔐 جدول الجلسات =====
CREATE TABLE public.user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    device_id UUID REFERENCES public.devices(id) ON DELETE CASCADE,
    
    -- معلومات الجلسة
    session_token TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيتات
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    
    -- حالة الجلسة
    is_active BOOLEAN DEFAULT TRUE,
    end_reason TEXT
);

-- ===== 📋 جدول سجلات الإدارة =====
CREATE TABLE public.admin_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    admin_id UUID REFERENCES public.users(id) NOT NULL,
    target_user_id UUID REFERENCES public.users(id),
    
    -- معلومات العملية
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id UUID,
    
    -- تفاصيل العملية
    description TEXT,
    old_values JSONB,
    new_values JSONB,
    
    -- معلومات إضافية
    ip_address INET,
    user_agent TEXT,
    
    -- التوقيت
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== 📊 جدول الإحصائيات =====
CREATE TABLE public.system_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    stat_date DATE DEFAULT CURRENT_DATE,
    
    -- إحصائيات المستخدمين
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    new_users_today INTEGER DEFAULT 0,
    
    -- إحصائيات الأجهزة
    total_devices INTEGER DEFAULT 0,
    active_devices INTEGER DEFAULT 0,
    blocked_devices INTEGER DEFAULT 0,
    
    -- إحصائيات الملفات
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    photos_uploaded_today INTEGER DEFAULT 0,
    videos_uploaded_today INTEGER DEFAULT 0,
    
    -- إحصائيات التخزين
    total_storage_used_mb BIGINT DEFAULT 0,
    photos_storage_mb BIGINT DEFAULT 0,
    videos_storage_mb BIGINT DEFAULT 0,
    
    -- إحصائيات المواقع
    u_locations_used INTEGER DEFAULT 0,
    c_locations_used INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(stat_date)
);

-- ===== 📍 جدول المواقع =====
CREATE TABLE public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,

    -- معرف الموقع
    location_code TEXT UNIQUE NOT NULL, -- U101, U102, ..., C101, C102, ...
    location_type TEXT NOT NULL CHECK (location_type IN ('U', 'C')),
    location_number TEXT NOT NULL,

    -- تفاصيل الموقع
    location_name_ar TEXT,
    location_name_en TEXT,
    description_ar TEXT,
    description_en TEXT,

    -- ترتيب وتصنيف
    sort_order INTEGER NOT NULL,
    category TEXT,
    department TEXT,

    -- حالة الموقع
    is_active BOOLEAN DEFAULT TRUE,
    is_available BOOLEAN DEFAULT TRUE,

    -- إحصائيات
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,

    -- التوقيتات
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- فهارس مركبة
    UNIQUE(location_type, location_number)
);

-- ===== 📍 إدراج مواقع U (U101-U125) =====
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, category) VALUES
('U101', 'U', '101', 'موقع يو 101', 'U Location 101', 1, 'U_SECTION'),
('U102', 'U', '102', 'موقع يو 102', 'U Location 102', 2, 'U_SECTION'),
('U103', 'U', '103', 'موقع يو 103', 'U Location 103', 3, 'U_SECTION'),
('U104', 'U', '104', 'موقع يو 104', 'U Location 104', 4, 'U_SECTION'),
('U105', 'U', '105', 'موقع يو 105', 'U Location 105', 5, 'U_SECTION'),
('U106', 'U', '106', 'موقع يو 106', 'U Location 106', 6, 'U_SECTION'),
('U107', 'U', '107', 'موقع يو 107', 'U Location 107', 7, 'U_SECTION'),
('U108', 'U', '108', 'موقع يو 108', 'U Location 108', 8, 'U_SECTION'),
('U109', 'U', '109', 'موقع يو 109', 'U Location 109', 9, 'U_SECTION'),
('U110', 'U', '110', 'موقع يو 110', 'U Location 110', 10, 'U_SECTION'),
('U111', 'U', '111', 'موقع يو 111', 'U Location 111', 11, 'U_SECTION'),
('U112', 'U', '112', 'موقع يو 112', 'U Location 112', 12, 'U_SECTION'),
('U113', 'U', '113', 'موقع يو 113', 'U Location 113', 13, 'U_SECTION'),
('U114', 'U', '114', 'موقع يو 114', 'U Location 114', 14, 'U_SECTION'),
('U115', 'U', '115', 'موقع يو 115', 'U Location 115', 15, 'U_SECTION'),
('U116', 'U', '116', 'موقع يو 116', 'U Location 116', 16, 'U_SECTION'),
('U117', 'U', '117', 'موقع يو 117', 'U Location 117', 17, 'U_SECTION'),
('U118', 'U', '118', 'موقع يو 118', 'U Location 118', 18, 'U_SECTION'),
('U119', 'U', '119', 'موقع يو 119', 'U Location 119', 19, 'U_SECTION'),
('U120', 'U', '120', 'موقع يو 120', 'U Location 120', 20, 'U_SECTION'),
('U121', 'U', '121', 'موقع يو 121', 'U Location 121', 21, 'U_SECTION'),
('U122', 'U', '122', 'موقع يو 122', 'U Location 122', 22, 'U_SECTION'),
('U123', 'U', '123', 'موقع يو 123', 'U Location 123', 23, 'U_SECTION'),
('U124', 'U', '124', 'موقع يو 124', 'U Location 124', 24, 'U_SECTION'),
('U125', 'U', '125', 'موقع يو 125', 'U Location 125', 25, 'U_SECTION');

-- ===== 📍 إدراج مواقع C (C101-C145) =====
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, category) VALUES
('C101', 'C', '101', 'موقع سي 101', 'C Location 101', 101, 'C_SECTION'),
('C102', 'C', '102', 'موقع سي 102', 'C Location 102', 102, 'C_SECTION'),
('C103', 'C', '103', 'موقع سي 103', 'C Location 103', 103, 'C_SECTION'),
('C104', 'C', '104', 'موقع سي 104', 'C Location 104', 104, 'C_SECTION'),
('C105', 'C', '105', 'موقع سي 105', 'C Location 105', 105, 'C_SECTION'),
('C106', 'C', '106', 'موقع سي 106', 'C Location 106', 106, 'C_SECTION'),
('C107', 'C', '107', 'موقع سي 107', 'C Location 107', 107, 'C_SECTION'),
('C108', 'C', '108', 'موقع سي 108', 'C Location 108', 108, 'C_SECTION'),
('C109', 'C', '109', 'موقع سي 109', 'C Location 109', 109, 'C_SECTION'),
('C110', 'C', '110', 'موقع سي 110', 'C Location 110', 110, 'C_SECTION'),
('C111', 'C', '111', 'موقع سي 111', 'C Location 111', 111, 'C_SECTION'),
('C112', 'C', '112', 'موقع سي 112', 'C Location 112', 112, 'C_SECTION'),
('C113', 'C', '113', 'موقع سي 113', 'C Location 113', 113, 'C_SECTION'),
('C114', 'C', '114', 'موقع سي 114', 'C Location 114', 114, 'C_SECTION'),
('C115', 'C', '115', 'موقع سي 115', 'C Location 115', 115, 'C_SECTION'),
('C116', 'C', '116', 'موقع سي 116', 'C Location 116', 116, 'C_SECTION'),
('C117', 'C', '117', 'موقع سي 117', 'C Location 117', 117, 'C_SECTION'),
('C118', 'C', '118', 'موقع سي 118', 'C Location 118', 118, 'C_SECTION'),
('C119', 'C', '119', 'موقع سي 119', 'C Location 119', 119, 'C_SECTION'),
('C120', 'C', '120', 'موقع سي 120', 'C Location 120', 120, 'C_SECTION'),
('C121', 'C', '121', 'موقع سي 121', 'C Location 121', 121, 'C_SECTION'),
('C122', 'C', '122', 'موقع سي 122', 'C Location 122', 122, 'C_SECTION'),
('C123', 'C', '123', 'موقع سي 123', 'C Location 123', 123, 'C_SECTION'),
('C124', 'C', '124', 'موقع سي 124', 'C Location 124', 124, 'C_SECTION'),
('C125', 'C', '125', 'موقع سي 125', 'C Location 125', 125, 'C_SECTION'),
('C126', 'C', '126', 'موقع سي 126', 'C Location 126', 126, 'C_SECTION'),
('C127', 'C', '127', 'موقع سي 127', 'C Location 127', 127, 'C_SECTION'),
('C128', 'C', '128', 'موقع سي 128', 'C Location 128', 128, 'C_SECTION'),
('C129', 'C', '129', 'موقع سي 129', 'C Location 129', 129, 'C_SECTION'),
('C130', 'C', '130', 'موقع سي 130', 'C Location 130', 130, 'C_SECTION'),
('C131', 'C', '131', 'موقع سي 131', 'C Location 131', 131, 'C_SECTION'),
('C132', 'C', '132', 'موقع سي 132', 'C Location 132', 132, 'C_SECTION'),
('C133', 'C', '133', 'موقع سي 133', 'C Location 133', 133, 'C_SECTION'),
('C134', 'C', '134', 'موقع سي 134', 'C Location 134', 134, 'C_SECTION'),
('C135', 'C', '135', 'موقع سي 135', 'C Location 135', 135, 'C_SECTION'),
('C136', 'C', '136', 'موقع سي 136', 'C Location 136', 136, 'C_SECTION'),
('C137', 'C', '137', 'موقع سي 137', 'C Location 137', 137, 'C_SECTION'),
('C138', 'C', '138', 'موقع سي 138', 'C Location 138', 138, 'C_SECTION'),
('C139', 'C', '139', 'موقع سي 139', 'C Location 139', 139, 'C_SECTION'),
('C140', 'C', '140', 'موقع سي 140', 'C Location 140', 140, 'C_SECTION'),
('C141', 'C', '141', 'موقع سي 141', 'C Location 141', 141, 'C_SECTION'),
('C142', 'C', '142', 'موقع سي 142', 'C Location 142', 142, 'C_SECTION'),
('C143', 'C', '143', 'موقع سي 143', 'C Location 143', 143, 'C_SECTION'),
('C144', 'C', '144', 'موقع سي 144', 'C Location 144', 144, 'C_SECTION'),
('C145', 'C', '145', 'موقع سي 145', 'C Location 145', 145, 'C_SECTION');

-- ===== 📊 إنشاء فهارس للأداء =====
CREATE INDEX IF NOT EXISTS idx_locations_type ON public.locations(location_type);
CREATE INDEX IF NOT EXISTS idx_locations_sort_order ON public.locations(sort_order);
CREATE INDEX IF NOT EXISTS idx_locations_active ON public.locations(is_active);
CREATE INDEX IF NOT EXISTS idx_locations_category ON public.locations(category);

-- ===== 🗂️ إنشاء Storage Buckets =====
INSERT INTO storage.buckets (id, name, public)
VALUES ('photos', 'photos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('videos', 'videos', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- ===== ✅ رسائل النجاح =====
SELECT '🎉 تم إنشاء جميع الجداول بنجاح!' as status;

-- عرض الجداول المُنشأة
SELECT table_name, 
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('users', 'devices', 'photos', 'videos', 'user_sessions', 'admin_logs', 'system_stats')
ORDER BY table_name;

SELECT '🚀 النظام جاهز للمرحلة التالية!' as next_step;
