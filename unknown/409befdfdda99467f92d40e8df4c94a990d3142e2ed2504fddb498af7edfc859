#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراقبة نظام ذاكرة القمر المتقدمة
Moon Memory Advanced System Monitor

هذه الأداة توفر مراقبة مستمرة للنظام مع:
- مراقبة الأداء في الوقت الفعلي
- إنذارات تلقائية
- تقارير دورية
- واجهة ويب للمراقبة

المطور: فريق ذاكرة القمر
التاريخ: يناير 2025
"""

import os
import sys
import json
import time
import threading
import logging
import psutil
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Flask, render_template, jsonify, request
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_monitor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SystemMonitor:
    """فئة مراقبة النظام المتقدمة"""
    
    def __init__(self, config_file: str = 'db_config.json'):
        """تهيئة مراقب النظام"""
        self.config = self._load_config(config_file)
        self.connection = None
        self.cursor = None
        self.monitoring_active = False
        self.alerts_sent = set()  # لتجنب إرسال نفس التنبيه مراراً
        self.metrics_history = []
        
    def _load_config(self, config_file: str) -> Dict:
        """تحميل إعدادات المراقبة"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"ملف الإعدادات {config_file} غير موجود")
            sys.exit(1)
    
    def connect_database(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            self.cursor = self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            logger.info("تم الاتصال بقاعدة البيانات للمراقبة")
            return True
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def collect_system_metrics(self) -> Dict:
        """جمع مقاييس النظام"""
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'system': {},
            'database': {},
            'application': {}
        }
        
        # مقاييس النظام
        try:
            metrics['system'] = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'network_io': psutil.net_io_counters()._asdict(),
                'process_count': len(psutil.pids()),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }
        except Exception as e:
            logger.error(f"خطأ في جمع مقاييس النظام: {e}")
        
        # مقاييس قاعدة البيانات
        if self.connection:
            try:
                # إحصائيات الأداء
                self.cursor.execute("SELECT * FROM get_performance_stats()")
                db_stats = self.cursor.fetchall()
                metrics['database']['performance'] = {
                    row['metric_name']: {
                        'value': row['metric_value'],
                        'unit': row['metric_unit'],
                        'status': row['status']
                    } for row in db_stats
                }
                
                # عدد الاتصالات النشطة
                self.cursor.execute("""
                    SELECT count(*) as active_connections 
                    FROM pg_stat_activity 
                    WHERE state = 'active'
                """)
                result = self.cursor.fetchone()
                metrics['database']['active_connections'] = result['active_connections']
                
                # حجم قاعدة البيانات
                self.cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size,
                           pg_database_size(current_database()) as db_size_bytes
                """)
                result = self.cursor.fetchone()
                metrics['database']['size'] = result['db_size']
                metrics['database']['size_bytes'] = result['db_size_bytes']
                
            except Exception as e:
                logger.error(f"خطأ في جمع مقاييس قاعدة البيانات: {e}")
        
        return metrics
    
    def check_thresholds(self, metrics: Dict) -> List[Dict]:
        """فحص العتبات وإنشاء التنبيهات"""
        alerts = []
        thresholds = self.config.get('monitoring', {}).get('performance_threshold', {})
        
        # فحص استخدام المعالج
        cpu_percent = metrics['system'].get('cpu_percent', 0)
        if cpu_percent > 80:
            alerts.append({
                'type': 'cpu_high',
                'severity': 'warning' if cpu_percent < 90 else 'critical',
                'message': f'استخدام المعالج مرتفع: {cpu_percent}%',
                'value': cpu_percent
            })
        
        # فحص استخدام الذاكرة
        memory_percent = metrics['system'].get('memory_percent', 0)
        if memory_percent > 80:
            alerts.append({
                'type': 'memory_high',
                'severity': 'warning' if memory_percent < 90 else 'critical',
                'message': f'استخدام الذاكرة مرتفع: {memory_percent}%',
                'value': memory_percent
            })
        
        # فحص استخدام القرص
        disk_percent = metrics['system'].get('disk_percent', 0)
        if disk_percent > 85:
            alerts.append({
                'type': 'disk_high',
                'severity': 'warning' if disk_percent < 95 else 'critical',
                'message': f'استخدام القرص مرتفع: {disk_percent}%',
                'value': disk_percent
            })
        
        # فحص عدد الاتصالات بقاعدة البيانات
        db_connections = metrics['database'].get('active_connections', 0)
        max_connections = thresholds.get('connection_count', 50)
        if db_connections > max_connections:
            alerts.append({
                'type': 'db_connections_high',
                'severity': 'warning',
                'message': f'عدد اتصالات قاعدة البيانات مرتفع: {db_connections}',
                'value': db_connections
            })
        
        # فحص حجم قاعدة البيانات
        db_size_bytes = metrics['database'].get('size_bytes', 0)
        max_size_bytes = thresholds.get('database_size_gb', 10) * 1024 * 1024 * 1024
        if db_size_bytes > max_size_bytes:
            alerts.append({
                'type': 'db_size_large',
                'severity': 'info',
                'message': f'حجم قاعدة البيانات كبير: {metrics["database"].get("size", "غير معروف")}',
                'value': db_size_bytes
            })
        
        return alerts
    
    def send_alert(self, alert: Dict):
        """إرسال تنبيه"""
        alert_key = f"{alert['type']}_{alert['severity']}"
        
        # تجنب إرسال نفس التنبيه خلال 30 دقيقة
        if alert_key in self.alerts_sent:
            return
        
        self.alerts_sent.add(alert_key)
        
        # إزالة التنبيه من القائمة بعد 30 دقيقة
        threading.Timer(1800, lambda: self.alerts_sent.discard(alert_key)).start()
        
        logger.warning(f"تنبيه {alert['severity']}: {alert['message']}")
        
        # إرسال بريد إلكتروني إذا كان مفعلاً
        if self.config.get('monitoring', {}).get('alerts', {}).get('email_enabled', False):
            self._send_email_alert(alert)
        
        # إرسال webhook إذا كان مفعلاً
        webhook_url = self.config.get('monitoring', {}).get('alerts', {}).get('webhook_url')
        if webhook_url:
            self._send_webhook_alert(alert, webhook_url)
    
    def _send_email_alert(self, alert: Dict):
        """إرسال تنبيه عبر البريد الإلكتروني"""
        try:
            # إعدادات البريد الإلكتروني (يجب إضافتها للإعدادات)
            smtp_server = self.config.get('email', {}).get('smtp_server', 'smtp.gmail.com')
            smtp_port = self.config.get('email', {}).get('smtp_port', 587)
            username = self.config.get('email', {}).get('username', '')
            password = self.config.get('email', {}).get('password', '')
            
            recipients = self.config.get('monitoring', {}).get('alerts', {}).get('email_recipients', [])
            
            if not recipients or not username:
                return
            
            msg = MIMEMultipart()
            msg['From'] = username
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = f"تنبيه ذاكرة القمر - {alert['severity'].upper()}"
            
            body = f"""
            تم اكتشاف مشكلة في نظام ذاكرة القمر:
            
            النوع: {alert['type']}
            الخطورة: {alert['severity']}
            الرسالة: {alert['message']}
            الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            
            يرجى مراجعة النظام واتخاذ الإجراء المناسب.
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"تم إرسال تنبيه بريد إلكتروني: {alert['type']}")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {e}")
    
    def _send_webhook_alert(self, alert: Dict, webhook_url: str):
        """إرسال تنبيه عبر webhook"""
        try:
            payload = {
                'alert_type': alert['type'],
                'severity': alert['severity'],
                'message': alert['message'],
                'value': alert.get('value'),
                'timestamp': datetime.now().isoformat(),
                'system': 'moon_memory'
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            logger.info(f"تم إرسال webhook: {alert['type']}")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال webhook: {e}")
    
    def start_monitoring(self, interval_seconds: int = 60):
        """بدء المراقبة المستمرة"""
        logger.info(f"بدء المراقبة المستمرة - فترة: {interval_seconds} ثانية")
        self.monitoring_active = True
        
        if not self.connect_database():
            logger.error("فشل في الاتصال بقاعدة البيانات للمراقبة")
            return
        
        while self.monitoring_active:
            try:
                # جمع المقاييس
                metrics = self.collect_system_metrics()
                
                # إضافة للتاريخ (الاحتفاظ بآخر 100 عينة)
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > 100:
                    self.metrics_history.pop(0)
                
                # فحص العتبات
                alerts = self.check_thresholds(metrics)
                
                # إرسال التنبيهات
                for alert in alerts:
                    self.send_alert(alert)
                
                # انتظار الفترة المحددة
                time.sleep(interval_seconds)
                
            except KeyboardInterrupt:
                logger.info("تم إيقاف المراقبة بواسطة المستخدم")
                break
            except Exception as e:
                logger.error(f"خطأ في دورة المراقبة: {e}")
                time.sleep(interval_seconds)
        
        self.monitoring_active = False
        if self.connection:
            self.connection.close()
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
        logger.info("تم إيقاف المراقبة")
    
    def get_current_status(self) -> Dict:
        """الحصول على الحالة الحالية"""
        if not self.metrics_history:
            return {'status': 'no_data', 'message': 'لا توجد بيانات متاحة'}
        
        latest_metrics = self.metrics_history[-1]
        alerts = self.check_thresholds(latest_metrics)
        
        return {
            'status': 'critical' if any(a['severity'] == 'critical' for a in alerts) else
                     'warning' if any(a['severity'] == 'warning' for a in alerts) else 'ok',
            'metrics': latest_metrics,
            'alerts': alerts,
            'history_count': len(self.metrics_history)
        }
