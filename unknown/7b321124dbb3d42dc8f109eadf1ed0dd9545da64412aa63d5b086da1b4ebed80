-- السياسات والدوال المبسطة
-- Simple Security and Functions
-- Date: 2025-01-15
-- يجب تطبيق هذا بعد simple_tables_only.sql

-- ===== إضافة الحقول المُحسوبة =====

-- إض<PERSON><PERSON>ة الحقل المُحسوب للصور
ALTER TABLE photos 
ADD COLUMN IF NOT EXISTS full_location_code TEXT;

-- إضافة الحقل المُحسوب للفيديو
ALTER TABLE videos 
ADD COLUMN IF NOT EXISTS full_location_code TEXT;

-- ===== تفعيل Row Level Security =====
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_stats ENABLE ROW LEVEL SECURITY;

-- ===== إنشاء السياسات المبسطة =====

-- سياسات المستخدمين
DROP POLICY IF EXISTS users_policy ON users;
CREATE POLICY users_policy ON users FOR ALL USING (true);

-- سياسات الأجهزة
DROP POLICY IF EXISTS devices_policy ON devices;
CREATE POLICY devices_policy ON devices FOR ALL USING (true);

-- سياسات الصور
DROP POLICY IF EXISTS photos_policy ON photos;
CREATE POLICY photos_policy ON photos FOR ALL USING (true);

-- سياسات الفيديو
DROP POLICY IF EXISTS videos_policy ON videos;
CREATE POLICY videos_policy ON videos FOR ALL USING (true);

-- سياسات الجلسات
DROP POLICY IF EXISTS sessions_policy ON user_sessions;
CREATE POLICY sessions_policy ON user_sessions FOR ALL USING (true);

-- سياسات سجلات الإدارة
DROP POLICY IF EXISTS admin_logs_policy ON admin_logs;
CREATE POLICY admin_logs_policy ON admin_logs FOR ALL USING (true);

-- سياسات الإحصائيات
DROP POLICY IF EXISTS stats_policy ON system_stats;
CREATE POLICY stats_policy ON system_stats FOR ALL USING (true);

-- ===== إنشاء الدوال الأساسية =====

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الحقل المُحسوب للصور
CREATE OR REPLACE FUNCTION update_photo_location_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.full_location_code := NEW.location_type || NEW.location_number;
        
        -- تحديث ترقيم الفرز
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM photos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث الحقل المُحسوب للفيديو
CREATE OR REPLACE FUNCTION update_video_location_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_type IS NOT NULL AND NEW.location_number IS NOT NULL THEN
        NEW.full_location_code := NEW.location_type || NEW.location_number;
        
        -- تحديث ترقيم الفرز
        NEW.sort_order := (
            SELECT COALESCE(MAX(sort_order), 0) + 1
            FROM videos 
            WHERE location_type = NEW.location_type 
            AND location_number = NEW.location_number
            AND user_id = NEW.user_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===== تطبيق المحفزات =====

-- محفزات تحديث updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_devices_updated_at ON devices;
CREATE TRIGGER update_devices_updated_at 
    BEFORE UPDATE ON devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_photos_updated_at ON photos;
CREATE TRIGGER update_photos_updated_at 
    BEFORE UPDATE ON photos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_videos_updated_at ON videos;
CREATE TRIGGER update_videos_updated_at 
    BEFORE UPDATE ON videos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- محفزات تحديث الحقول المُحسوبة
DROP TRIGGER IF EXISTS trigger_update_photo_location_code ON photos;
CREATE TRIGGER trigger_update_photo_location_code
    BEFORE INSERT OR UPDATE ON photos
    FOR EACH ROW
    EXECUTE FUNCTION update_photo_location_code();

DROP TRIGGER IF EXISTS trigger_update_video_location_code ON videos;
CREATE TRIGGER trigger_update_video_location_code
    BEFORE INSERT OR UPDATE ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_video_location_code();

-- ===== دوال إدارية بسيطة =====

-- دالة إنشاء مستخدم جديد
CREATE OR REPLACE FUNCTION create_user_simple(
    p_user_id UUID,
    p_national_id TEXT,
    p_full_name TEXT,
    p_email TEXT,
    p_is_admin BOOLEAN DEFAULT FALSE
)
RETURNS TEXT AS $$
BEGIN
    INSERT INTO users (
        id,
        national_id,
        full_name,
        email,
        is_admin,
        account_type
    ) VALUES (
        p_user_id,
        p_national_id,
        p_full_name,
        p_email,
        p_is_admin,
        CASE WHEN p_is_admin THEN 'admin' ELSE 'user' END
    );
    
    RETURN 'تم إنشاء المستخدم بنجاح';
    
EXCEPTION WHEN OTHERS THEN
    RETURN 'خطأ: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- دالة الحصول على إحصائيات بسيطة
CREATE OR REPLACE FUNCTION get_simple_stats()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'total_users', (SELECT COUNT(*) FROM users),
        'active_users', (SELECT COUNT(*) FROM users WHERE is_active = TRUE),
        'total_devices', (SELECT COUNT(*) FROM devices),
        'total_photos', (SELECT COUNT(*) FROM photos WHERE status = 'active'),
        'total_videos', (SELECT COUNT(*) FROM videos WHERE status = 'active'),
        'u_locations', (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'U'),
        'c_locations', (SELECT COUNT(DISTINCT location_number) FROM photos WHERE location_type = 'C')
    );
END;
$$ LANGUAGE plpgsql;

-- ===== إنشاء فهارس إضافية =====
CREATE INDEX IF NOT EXISTS idx_photos_full_location_code ON photos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_videos_full_location_code ON videos(full_location_code);
CREATE INDEX IF NOT EXISTS idx_photos_capture_timestamp ON photos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_videos_capture_timestamp ON videos(capture_timestamp);
CREATE INDEX IF NOT EXISTS idx_photos_username ON photos(username);
CREATE INDEX IF NOT EXISTS idx_videos_username ON videos(username);

-- فهرس مركب للترتيب المطلوب
CREATE INDEX IF NOT EXISTS idx_photos_sorting ON photos(location_type, location_number, capture_timestamp, username);
CREATE INDEX IF NOT EXISTS idx_videos_sorting ON videos(location_type, location_number, capture_timestamp, username);

-- ===== إدراج بيانات تجريبية للإحصائيات =====
INSERT INTO system_stats (stat_date) VALUES (CURRENT_DATE) ON CONFLICT (stat_date) DO NOTHING;

-- ===== رسائل النجاح =====
SELECT 'تم إعداد السياسات والدوال بنجاح! ✅' as status;
SELECT 'النظام جاهز للاستخدام! 🚀' as final_message;

-- اختبار الدوال
SELECT 'إحصائيات النظام:' as info;
SELECT get_simple_stats() as stats;
