# 🛠️ دليل التطوير | Development Guide

## 📋 جدول المحتويات

- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
- [هيكل المشروع](#هيكل-المشروع)
- [معايير الكود](#معايير-الكود)
- [إدارة الحالة](#إدارة-الحالة)
- [معالجة الأخطاء](#معالجة-الأخطاء)
- [الاختبارات](#الاختبارات)
- [الأداء](#الأداء)
- [الأمان](#الأمان)

## 🔧 إعداد بيئة التطوير

### المتطلبات الأساسية
```bash
# Flutter SDK
flutter --version
# يجب أن يكون 3.6.1 أو أحدث

# Dart SDK
dart --version
# يجب أن يكون 3.0 أو أحدث
```

### أدوات التطوير المطلوبة
- **VS Code** مع إضافات Flutter و Dart
- **Android Studio** للتطوير على Android
- **Xcode** للتطوير على iOS (macOS فقط)
- **Git** لإدارة النسخ

### إعداد VS Code
```json
// .vscode/settings.json
{
  "dart.flutterSdkPath": "path/to/flutter",
  "dart.lineLength": 80,
  "editor.rulers": [80],
  "editor.formatOnSave": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true
}
```

## 🏗️ هيكل المشروع

### تنظيم الملفات
```
lib/
├── core/                    # الوظائف الأساسية
│   ├── config/             # إعدادات التطبيق
│   │   ├── app_config.dart
│   │   └── supabase_config.dart
│   ├── errors/             # معالجة الأخطاء
│   │   ├── app_exceptions.dart
│   │   └── error_handler.dart
│   ├── routing/            # نظام التنقل
│   │   └── app_router.dart
│   ├── services/           # الخدمات الأساسية
│   │   ├── auth_service.dart
│   │   ├── cache_service.dart
│   │   └── photos_service.dart
│   ├── theme/              # تصميم التطبيق
│   │   └── app_theme.dart
│   └── utils/              # أدوات مساعدة
│       ├── image_processor.dart
│       └── logger.dart
├── features/               # ميزات التطبيق
│   ├── auth/              # المصادقة
│   │   ├── domain/        # منطق العمل
│   │   └── presentation/  # واجهة المستخدم
│   ├── camera/            # الكاميرا
│   ├── home/              # الشاشة الرئيسية
│   └── settings/          # الإعدادات
├── shared/                # مكونات مشتركة
│   ├── theme/
│   └── widgets/
└── main.dart              # نقطة البداية
```

### قواعد التسمية
- **الملفات**: `snake_case.dart`
- **الكلاسات**: `PascalCase`
- **المتغيرات**: `camelCase`
- **الثوابت**: `UPPER_SNAKE_CASE`
- **الدوال الخاصة**: `_privateFunction`

## 📝 معايير الكود

### Dart Analysis
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  
linter:
  rules:
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - avoid_print
    - prefer_single_quotes
```

### تنسيق الكود
```bash
# تنسيق تلقائي
dart format .

# فحص الكود
dart analyze

# إصلاح المشاكل التلقائية
dart fix --apply
```

### التعليقات والتوثيق
```dart
/// وصف مختصر للكلاس أو الدالة
/// 
/// وصف مفصل إذا لزم الأمر
/// 
/// مثال:
/// ```dart
/// final result = await authService.signIn(
///   nationalId: '1234567890',
///   password: 'password',
/// );
/// ```
class AuthService {
  /// تسجيل دخول المستخدم
  /// 
  /// [nationalId] رقم الهوية الوطنية (10 أرقام)
  /// [password] كلمة المرور
  /// 
  /// يرجع [Future<void>] عند النجاح
  /// يرمي [AuthException] عند الفشل
  Future<void> signIn({
    required String nationalId,
    required String password,
  }) async {
    // تنفيذ الدالة
  }
}
```

## 🔄 إدارة الحالة

### Riverpod Providers
```dart
// Provider للخدمات
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// StateProvider للحالة البسيطة
final isLoadingProvider = StateProvider<bool>((ref) => false);

// FutureProvider للبيانات غير المتزامنة
final userProvider = FutureProvider<User?>((ref) async {
  final authService = ref.read(authServiceProvider);
  return authService.getCurrentUser();
});

// StreamProvider للبيانات المتدفقة
final photosStreamProvider = StreamProvider<List<Photo>>((ref) {
  final photosService = ref.read(photosServiceProvider);
  return photosService.getPhotosStream();
});
```

### استخدام Providers
```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(isLoadingProvider);
    final userAsync = ref.watch(userProvider);
    
    return userAsync.when(
      data: (user) => Text('مرحباً ${user?.name}'),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('خطأ: $error'),
    );
  }
}
```

## ⚠️ معالجة الأخطاء

### هيكل الأخطاء
```dart
// استثناء مخصص
class AuthException extends AppException {
  const AuthException(super.message, {super.code});
  
  @override
  String getLocalizedMessage() {
    switch (code) {
      case 'invalid_credentials':
        return 'auth.errors.invalid_credentials'.tr();
      default:
        return message;
    }
  }
}

// معالجة الأخطاء
try {
  await authService.signIn(nationalId: id, password: pass);
} catch (e) {
  final appError = ErrorHandler.handleError(e);
  ErrorHandler.showErrorSnackBar(context, appError);
}
```

## 🧪 الاختبارات

### Unit Tests
```dart
void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    
    setUp(() {
      authService = AuthService();
    });
    
    test('should sign in with valid credentials', () async {
      // Arrange
      const nationalId = '1234567890';
      const password = 'password123';
      
      // Act & Assert
      expect(
        () => authService.signIn(
          nationalId: nationalId, 
          password: password,
        ),
        returnsNormally,
      );
    });
  });
}
```

### Widget Tests
```dart
void main() {
  testWidgets('LoginScreen should show login form', (tester) async {
    await tester.pumpWidget(
      MaterialApp(home: LoginScreen()),
    );
    
    expect(find.text('تسجيل الدخول'), findsOneWidget);
    expect(find.byType(TextFormField), findsNWidgets(2));
  });
}
```

### Integration Tests
```dart
void main() {
  group('App Integration Tests', () {
    testWidgets('complete login flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // التفاعل مع التطبيق
      await tester.enterText(find.byKey(Key('nationalId')), '1234567890');
      await tester.enterText(find.byKey(Key('password')), 'password');
      await tester.tap(find.byKey(Key('loginButton')));
      
      await tester.pumpAndSettle();
      
      // التحقق من النتيجة
      expect(find.byType(HomeScreen), findsOneWidget);
    });
  });
}
```

## ⚡ الأداء

### تحسين الصور
```dart
// ضغط الصور
final compressedImage = await ImageProcessor.compressImage(
  imageBytes,
  quality: 85,
  maxWidth: 1920,
  maxHeight: 1080,
);

// تخزين مؤقت
final cachedImage = await cacheService.getCachedFile(imageKey);
if (cachedImage != null) {
  return cachedImage;
}
```

### تحسين الذاكرة
```dart
// تحرير الموارد
@override
void dispose() {
  _controller?.dispose();
  _timer?.cancel();
  super.dispose();
}

// استخدام const constructors
const MyWidget({super.key});
```

### تحسين البناء
```dart
// تجنب البناء غير الضروري
class MyWidget extends StatelessWidget {
  const MyWidget({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final data = ref.watch(dataProvider);
        return ExpensiveWidget(data: data);
      },
      child: const StaticWidget(), // لن يُعاد بناؤها
    );
  }
}
```

## 🔒 الأمان

### حماية البيانات الحساسة
```dart
// استخدام متغيرات البيئة
class SupabaseConfig {
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';
}

// تشفير البيانات المحلية
final encryptedData = await EncryptionService.encrypt(sensitiveData);
```

### التحقق من الصحة
```dart
// التحقق من المدخلات
String? validateNationalId(String? value) {
  if (value == null || value.isEmpty) {
    return 'auth.national_id_required'.tr();
  }
  if (value.length != 10) {
    return 'auth.national_id_length'.tr();
  }
  if (!RegExp(r'^\d{10}$').hasMatch(value)) {
    return 'auth.national_id_invalid'.tr();
  }
  return null;
}
```

## 🚀 نصائح للتطوير

### استخدام Hot Reload بفعالية
- احفظ الملفات بانتظام لتفعيل Hot Reload
- استخدم Hot Restart عند تغيير الحالة الأولية
- تجنب الأخطاء في build methods

### تحسين تجربة التطوير
```bash
# تشغيل مع verbose logging
flutter run --verbose

# تشغيل على جهاز محدد
flutter run -d device_id

# بناء مع profiling
flutter run --profile
```

### أدوات مفيدة
- **Flutter Inspector**: لفحص widget tree
- **Performance Overlay**: لمراقبة الأداء
- **Memory Profiler**: لمراقبة استخدام الذاكرة

---

هذا الدليل يتطور مع المشروع. لا تتردد في إضافة المزيد من التفاصيل!
