import 'dart:io';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:share_plus/share_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'cache_service.dart';

final photosServiceProvider = Provider<PhotosService>((ref) {
  return PhotosService(ref.read(cacheServiceProvider));
});

class PhotosService {
  final _client = Supabase.instance.client;
  final _photosBucket = 'photos';
  final _logger = Logger();
  final CacheService _cacheService;

  PhotosService(this._cacheService);

  String? get _currentUserId => _client.auth.currentUser?.id;
  String? get _currentUsername => _client.auth.currentUser?.userMetadata?['full_name'] ??
                                  _client.auth.currentUser?.email?.split('@').first;

  /// تنظيف معرف المستخدم للاستخدام في مسارات التخزين
  String _sanitizeUserId(String userId) {
    return userId.toLowerCase().replaceAll(RegExp(r'[^a-z0-9-]'), '_');
  }

  /// تحليل كود الموقع الجديد (مثل U101, C145)
  Map<String, String?> _parseLocationCode(String? location) {
    if (location == null || location.isEmpty) {
      return {'type': null, 'number': null};
    }

    // التحقق من تطابق النمط الجديد (U/C + رقم)
    final regex = RegExp(r'^([UC])(\d+)$');
    final match = regex.firstMatch(location);

    if (match != null) {
      return {
        'type': match.group(1),
        'number': match.group(2),
      };
    }

    // إذا لم يتطابق مع النمط الجديد، اعتبره موقع قديم
    return {'type': 'U', 'number': '101'}; // قيم افتراضية
  }

  /// رفع صورة إلى التخزين مع نظام ذكي للحفظ
  Future<String?> uploadPhoto(String photoPath, String? location) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // فحص الاتصال بالإنترنت
      final hasInternet = await _checkInternetConnection();

      if (hasInternet) {
        // رفع مباشر مع الإنترنت
        return await _uploadDirectly(photoPath, location, userId);
      } else {
        // حفظ محلي بدون إنترنت
        await _saveOffline(photoPath, location, userId);
        return null; // لا يوجد storage path للملفات المحفوظة محلياً
      }
    } catch (e) {
      _logger.e('Error processing photo: $e');
      rethrow;
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// رفع مباشر مع الإنترنت
  Future<String> _uploadDirectly(String photoPath, String? location, String userId) async {
    final file = File(photoPath);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(photoPath);
    final fileName = 'photo_$timestamp$extension';
    final safeUserId = _sanitizeUserId(userId);
    final storagePath = 'users/$safeUserId/$fileName';

    // حفظ نسخة في التخزين المؤقت
    await _cacheService.cacheFile(fileName, file);

    // رفع الصورة إلى التخزين
    await _client.storage.from(_photosBucket).upload(
          storagePath,
          file,
          fileOptions: const FileOptions(
            cacheControl: '3600',
            upsert: true,
          ),
        );

    // الحصول على رابط الصورة العام
    final imageUrl = await getPhotoUrl(storagePath);
    if (imageUrl == null) {
      throw 'فشل في الحصول على رابط الصورة';
    }

    // تحليل كود الموقع الجديد
    final locationData = _parseLocationCode(location);
    final currentTime = DateTime.now();

    // إنشاء سجل في قاعدة البيانات مع النظام الجديد
    await _client.from('photos').insert({
      'user_id': userId,
      'storage_path': storagePath,
      'image_url': imageUrl,
      'date_time': currentTime.toIso8601String(),
      'location': location, // الحقل القديم للتوافق
      'location_type': locationData['type'],
      'location_number': locationData['number'],
      'username': _currentUsername,
      'capture_timestamp': currentTime.toIso8601String(),
      'upload_timestamp': currentTime.toIso8601String(),
    });

    _logger.i('Photo uploaded directly: $storagePath');

    // حذف الملف المحلي بعد الرفع الناجح
    await file.delete();

    return storagePath;
  }

  /// حفظ محلي بدون إنترنت
  Future<void> _saveOffline(String photoPath, String? location, String userId) async {
    // إنشاء مجلد الوسائط المحلية
    final directory = await getApplicationDocumentsDirectory();
    final offlineDir = Directory('${directory.path}/offline_media');
    if (!await offlineDir.exists()) {
      await offlineDir.create(recursive: true);
    }

    // نسخ الصورة إلى مجلد الوسائط المحلية
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = path.extension(photoPath);
    final fileName = 'photo_offline_$timestamp$extension';
    final savedPath = '${offlineDir.path}/$fileName';

    await File(photoPath).copy(savedPath);

    // حفظ معلومات الصورة للرفع لاحقاً
    await _saveOfflineMetadata(savedPath, fileName, location, userId);

    // حذف الملف المؤقت
    await File(photoPath).delete();

    _logger.i('Photo saved offline: $savedPath');
  }

  /// حفظ معلومات الملف المحفوظ محلياً
  Future<void> _saveOfflineMetadata(String filePath, String fileName, String? location, String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final offlinePhotos = prefs.getStringList('offline_photos') ?? [];

    // تحليل كود الموقع الجديد
    final locationData = _parseLocationCode(location);
    final currentTime = DateTime.now();

    final metadata = {
      'file_path': filePath,
      'file_name': fileName,
      'location': location ?? 'unknown',
      'location_type': locationData['type'],
      'location_number': locationData['number'],
      'username': _currentUsername,
      'user_id': userId,
      'created_at': currentTime.toIso8601String(),
      'capture_timestamp': currentTime.toIso8601String(),
      'type': 'photo',
    };

    offlinePhotos.add(jsonEncode(metadata));
    await prefs.setStringList('offline_photos', offlinePhotos);
  }

  /// رفع ومشاركة صورة
  Future<void> uploadAndSharePhoto(String photoPath, String? location) async {
    try {
      await uploadPhoto(photoPath, location);
      await Share.shareXFiles([XFile(photoPath)]);
    } catch (e) {
      _logger.e('Error sharing photo: $e');
      rethrow;
    }
  }

  /// الحصول على الرابط العام للصورة
  Future<String?> getPhotoUrl(String storagePath) async {
    try {
      final response = await _client.storage.from(_photosBucket).createSignedUrl(
            storagePath,
            3600 * 24 * 365,
          );
      return response;
    } catch (e) {
      _logger.e('Error getting photo URL: $e');
      return null;
    }
  }
}
