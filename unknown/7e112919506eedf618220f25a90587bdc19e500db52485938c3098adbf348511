-- إضافة الدوال للتحقق من الأجهزة
-- Migration: 002_add_functions.sql
-- Date: 2025-01-12

-- دالة بسيطة للتحقق من الجهاز وإضافته/تحديثه
CREATE OR REPLACE FUNCTION verify_device_enhanced(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_build_fingerprint TEXT DEFAULT NULL,
    p_confidence_score DECIMAL DEFAULT 0,
    p_trust_level TEXT DEFAULT 'untrusted',
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_devices INTEGER := 3; -- الحد الأقصى للأجهزة لكل مستخدم
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود بالفعل
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- الجهاز موجود - تحديث المعلومات
        UPDATE devices SET
            device_fingerprint = p_device_fingerprint,
            android_id = p_android_id,
            build_fingerprint = COALESCE(p_build_fingerprint, build_fingerprint),
            confidence_score = p_confidence_score,
            trust_level = p_trust_level,
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            last_verified_at = NOW()
        WHERE id = device_record.id;
        
        result := json_build_object(
            'status', 'success',
            'action', 'updated',
            'device_id', device_record.id,
            'message', 'تم تحديث معلومات الجهاز بنجاح',
            'confidence_score', p_confidence_score,
            'trust_level', p_trust_level
        );
    ELSE
        -- جهاز جديد - فحص الحد الأقصى للأجهزة
        SELECT COUNT(*) INTO device_count 
        FROM devices 
        WHERE user_id = p_user_id;
        
        IF device_count >= max_devices THEN
            result := json_build_object(
                'status', 'error',
                'code', 'DEVICE_LIMIT_REACHED',
                'message', 'تم الوصول للحد الأقصى من الأجهزة المسموحة (' || max_devices || ')',
                'current_devices', device_count
            );
        ELSE
            -- إضافة الجهاز الجديد
            INSERT INTO devices (
                user_id,
                device_fingerprint,
                android_id,
                build_fingerprint,
                confidence_score,
                trust_level,
                device_name,
                device_model,
                device_brand,
                last_verified_at,
                created_at
            ) VALUES (
                p_user_id,
                p_device_fingerprint,
                p_android_id,
                p_build_fingerprint,
                p_confidence_score,
                p_trust_level,
                p_device_name,
                p_device_model,
                p_device_brand,
                NOW(),
                NOW()
            ) RETURNING id INTO device_record;
            
            result := json_build_object(
                'status', 'success',
                'action', 'created',
                'device_id', device_record.id,
                'message', 'تم إضافة الجهاز الجديد بنجاح',
                'confidence_score', p_confidence_score,
                'trust_level', p_trust_level
            );
        END IF;
    END IF;
    
    RETURN result;
END;
$$;

-- دالة للحصول على أجهزة المستخدم
CREATE OR REPLACE FUNCTION get_user_devices(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    devices_array JSON;
    device_count INTEGER;
BEGIN
    -- الحصول على عدد الأجهزة
    SELECT COUNT(*) INTO device_count 
    FROM devices 
    WHERE user_id = p_user_id;
    
    -- الحصول على قائمة الأجهزة
    SELECT json_agg(
        json_build_object(
            'id', id,
            'device_fingerprint', device_fingerprint,
            'android_id', android_id,
            'device_name', device_name,
            'device_model', device_model,
            'device_brand', device_brand,
            'confidence_score', confidence_score,
            'trust_level', trust_level,
            'last_verified_at', last_verified_at,
            'created_at', created_at
        )
    ) INTO devices_array
    FROM devices 
    WHERE user_id = p_user_id
    ORDER BY last_verified_at DESC NULLS LAST, created_at DESC;
    
    RETURN json_build_object(
        'status', 'success',
        'device_count', device_count,
        'devices', COALESCE(devices_array, '[]'::json)
    );
END;
$$;

-- دالة لحذف جهاز
CREATE OR REPLACE FUNCTION remove_device(
    p_user_id UUID,
    p_device_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود ويخص المستخدم
    SELECT * INTO device_record 
    FROM devices 
    WHERE id = p_device_id AND user_id = p_user_id;
    
    IF device_record.id IS NOT NULL THEN
        -- حذف الجهاز
        DELETE FROM devices 
        WHERE id = p_device_id AND user_id = p_user_id;
        
        result := json_build_object(
            'status', 'success',
            'message', 'تم حذف الجهاز بنجاح',
            'deleted_device', json_build_object(
                'id', device_record.id,
                'device_name', device_record.device_name,
                'device_model', device_record.device_model
            )
        );
    ELSE
        result := json_build_object(
            'status', 'error',
            'code', 'DEVICE_NOT_FOUND',
            'message', 'الجهاز غير موجود أو لا يخصك'
        );
    END IF;
    
    RETURN result;
END;
$$;

-- تفعيل Row Level Security
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين - يمكنهم رؤية أجهزتهم فقط
DROP POLICY IF EXISTS devices_user_policy ON devices;
CREATE POLICY devices_user_policy ON devices
    FOR ALL
    USING (auth.uid() = user_id);

-- رسالة نجاح
SELECT 'Functions added successfully! 🚀' as status;
