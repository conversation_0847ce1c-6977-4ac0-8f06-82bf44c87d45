-- إضافة الدوال المتقدمة للنظام الجديد
-- Add Advanced Functions for New System
-- Date: 2025-01-12

-- دالة شاملة للتحقق من الجهاز وإضافته/تحديثه
CREATE OR REPLACE FUNCTION verify_device_enhanced(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT,
    p_build_fingerprint TEXT DEFAULT NULL,
    p_confidence_score DECIMAL DEFAULT 0,
    p_trust_level TEXT DEFAULT 'untrusted',
    p_device_name TEXT DEFAULT NULL,
    p_device_model TEXT DEFAULT NULL,
    p_device_brand TEXT DEFAULT NULL,
    p_device_product TEXT DEFAULT NULL,
    p_device_hardware TEXT DEFAULT NULL,
    p_hardware_info TEXT DEFAULT NULL,
    p_system_info TEXT DEFAULT NULL,
    p_screen_info TEXT DEFAULT NULL,
    p_cpu_info TEXT DEFAULT NULL,
    p_storage_info TEXT DEFAULT NULL,
    p_raw_data TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_devices INTEGER := 3; -- الحد الأقصى للأجهزة لكل مستخدم
    device_count INTEGER;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود بالفعل
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- الجهاز موجود - تحديث المعلومات
        UPDATE devices SET
            device_fingerprint = p_device_fingerprint,
            android_id = p_android_id,
            build_fingerprint = COALESCE(p_build_fingerprint, build_fingerprint),
            confidence_score = p_confidence_score,
            trust_level = p_trust_level,
            device_name = COALESCE(p_device_name, device_name),
            device_model = COALESCE(p_device_model, device_model),
            device_brand = COALESCE(p_device_brand, device_brand),
            device_product = COALESCE(p_device_product, device_product),
            device_hardware = COALESCE(p_device_hardware, device_hardware),
            hardware_info = COALESCE(p_hardware_info, hardware_info),
            system_info = COALESCE(p_system_info, system_info),
            screen_info = COALESCE(p_screen_info, screen_info),
            cpu_info = COALESCE(p_cpu_info, cpu_info),
            storage_info = COALESCE(p_storage_info, storage_info),
            raw_fingerprint_data = COALESCE(p_raw_data, raw_fingerprint_data),
            last_verified_at = NOW(),
            auth_attempts = 0,
            is_blocked = FALSE,
            blocked_until = NULL
        WHERE id = device_record.id;
        
        result := json_build_object(
            'status', 'success',
            'action', 'updated',
            'device_id', device_record.id,
            'message', 'تم تحديث معلومات الجهاز بنجاح',
            'confidence_score', p_confidence_score,
            'trust_level', p_trust_level
        );
    ELSE
        -- جهاز جديد - فحص الحد الأقصى للأجهزة
        SELECT COUNT(*) INTO device_count 
        FROM devices 
        WHERE user_id = p_user_id;
        
        IF device_count >= max_devices THEN
            result := json_build_object(
                'status', 'error',
                'code', 'DEVICE_LIMIT_REACHED',
                'message', 'تم الوصول للحد الأقصى من الأجهزة المسموحة (' || max_devices || ')',
                'current_devices', device_count,
                'max_devices', max_devices
            );
        ELSE
            -- إضافة الجهاز الجديد
            INSERT INTO devices (
                user_id,
                device_fingerprint,
                android_id,
                build_fingerprint,
                confidence_score,
                trust_level,
                device_name,
                device_model,
                device_brand,
                device_product,
                device_hardware,
                hardware_info,
                system_info,
                screen_info,
                cpu_info,
                storage_info,
                raw_fingerprint_data,
                last_verified_at
            ) VALUES (
                p_user_id,
                p_device_fingerprint,
                p_android_id,
                p_build_fingerprint,
                p_confidence_score,
                p_trust_level,
                p_device_name,
                p_device_model,
                p_device_brand,
                p_device_product,
                p_device_hardware,
                p_hardware_info,
                p_system_info,
                p_screen_info,
                p_cpu_info,
                p_storage_info,
                p_raw_data,
                NOW()
            ) RETURNING id INTO device_record;
            
            result := json_build_object(
                'status', 'success',
                'action', 'created',
                'device_id', device_record.id,
                'message', 'تم إضافة الجهاز الجديد بنجاح',
                'confidence_score', p_confidence_score,
                'trust_level', p_trust_level,
                'device_count', device_count + 1
            );
        END IF;
    END IF;
    
    RETURN result;
END;
$$;

-- دالة للحصول على أجهزة المستخدم
CREATE OR REPLACE FUNCTION get_user_devices(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    devices_array JSON;
    device_count INTEGER;
    stats JSON;
BEGIN
    -- الحصول على عدد الأجهزة
    SELECT COUNT(*) INTO device_count 
    FROM devices 
    WHERE user_id = p_user_id;
    
    -- الحصول على قائمة الأجهزة
    SELECT json_agg(
        json_build_object(
            'id', id,
            'device_fingerprint', device_fingerprint,
            'android_id', android_id,
            'device_name', device_name,
            'device_model', device_model,
            'device_brand', device_brand,
            'confidence_score', confidence_score,
            'trust_level', trust_level,
            'is_blocked', is_blocked,
            'last_verified_at', last_verified_at,
            'created_at', created_at
        ) ORDER BY last_verified_at DESC NULLS LAST, created_at DESC
    ) INTO devices_array
    FROM devices 
    WHERE user_id = p_user_id;
    
    -- حساب الإحصائيات
    SELECT json_build_object(
        'total_devices', COUNT(*),
        'avg_confidence', ROUND(AVG(confidence_score), 2),
        'high_trust', COUNT(*) FILTER (WHERE trust_level = 'high'),
        'medium_trust', COUNT(*) FILTER (WHERE trust_level = 'medium'),
        'low_trust', COUNT(*) FILTER (WHERE trust_level = 'low'),
        'blocked_devices', COUNT(*) FILTER (WHERE is_blocked = TRUE)
    ) INTO stats
    FROM devices 
    WHERE user_id = p_user_id;
    
    RETURN json_build_object(
        'status', 'success',
        'user_id', p_user_id,
        'device_count', device_count,
        'devices', COALESCE(devices_array, '[]'::json),
        'statistics', COALESCE(stats, '{}'::json)
    );
END;
$$;

-- دالة لحذف جهاز
CREATE OR REPLACE FUNCTION remove_device(
    p_user_id UUID,
    p_device_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    result JSON;
BEGIN
    -- فحص ما إذا كان الجهاز موجود ويخص المستخدم
    SELECT * INTO device_record 
    FROM devices 
    WHERE id = p_device_id AND user_id = p_user_id;
    
    IF device_record.id IS NOT NULL THEN
        -- حذف الجهاز
        DELETE FROM devices 
        WHERE id = p_device_id AND user_id = p_user_id;
        
        result := json_build_object(
            'status', 'success',
            'message', 'تم حذف الجهاز بنجاح',
            'deleted_device', json_build_object(
                'id', device_record.id,
                'device_name', device_record.device_name,
                'device_model', device_record.device_model,
                'device_brand', device_record.device_brand
            )
        );
    ELSE
        result := json_build_object(
            'status', 'error',
            'code', 'DEVICE_NOT_FOUND',
            'message', 'الجهاز غير موجود أو لا يخصك'
        );
    END IF;
    
    RETURN result;
END;
$$;

-- دالة لتسجيل محاولة مصادقة فاشلة
CREATE OR REPLACE FUNCTION record_failed_auth(
    p_user_id UUID,
    p_device_fingerprint TEXT,
    p_android_id TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    device_record RECORD;
    max_attempts INTEGER := 5;
    block_duration INTERVAL := '15 minutes';
    result JSON;
BEGIN
    -- البحث عن الجهاز
    SELECT * INTO device_record 
    FROM devices 
    WHERE user_id = p_user_id 
    AND (device_fingerprint = p_device_fingerprint OR android_id = p_android_id)
    LIMIT 1;
    
    IF device_record.id IS NOT NULL THEN
        -- تحديث عدد المحاولات
        UPDATE devices SET
            auth_attempts = auth_attempts + 1,
            last_auth_attempt = NOW()
        WHERE id = device_record.id;
        
        -- فحص ما إذا كان يجب حجب الجهاز
        IF (device_record.auth_attempts + 1) >= max_attempts THEN
            UPDATE devices SET
                is_blocked = TRUE,
                blocked_until = NOW() + block_duration,
                trust_level = 'blocked'
            WHERE id = device_record.id;
            
            result := json_build_object(
                'status', 'blocked',
                'message', 'تم حجب الجهاز بسبب تجاوز الحد الأقصى للمحاولات',
                'blocked_until', NOW() + block_duration,
                'attempts', device_record.auth_attempts + 1
            );
        ELSE
            result := json_build_object(
                'status', 'failed',
                'attempts', device_record.auth_attempts + 1,
                'max_attempts', max_attempts,
                'remaining_attempts', max_attempts - (device_record.auth_attempts + 1),
                'message', 'محاولة مصادقة فاشلة'
            );
        END IF;
    ELSE
        result := json_build_object(
            'status', 'not_found',
            'message', 'الجهاز غير مسجل'
        );
    END IF;
    
    RETURN result;
END;
$$;

-- رسالة نجاح
SELECT 'تم إضافة جميع الدوال بنجاح! 🔧' as functions_status;
