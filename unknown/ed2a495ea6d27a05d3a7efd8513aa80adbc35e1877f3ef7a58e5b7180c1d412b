# تطبيق Migration لتحديث جدول الأجهزة

## 📋 الخطوات المطلوبة

### 1. الدخول إلى Supabase Dashboard
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard)
2. اختر مشروعك
3. اذهب إلى **SQL Editor**

### 2. تطبيق Migration
1. انسخ محتوى ملف `001_update_devices_table.sql`
2. الصقه في SQL Editor
3. اضغط **Run** لتنفيذ الأوامر

### 3. التحقق من النتائج
```sql
-- فحص بنية الجدول الجديدة
\d devices;

-- أو استخدم هذا الاستعلام
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
ORDER BY ordinal_position;
```

### 4. اختبار الدوال الجديدة
```sql
-- اختبار دالة التحقق المحسنة
SELECT verify_device_enhanced(
    'user-uuid-here'::UUID,
    'test-fingerprint',
    'test-android-id',
    'test-build-fingerprint',
    85.5,
    'high'
);
```

## 🔧 الحقول الجديدة المضافة

### حقول البصمة الرقمية
- `device_fingerprint` - البصمة الرقمية المتقدمة
- `android_id` - معرف Android الفريد
- `build_fingerprint` - بصمة البناء
- `confidence_score` - نقاط الثقة (0-100)
- `trust_level` - مستوى الثقة

### حقول معلومات الجهاز
- `device_product` - اسم المنتج
- `device_hardware` - معلومات الهاردوير
- `hardware_info` - معلومات الهاردوير المجمعة
- `system_info` - معلومات النظام
- `screen_info` - معلومات الشاشة
- `cpu_info` - معلومات المعالج
- `storage_info` - معلومات التخزين

### حقول الأمان والمراقبة
- `last_verified_at` - آخر وقت تحقق
- `auth_attempts` - عدد محاولات المصادقة الفاشلة
- `last_auth_attempt` - آخر محاولة مصادقة
- `is_blocked` - هل الجهاز محجوب
- `blocked_until` - وقت انتهاء الحجب
- `raw_fingerprint_data` - البيانات الخام

## 🚀 الدوال الجديدة

### 1. verify_device_enhanced()
دالة شاملة للتحقق من الجهاز وإضافة/تحديث معلوماته

### 2. record_failed_auth_attempt()
تسجيل محاولات المصادقة الفاشلة وحجب الأجهزة المشبوهة

### 3. reset_auth_attempts()
إعادة تعيين محاولات المصادقة للجهاز

### 4. cleanup_old_devices()
تنظيف الأجهزة القديمة غير المستخدمة

## 🔒 الأمان

### Row Level Security (RLS)
- تم تفعيل RLS على جدول devices
- المستخدمون يرون أجهزتهم فقط
- الإدارة ترى جميع الأجهزة

### القيود والفهارس
- قيود للتحقق من صحة البيانات
- فهارس للبحث السريع
- تعليقات توضيحية للحقول

## ⚠️ ملاحظات مهمة

### 1. النسخ الاحتياطي
```sql
-- إنشاء نسخة احتياطية قبل التطبيق
CREATE TABLE devices_backup AS SELECT * FROM devices;
```

### 2. البيانات الموجودة
- الحقول الجديدة ستكون NULL للأجهزة الموجودة
- سيتم ملؤها تلقائياً عند تسجيل الدخول التالي

### 3. التوافق مع النظام القديم
- حقل `device_id` القديم محفوظ للتوافق
- يمكن حذفه لاحقاً بعد التأكد من عمل النظام الجديد

## 🧪 اختبار النظام

### 1. اختبار إضافة جهاز جديد
```sql
SELECT verify_device_enhanced(
    'your-user-id'::UUID,
    'new-device-fingerprint',
    'android-id-123',
    'build-fingerprint-abc',
    90.0,
    'high',
    'Samsung Galaxy S23',
    'SM-S911B',
    'Samsung'
);
```

### 2. اختبار تحديث جهاز موجود
```sql
SELECT verify_device_enhanced(
    'your-user-id'::UUID,
    'updated-fingerprint',
    'android-id-123',  -- نفس Android ID
    'new-build-fingerprint',
    85.0,
    'medium'
);
```

### 3. اختبار محاولة فاشلة
```sql
SELECT record_failed_auth_attempt(
    'your-user-id'::UUID,
    'wrong-fingerprint',
    'android-id-123'
);
```

## 📊 مراقبة النظام

### استعلامات مفيدة للمراقبة
```sql
-- عدد الأجهزة لكل مستخدم
SELECT user_id, COUNT(*) as device_count 
FROM devices 
GROUP BY user_id 
ORDER BY device_count DESC;

-- الأجهزة المحجوبة
SELECT * FROM devices 
WHERE is_blocked = TRUE;

-- الأجهزة حسب مستوى الثقة
SELECT trust_level, COUNT(*) 
FROM devices 
GROUP BY trust_level;

-- آخر نشاط للأجهزة
SELECT device_name, last_verified_at, 
       NOW() - last_verified_at as inactive_duration
FROM devices 
ORDER BY last_verified_at DESC;
```
