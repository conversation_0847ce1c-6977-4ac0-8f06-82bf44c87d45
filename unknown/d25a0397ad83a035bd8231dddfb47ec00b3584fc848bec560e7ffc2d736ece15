# متطلبات أدوات إدارة نظام ذاكرة القمر
# Moon Memory System Administration Tools Requirements

# قاعدة البيانات
psycopg2-binary>=2.9.0
sqlalchemy>=1.4.0

# مراقبة النظام
psutil>=5.8.0

# واجهة الويب
flask>=2.0.0
flask-cors>=3.0.0

# جدولة المهام
schedule>=1.1.0

# طلبات HTTP
requests>=2.25.0

# معالجة JSON وCSV
pandas>=1.3.0

# التشفير والأمان
cryptography>=3.4.0

# السجلات المتقدمة
colorlog>=6.0.0

# أدوات سطر الأوامر
click>=8.0.0
rich>=10.0.0

# معالجة التواريخ
python-dateutil>=2.8.0

# ضغط الملفات
py7zr>=0.16.0

# إرسال البريد الإلكتروني
smtplib-ssl>=1.0.0

# متطلبات اختيارية للميزات المتقدمة
# تثبيت حسب الحاجة:

# لدعم Redis (للتخزين المؤقت)
# redis>=3.5.0

# لدعم Elasticsearch (للبحث المتقدم)
# elasticsearch>=7.0.0

# لدعم Prometheus (للمراقبة المتقدمة)
# prometheus-client>=0.11.0

# لدعم Grafana (للمخططات)
# grafana-api>=1.0.0

# لدعم Docker (لإدارة الحاويات)
# docker>=5.0.0

# لدعم Kubernetes (للنشر السحابي)
# kubernetes>=18.0.0
