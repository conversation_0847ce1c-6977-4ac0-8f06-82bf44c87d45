-- ===== 🔧 سكريبت توحيد جداول المستخدمين =====
-- Users Tables Unification Script
-- تاريخ الإنشاء: 2025-07-20
-- الهدف: توحيد auth.users و public.users في جدول واحد محسن

-- ===== ⚠️ تحذيرات مهمة =====
/*
هذا السكريبت سيقوم بـ:
1. إنشاء نسخة احتياطية من البيانات الحالية
2. تحليل البيانات في كلا الجدولين
3. إنشاء جدول موحد جديد
4. دمج البيانات من كلا الجدولين
5. إنشاء views للتوافق مع الكود القديم
6. تحديث الصلاحيات والسياسات

⚠️ يجب تشغيل هذا السكريبت في بيئة تجريبية أولاً
⚠️ يجب إيقاف التطبيقين أثناء التنفيذ
⚠️ يجب عمل نسخة احتياطية كاملة قبل التنفيذ
*/

-- ===== 📊 المرحلة 1: تحليل البيانات الحالية =====

-- إنشاء جدول تحليل مؤقت
CREATE TEMP TABLE users_analysis AS
SELECT 
    'auth.users' as source_table,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as users_with_email,
    COUNT(CASE WHEN last_sign_in_at IS NOT NULL THEN 1 END) as users_with_login,
    MIN(created_at) as oldest_user,
    MAX(created_at) as newest_user
FROM auth.users
UNION ALL
SELECT
    'public.users' as source_table,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as users_with_email,
    COUNT(CASE WHEN last_login IS NOT NULL THEN 1 END) as users_with_login,
    MIN(created_at) as oldest_user,
    MAX(created_at) as newest_user
FROM public.users;

-- عرض تحليل البيانات
SELECT 
    '=== تحليل البيانات الحالية ===' as analysis_step,
    source_table,
    total_users,
    users_with_email,
    users_with_login,
    oldest_user,
    newest_user
FROM users_analysis;

-- ===== 💾 المرحلة 2: إنشاء نسخ احتياطية =====

-- نسخة احتياطية من auth.users
CREATE TABLE IF NOT EXISTS backup_auth_users AS 
SELECT * FROM auth.users;

-- نسخة احتياطية من public.users
CREATE TABLE IF NOT EXISTS backup_public_users AS 
SELECT * FROM public.users;

-- تأكيد إنشاء النسخ الاحتياطية
SELECT 
    '=== تأكيد النسخ الاحتياطية ===' as backup_step,
    'backup_auth_users' as backup_table,
    COUNT(*) as backed_up_records
FROM backup_auth_users
UNION ALL
SELECT 
    '=== تأكيد النسخ الاحتياطية ===' as backup_step,
    'backup_public_users' as backup_table,
    COUNT(*) as backed_up_records
FROM backup_public_users;

-- ===== 🏗️ المرحلة 3: إنشاء الجدول الموحد الجديد =====

-- حذف الجدول إذا كان موجوداً (للاختبار)
DROP TABLE IF EXISTS public.users_unified CASCADE;

-- إنشاء الجدول الموحد الجديد
CREATE TABLE public.users_unified (
    -- المعرف الأساسي
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- بيانات المصادقة (من auth.users)
    email CHARACTER VARYING(255) UNIQUE NOT NULL,
    encrypted_password TEXT,
    email_confirmed_at TIMESTAMP WITH TIME ZONE,
    last_sign_in_at TIMESTAMP WITH TIME ZONE,
    confirmation_token TEXT,
    recovery_token TEXT,
    email_change_token_new TEXT,
    email_change TEXT,
    raw_app_meta_data JSONB,
    raw_user_meta_data JSONB,
    
    -- البيانات الشخصية (من public.users)
    national_id CHARACTER VARYING(20) UNIQUE NOT NULL,
    full_name CHARACTER VARYING(255) NOT NULL,
    phone CHARACTER VARYING(20),
    
    -- البيانات الوظيفية
    department CHARACTER VARYING(255),
    position CHARACTER VARYING(255),
    
    -- إعدادات الحساب
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    account_type CHARACTER VARYING(20) DEFAULT 'user',
    max_devices INTEGER DEFAULT 3,
    storage_quota_mb INTEGER DEFAULT 1000,
    
    -- إدارة الجلسات
    last_seen TIMESTAMP WITH TIME ZONE,
    is_online BOOLEAN DEFAULT false,
    current_session_id UUID,
    total_sessions INTEGER DEFAULT 0,
    
    -- بيانات الموقع
    last_location_lat DOUBLE PRECISION,
    last_location_lng DOUBLE PRECISION,
    last_location_name TEXT,
    
    -- طوابع زمنية
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_users_unified_email ON public.users_unified(email);
CREATE INDEX idx_users_unified_national_id ON public.users_unified(national_id);
CREATE INDEX idx_users_unified_is_active ON public.users_unified(is_active);
CREATE INDEX idx_users_unified_last_sign_in ON public.users_unified(last_sign_in_at);
CREATE INDEX idx_users_unified_is_online ON public.users_unified(is_online);

-- ===== 🔄 المرحلة 4: دمج البيانات =====

-- دمج البيانات من كلا الجدولين
INSERT INTO public.users_unified (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    last_sign_in_at,
    confirmation_token,
    recovery_token,
    email_change_token_new,
    email_change,
    raw_app_meta_data,
    raw_user_meta_data,
    national_id,
    full_name,
    phone,
    department,
    position,
    is_active,
    is_admin,
    account_type,
    max_devices,
    storage_quota_mb,
    last_seen,
    is_online,
    current_session_id,
    total_sessions,
    last_location_lat,
    last_location_lng,
    last_location_name,
    created_at,
    updated_at
)
SELECT 
    COALESCE(au.id, pu.id) as id,
    COALESCE(au.email, pu.email) as email,
    au.encrypted_password,
    au.email_confirmed_at,
    COALESCE(au.last_sign_in_at, pu.last_login) as last_sign_in_at,
    au.confirmation_token,
    au.recovery_token,
    au.email_change_token_new,
    au.email_change,
    au.raw_app_meta_data,
    au.raw_user_meta_data,
    COALESCE(pu.national_id, REPLACE(au.email, '@moon-memory.com', '')) as national_id,
    COALESCE(pu.full_name, au.raw_user_meta_data->>'full_name', 'مستخدم مدمج') as full_name,
    COALESCE(pu.phone, au.phone) as phone,
    pu.department,
    pu.position,
    COALESCE(pu.is_active, true) as is_active,
    COALESCE(pu.is_admin, false) as is_admin,
    COALESCE(pu.account_type, 'user') as account_type,
    COALESCE(pu.max_devices, 3) as max_devices,
    COALESCE(pu.storage_quota_mb, 1000) as storage_quota_mb,
    COALESCE(pu.last_seen, au.last_sign_in_at) as last_seen,
    COALESCE(pu.is_online, false) as is_online,
    pu.current_session_id,
    COALESCE(pu.total_sessions, 0) as total_sessions,
    pu.last_location_lat,
    pu.last_location_lng,
    pu.last_location_name,
    COALESCE(au.created_at, pu.created_at, NOW()) as created_at,
    COALESCE(pu.updated_at, au.updated_at, NOW()) as updated_at
FROM auth.users au
FULL OUTER JOIN public.users pu ON au.id = pu.id;

-- تحديث البيانات المفقودة
UPDATE public.users_unified 
SET 
    national_id = REPLACE(email, '@moon-memory.com', '')
WHERE national_id IS NULL OR national_id = '';

UPDATE public.users_unified 
SET 
    full_name = 'مستخدم مدمج - ' || SUBSTRING(email, 1, POSITION('@' IN email) - 1)
WHERE full_name IS NULL OR full_name = '';

-- ===== 📊 المرحلة 5: التحقق من النتائج =====

-- عرض إحصائيات الدمج
SELECT 
    '=== نتائج الدمج ===' as merge_results,
    COUNT(*) as total_unified_users,
    COUNT(CASE WHEN encrypted_password IS NOT NULL THEN 1 END) as users_with_auth,
    COUNT(CASE WHEN department IS NOT NULL THEN 1 END) as users_with_department,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
    COUNT(CASE WHEN is_admin = true THEN 1 END) as admin_users
FROM public.users_unified;

-- عرض عينة من البيانات المدمجة
SELECT 
    '=== عينة من البيانات المدمجة ===' as sample_data,
    national_id,
    full_name,
    email,
    department,
    is_active,
    created_at
FROM public.users_unified
ORDER BY created_at
LIMIT 5;

-- ===== 🔗 المرحلة 6: إنشاء Views للتوافق =====

-- إنشاء view لـ auth.users للتوافق مع Supabase
CREATE OR REPLACE VIEW auth.users_view AS
SELECT
    id,
    NULL::UUID as instance_id,
    'authenticated' as aud,
    'authenticated' as role,
    email,
    encrypted_password,
    email_confirmed_at,
    NULL::TIMESTAMP WITH TIME ZONE as invited_at,
    confirmation_token,
    NULL::TIMESTAMP WITH TIME ZONE as confirmation_sent_at,
    recovery_token,
    NULL::TIMESTAMP WITH TIME ZONE as recovery_sent_at,
    email_change_token_new,
    email_change,
    NULL::TIMESTAMP WITH TIME ZONE as email_change_sent_at,
    last_sign_in_at,
    raw_app_meta_data,
    raw_user_meta_data,
    false as is_super_admin,
    created_at,
    updated_at,
    phone,
    NULL::TIMESTAMP WITH TIME ZONE as phone_confirmed_at,
    ''::TEXT as phone_change,
    ''::CHARACTER VARYING(255) as phone_change_token,
    NULL::TIMESTAMP WITH TIME ZONE as phone_change_sent_at,
    email_confirmed_at as confirmed_at,
    ''::CHARACTER VARYING(255) as email_change_token_current,
    0::SMALLINT as email_change_confirm_status,
    NULL::TIMESTAMP WITH TIME ZONE as banned_until,
    ''::CHARACTER VARYING(255) as reauthentication_token,
    NULL::TIMESTAMP WITH TIME ZONE as reauthentication_sent_at,
    false as is_sso_user,
    NULL::TIMESTAMP WITH TIME ZONE as deleted_at,
    false as is_anonymous
FROM public.users_unified;

-- إنشاء view لـ public.users للتوافق مع التطبيق
CREATE OR REPLACE VIEW public.users_view AS
SELECT
    id,
    national_id,
    full_name,
    email,
    phone,
    NULL::TEXT as password_hash,
    is_active,
    is_admin,
    account_type,
    max_devices,
    storage_quota_mb,
    department,
    position,
    last_sign_in_at as last_login,
    created_at,
    updated_at,
    last_seen,
    is_online,
    current_session_id,
    total_sessions,
    last_location_lat,
    last_location_lng,
    last_location_name
FROM public.users_unified;

-- ===== 🔧 المرحلة 7: إنشاء دوال مساعدة =====

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_users_unified_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_users_unified_updated_at ON public.users_unified;
CREATE TRIGGER trigger_update_users_unified_updated_at
    BEFORE UPDATE ON public.users_unified
    FOR EACH ROW
    EXECUTE FUNCTION update_users_unified_updated_at();

-- دالة إنشاء مستخدم جديد في الجدول الموحد
CREATE OR REPLACE FUNCTION create_unified_user(
    p_national_id TEXT,
    p_full_name TEXT,
    p_email TEXT,
    p_password TEXT DEFAULT NULL,
    p_department TEXT DEFAULT NULL,
    p_position TEXT DEFAULT NULL,
    p_is_admin BOOLEAN DEFAULT FALSE
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
    generated_email TEXT;
    result JSON;
BEGIN
    -- إنشاء UUID جديد
    new_user_id := gen_random_uuid();

    -- إنشاء البريد الإلكتروني إذا لم يتم توفيره
    generated_email := COALESCE(p_email, p_national_id || '@moon-memory.com');

    -- إدراج المستخدم الجديد
    INSERT INTO public.users_unified (
        id,
        national_id,
        full_name,
        email,
        encrypted_password,
        email_confirmed_at,
        department,
        position,
        is_active,
        is_admin,
        account_type,
        max_devices,
        storage_quota_mb,
        created_at,
        updated_at
    ) VALUES (
        new_user_id,
        p_national_id,
        p_full_name,
        generated_email,
        CASE
            WHEN p_password IS NOT NULL THEN crypt(p_password, gen_salt('bf'))
            ELSE crypt(p_national_id, gen_salt('bf'))
        END,
        NOW(),
        p_department,
        p_position,
        true,
        p_is_admin,
        CASE WHEN p_is_admin THEN 'admin' ELSE 'user' END,
        CASE WHEN p_is_admin THEN 10 ELSE 1 END,
        CASE WHEN p_is_admin THEN 10000 ELSE 1000 END,
        NOW(),
        NOW()
    );

    result := json_build_object(
        'success', true,
        'user_id', new_user_id,
        'email', generated_email,
        'password', COALESCE(p_password, p_national_id),
        'message', 'تم إنشاء المستخدم بنجاح في الجدول الموحد'
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'message', 'فشل في إنشاء المستخدم'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث حالة تسجيل الدخول
CREATE OR REPLACE FUNCTION update_user_login_status(
    p_user_id UUID,
    p_is_online BOOLEAN DEFAULT TRUE,
    p_session_id UUID DEFAULT NULL,
    p_location_lat DOUBLE PRECISION DEFAULT NULL,
    p_location_lng DOUBLE PRECISION DEFAULT NULL,
    p_location_name TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.users_unified
    SET
        last_sign_in_at = CASE WHEN p_is_online THEN NOW() ELSE last_sign_in_at END,
        last_seen = NOW(),
        is_online = p_is_online,
        current_session_id = CASE WHEN p_is_online THEN p_session_id ELSE NULL END,
        total_sessions = CASE WHEN p_is_online THEN total_sessions + 1 ELSE total_sessions END,
        last_location_lat = COALESCE(p_location_lat, last_location_lat),
        last_location_lng = COALESCE(p_location_lng, last_location_lng),
        last_location_name = COALESCE(p_location_name, last_location_name),
        updated_at = NOW()
    WHERE id = p_user_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== 🛡️ المرحلة 8: إعداد الصلاحيات =====

-- منح الصلاحيات للأدوار المختلفة
GRANT ALL PRIVILEGES ON public.users_unified TO postgres;
GRANT ALL PRIVILEGES ON public.users_unified TO service_role;
GRANT SELECT, INSERT, UPDATE ON public.users_unified TO authenticated;
GRANT SELECT ON public.users_unified TO anon;

-- منح الصلاحيات على الدوال
GRANT EXECUTE ON FUNCTION create_unified_user TO service_role;
GRANT EXECUTE ON FUNCTION update_user_login_status TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_login_status TO service_role;

-- منح الصلاحيات على Views
GRANT SELECT ON auth.users_view TO supabase_auth_admin;
GRANT SELECT ON public.users_view TO authenticated;
GRANT SELECT ON public.users_view TO service_role;

-- ===== 🔍 المرحلة 9: التحقق النهائي =====

-- فحص شامل للبيانات المدمجة
SELECT
    '=== التحقق النهائي ===' as final_check,
    'users_unified' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as records_with_email,
    COUNT(CASE WHEN national_id IS NOT NULL THEN 1 END) as records_with_national_id,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as records_with_name,
    COUNT(CASE WHEN encrypted_password IS NOT NULL THEN 1 END) as records_with_password,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_records
FROM public.users_unified;

-- فحص التطابق مع الجداول الأصلية
SELECT
    '=== مقارنة مع الجداول الأصلية ===' as comparison,
    (SELECT COUNT(*) FROM auth.users) as original_auth_users,
    (SELECT COUNT(*) FROM public.users) as original_public_users,
    (SELECT COUNT(*) FROM public.users_unified) as unified_users,
    CASE
        WHEN (SELECT COUNT(*) FROM public.users_unified) >=
             GREATEST((SELECT COUNT(*) FROM auth.users), (SELECT COUNT(*) FROM public.users))
        THEN 'نجح الدمج ✅'
        ELSE 'فشل الدمج ❌'
    END as merge_status;

-- ===== 📝 المرحلة 10: تعليمات ما بعد التنفيذ =====

SELECT '
=== 📋 تعليمات ما بعد التنفيذ ===

✅ تم إنشاء الجدول الموحد: public.users_unified
✅ تم إنشاء Views للتوافق: auth.users_view, public.users_view
✅ تم إنشاء دوال مساعدة للإدارة
✅ تم إعداد الصلاحيات والفهارس

🔄 الخطوات التالية:

1. اختبار الجدول الموحد:
   SELECT * FROM public.users_unified LIMIT 5;

2. اختبار إنشاء مستخدم جديد:
   SELECT create_unified_user(''test123'', ''مستخدم تجريبي'', ''<EMAIL>'');

3. اختبار تحديث حالة تسجيل الدخول:
   SELECT update_user_login_status(''user-id-here'', true);

4. تحديث كود التطبيقين للاستخدام:
   - استبدال "users" بـ "users_unified"
   - استخدام الدوال الجديدة للإدارة

⚠️ تحذيرات:
- لا تحذف الجداول الأصلية حتى التأكد من عمل كل شيء
- اختبر جميع وظائف التطبيق قبل النشر
- راقب الأداء والأخطاء لمدة 24 ساعة

🎯 عند التأكد من الاستقرار:
- يمكن حذف backup_auth_users و backup_public_users
- يمكن إعادة تسمية users_unified إلى users
- يمكن حذف الجداول الأصلية

' as post_execution_instructions;
