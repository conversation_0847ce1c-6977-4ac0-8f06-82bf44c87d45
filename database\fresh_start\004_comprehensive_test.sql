-- اختبار شامل للنظام الجديد
-- Comprehensive Test for New System
-- Date: 2025-01-12

-- إدراج مستخدم تجريبي إذا لم يكن موجود
INSERT INTO users (id, full_name, email) 
VALUES ('a505a910-c192-4213-b3fc-63fd59d0659b'::UUID, 'Test User', '<EMAIL>')
ON CONFLICT (id) DO NOTHING;

-- فحص بنية الجدول الجديد
SELECT 'Table Structure Check' as test_name;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
ORDER BY ordinal_position;

-- اختبار 1: إضافة جهاز Samsung Galaxy S23
SELECT 'Test 1: Adding Samsung Galaxy S23' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_samsung_s23_new_001',
    'android_samsung_001',
    'samsung/beyond1lte/beyond1:13/TP1A.220624.014/G973FXXS9FVL1:user/release-keys',
    92.5,
    'high',
    'Samsung Galaxy S23',
    'SM-G991B',
    'Samsung',
    'beyond1lte',
    'qcom',
    'HW:qcom|BD:beyond1lte|BL:G973FXXS9FVL1|DV:beyond1',
    'OS:13|SDK:33|PATCH:2022-06-01|CODE:TIRAMISU',
    'SCR:1080x2340|DPI:420',
    'CPU:Snapdragon888|CORES:8',
    'STG:128GB|RAM:8GB',
    'samsung_s23_raw_data_001'
);

-- اختبار 2: إضافة جهاز Google Pixel 7 Pro
SELECT 'Test 2: Adding Google Pixel 7 Pro' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_pixel_7_new_002',
    'android_pixel_002',
    'google/cheetah/cheetah:14/UQ1A.240205.004/11269751:user/release-keys',
    95.0,
    'high',
    'Google Pixel 7 Pro',
    'GE2AE',
    'Google',
    'cheetah',
    'google',
    'HW:google|BD:cheetah|BL:UQ1A.240205.004|DV:cheetah',
    'OS:14|SDK:34|PATCH:2024-02-05|CODE:UPSIDE_DOWN_CAKE',
    'SCR:1440x3120|DPI:512',
    'CPU:Tensor2|CORES:8',
    'STG:256GB|RAM:12GB',
    'pixel_7_raw_data_002'
);

-- اختبار 3: تحديث الجهاز الأول
SELECT 'Test 3: Updating Samsung device' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_samsung_s23_updated_001',
    'android_samsung_001', -- نفس Android ID
    'samsung/beyond1lte/beyond1:14/TP1A.220624.015/G973FXXS9FVL2:user/release-keys',
    89.0,
    'medium',
    'Samsung Galaxy S23 Updated',
    'SM-G991B',
    'Samsung'
);

-- اختبار 4: إضافة جهاز ثالث
SELECT 'Test 4: Adding Xiaomi 13 Pro' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_xiaomi_13_new_003',
    'android_xiaomi_003',
    'xiaomi/marble/marble:13/TKQ1.220829.002/V14.0.3.0.TMRMIXM:user/release-keys',
    88.0,
    'medium',
    'Xiaomi 13 Pro',
    '2210132C',
    'Xiaomi',
    'marble',
    'qcom',
    'HW:qcom|BD:marble|BL:V14.0.3.0.TMRMIXM|DV:marble',
    'OS:13|SDK:33|PATCH:2022-08-29|CODE:TIRAMISU',
    'SCR:1440x3200|DPI:522',
    'CPU:Snapdragon8Gen2|CORES:8',
    'STG:256GB|RAM:12GB',
    'xiaomi_13_raw_data_003'
);

-- اختبار 5: محاولة إضافة جهاز رابع - يجب أن يفشل
SELECT 'Test 5: Adding fourth device - should fail' as test_name;
SELECT verify_device_enhanced(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_oneplus_11_new_004',
    'android_oneplus_004',
    'oneplus/OP594DL1/OP594DL1:13/TP1A.220905.001/Q.************:user/release-keys',
    85.0,
    'medium',
    'OnePlus 11',
    'CPH2449',
    'OnePlus'
);

-- اختبار 6: الحصول على قائمة الأجهزة
SELECT 'Test 6: Getting user devices' as test_name;
SELECT get_user_devices('a505a910-c192-4213-b3fc-63fd59d0659b'::UUID);

-- اختبار 7: محاولة مصادقة فاشلة
SELECT 'Test 7: Failed authentication attempt' as test_name;
SELECT record_failed_auth(
    'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID,
    'fp_samsung_s23_updated_001',
    'wrong_android_id'
);

-- فحص البيانات المدخلة
SELECT 'Final Data Check' as test_name;
SELECT 
    device_name,
    device_brand,
    device_model,
    confidence_score,
    trust_level,
    auth_attempts,
    is_blocked,
    last_verified_at,
    created_at
FROM devices 
WHERE user_id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID
ORDER BY created_at DESC;

-- إحصائيات شاملة
SELECT 'Comprehensive Statistics' as test_name;
SELECT 
    COUNT(*) as total_devices,
    ROUND(AVG(confidence_score), 2) as avg_confidence_score,
    MAX(confidence_score) as max_confidence,
    MIN(confidence_score) as min_confidence,
    COUNT(*) FILTER (WHERE trust_level = 'high') as high_trust_devices,
    COUNT(*) FILTER (WHERE trust_level = 'medium') as medium_trust_devices,
    COUNT(*) FILTER (WHERE trust_level = 'low') as low_trust_devices,
    COUNT(*) FILTER (WHERE is_blocked = TRUE) as blocked_devices,
    COUNT(*) FILTER (WHERE auth_attempts > 0) as devices_with_failed_attempts
FROM devices 
WHERE user_id = 'a505a910-c192-4213-b3fc-63fd59d0659b'::UUID;

-- فحص الفهارس
SELECT 'Indexes Check' as test_name;
SELECT indexname, indexdef
FROM pg_indexes 
WHERE tablename = 'devices'
ORDER BY indexname;

-- فحص السياسات
SELECT 'Policies Check' as test_name;
SELECT policyname, cmd, qual
FROM pg_policies 
WHERE tablename = 'devices';

-- رسالة النجاح النهائية
SELECT 'جميع الاختبارات مكتملة! النظام الجديد يعمل بالكامل! 🎉' as final_status;
