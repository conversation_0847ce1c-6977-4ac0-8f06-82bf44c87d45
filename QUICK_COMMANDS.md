# أوامر سريعة لنشر تطبيق ذاكرة القمر

## 📁 معلومات الملف الجاهز

```
✅ ملف App Bundle جاهز للرفع:
📍 المسار: build\app\outputs\bundle\release\app-release.aab
📏 الحجم: 33.1 MB (33,067,501 bytes)
📅 تاريخ الإنشاء: 18/7/2025 8:46 AM
🔢 رقم الإصدار: 2.0.0 (كود: 2)
🆔 معرف التطبيق: com.moonmemory.moon_memory_camera
```

## 🚀 أوامر البناء السريعة

### بناء App Bundle جديد (إذا احتجت)
```bash
# تنظيف المشروع
flutter clean

# تحديث التبعيات
flutter pub get

# بناء App Bundle للإنتاج
flutter build appbundle --release
```

### بناء APK (للاختبار المحلي)
```bash
flutter build apk --release
```

### التحقق من حالة البناء
```bash
# عرض معلومات الملف
dir "build\app\outputs\bundle\release\app-release.aab"

# التحقق من التوقيع
jarsigner -verify -verbose -certs "build\app\outputs\bundle\release\app-release.aab"
```

## 📋 قائمة التحقق السريعة

### قبل الرفع تأكد من:
- [x] ملف App Bundle موجود ✅
- [x] مفتاح التوقيع آمن ✅
- [x] رقم الإصدار صحيح ✅
- [ ] حساب Google Play Console جاهز
- [ ] الصور والأيقونات جاهزة
- [ ] سياسة الخصوصية جاهزة
- [ ] وصف التطبيق جاهز

## 🔗 روابط سريعة

### Google Play Console
```
https://play.google.com/console
```

### إنشاء سياسة خصوصية
```
https://www.privacypolicytemplate.net
```

### أدوات تصميم الصور
```
https://www.canva.com
https://www.figma.com
```

## 📱 معلومات التطبيق للنسخ

### اسم التطبيق
```
ذاكرة القمر
```

### الوصف القصير (80 حرف)
```
تطبيق لتوثيق الإنجازات بالصور والفيديو مع العلامات المائية
```

### معرف التطبيق
```
com.moonmemory.moon_memory_camera
```

### الكلمات المفتاحية
```
كاميرا، توثيق، إنجازات، علامة مائية، موقع، مؤسسات، شركات، أمان، سحابة، عربي
```

## 🎨 متطلبات الصور

### أيقونة التطبيق
- **الحجم**: 512x512 بكسل
- **التنسيق**: PNG
- **الخلفية**: شفافة أو ملونة

### صورة مميزة
- **الحجم**: 1024x500 بكسل
- **التنسيق**: PNG أو JPG

### لقطات الشاشة
- **العدد**: 2-8 صور
- **الحجم**: 16:9 أو 9:16
- **الدقة المقترحة**: 1080x1920 أو 1920x1080

## 🔄 تحديث الإصدار (للمستقبل)

### زيادة رقم الإصدار
```yaml
# في pubspec.yaml
version: 2.1.0+3  # زيادة الرقم
```

```gradle
// في android/app/build.gradle
versionCode 3      // زيادة الرقم
versionName "2.1.0"
```

### بناء التحديث
```bash
flutter clean
flutter pub get
flutter build appbundle --release
```

## 🆘 حل المشاكل الشائعة

### خطأ في التوقيع
```bash
# التحقق من مفتاح التوقيع
keytool -list -v -keystore android/app/my-release-key.jks
```

### خطأ في البناء
```bash
# تنظيف شامل
flutter clean
cd android
./gradlew clean
cd ..
flutter pub get
```

### خطأ في Gradle
```bash
# تحديث Gradle Wrapper
cd android
./gradlew wrapper --gradle-version 8.7
cd ..
```

## 📞 الدعم

### إذا واجهت مشاكل:
1. راجع `GOOGLE_PLAY_DEPLOYMENT_GUIDE.md`
2. راجع `DEPLOYMENT_CHECKLIST.md`
3. تحقق من وثائق Flutter الرسمية
4. تحقق من Google Play Console Help

### ملفات مهمة:
- `build\app\outputs\bundle\release\app-release.aab` - الملف للرفع
- `android\app\my-release-key.jks` - مفتاح التوقيع (احتفظ به آمناً!)
- `android\key.properties` - إعدادات المفتاح

---

**تذكير مهم**: احتفظ بنسخة احتياطية من مفتاح التوقيع في مكان آمن!
