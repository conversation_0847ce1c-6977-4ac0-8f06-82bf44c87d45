-- 🚀 تطبيق جميع التحسينات على قاعدة البيانات
-- Apply All Database Optimizations
-- Date: 2025-01-17
-- Version: 1.0 - Complete Application

-- ===== 📋 معلومات التطبيق =====
SELECT '🚀 بدء تطبيق جميع التحسينات على قاعدة البيانات' as status;
SELECT 'Moon Memory System - Database Optimizations' as project;
SELECT NOW() as start_time;

-- ===== 🔧 الخطوة 1: إصلاح RLS مؤقتاً =====
SELECT '🔧 الخطوة 1: إصلاح Row Level Security مؤقتاً...' as step;

-- إيقاف RLS مؤقتاً للتطبيق
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos DISABLE ROW LEVEL SECURITY;

-- إن<PERSON><PERSON><PERSON> جدول locations إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    location_code TEXT UNIQUE NOT NULL,
    location_type TEXT NOT NULL CHECK (location_type IN ('U', 'C')),
    location_number TEXT NOT NULL,
    location_name_ar TEXT,
    location_name_en TEXT,
    sort_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    total_photos INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE public.locations DISABLE ROW LEVEL SECURITY;

SELECT '✅ تم إيقاف RLS مؤقتاً' as rls_status;

-- ===== 📍 الخطوة 2: إضافة نظام المواقع الـ 70 =====
SELECT '📍 الخطوة 2: إضافة نظام المواقع الـ 70...' as step;

-- حذف المواقع الموجودة لإعادة إنشائها
DELETE FROM public.locations;

-- إضافة مواقع U (U101 - U125)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'U' || LPAD(i::text, 3, '0'),
    'U',
    LPAD(i::text, 3, '0'),
    'موقع U' || LPAD(i::text, 3, '0'),
    'Location U' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 125) as i
ON CONFLICT (location_code) DO NOTHING;

-- إضافة مواقع C (C101 - C145)
INSERT INTO public.locations (location_code, location_type, location_number, location_name_ar, location_name_en, sort_order, is_active)
SELECT 
    'C' || LPAD(i::text, 3, '0'),
    'C',
    LPAD(i::text, 3, '0'),
    'موقع C' || LPAD(i::text, 3, '0'),
    'Location C' || LPAD(i::text, 3, '0'),
    i,
    true
FROM generate_series(101, 145) as i
ON CONFLICT (location_code) DO NOTHING;

SELECT '✅ تم إضافة ' || COUNT(*) || ' موقع' as locations_added FROM public.locations;

-- ===== 🔄 الخطوة 3: إضافة الحقول المحسوبة =====
SELECT '🔄 الخطوة 3: إضافة الحقول المحسوبة...' as step;

-- إضافة الحقول للصور
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0;

-- إضافة الحقول للفيديوهات
ALTER TABLE public.videos 
ADD COLUMN IF NOT EXISTS location_type TEXT CHECK (location_type IN ('U', 'C')),
ADD COLUMN IF NOT EXISTS location_number TEXT,
ADD COLUMN IF NOT EXISTS full_location_code TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active',
ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS duration_seconds INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS resolution TEXT;

SELECT '✅ تم إضافة الحقول المحسوبة' as computed_fields;

-- ===== 📊 الخطوة 4: إنشاء الفهارس المحسنة =====
SELECT '📊 الخطوة 4: إنشاء الفهارس المحسنة...' as step;

-- فهارس الصور
CREATE INDEX IF NOT EXISTS idx_photos_user_active ON public.photos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_location_date ON public.photos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_full_location ON public.photos(full_location_code, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_photos_upload_date ON public.photos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس الفيديوهات
CREATE INDEX IF NOT EXISTS idx_videos_user_active ON public.videos(user_id) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_location_date ON public.videos(location_type, location_number, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_full_location ON public.videos(full_location_code, capture_timestamp DESC) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_videos_upload_date ON public.videos(upload_timestamp DESC) WHERE status = 'active';

-- فهارس المواقع
CREATE INDEX IF NOT EXISTS idx_locations_type_sort ON public.locations(location_type, sort_order);
CREATE INDEX IF NOT EXISTS idx_locations_active ON public.locations(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_locations_usage ON public.locations(total_photos + total_videos DESC) WHERE is_active = true;

-- فهارس المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_active ON public.users(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_created ON public.users(created_at DESC);

SELECT '✅ تم إنشاء ' || COUNT(*) || ' فهرس محسن' as indexes_created 
FROM pg_indexes WHERE schemaname = 'public' AND indexname LIKE 'idx_%';

-- ===== ⚡ الخطوة 5: إنشاء الدوال المحسنة =====
SELECT '⚡ الخطوة 5: إنشاء الدوال المحسنة...' as step;

-- دالة تحديث الإحصائيات
CREATE OR REPLACE FUNCTION update_location_statistics(location_code_param TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE public.locations 
    SET 
        total_photos = (
            SELECT COUNT(*) FROM public.photos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        total_videos = (
            SELECT COUNT(*) FROM public.videos 
            WHERE full_location_code = location_code_param AND status = 'active'
        ),
        last_used_at = GREATEST(
            (SELECT MAX(capture_timestamp) FROM public.photos WHERE full_location_code = location_code_param),
            (SELECT MAX(capture_timestamp) FROM public.videos WHERE full_location_code = location_code_param)
        ),
        updated_at = NOW()
    WHERE location_code = location_code_param;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث جميع الإحصائيات
CREATE OR REPLACE FUNCTION update_all_locations_statistics()
RETURNS TEXT AS $$
DECLARE
    location_rec RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR location_rec IN SELECT location_code FROM public.locations WHERE is_active = true
    LOOP
        PERFORM update_location_statistics(location_rec.location_code);
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN 'تم تحديث إحصائيات ' || updated_count || ' موقع';
END;
$$ LANGUAGE plpgsql;

-- دالة الحصول على الصور مرتبة
CREATE OR REPLACE FUNCTION get_photos_sorted_optimized(
    p_user_id UUID DEFAULT NULL,
    p_location_type TEXT DEFAULT NULL,
    p_sort_by TEXT DEFAULT 'date_desc',
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    file_name TEXT,
    url TEXT,
    location TEXT,
    location_type TEXT,
    location_number TEXT,
    full_location_code TEXT,
    username TEXT,
    capture_timestamp TIMESTAMP WITH TIME ZONE,
    upload_timestamp TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.user_id,
        p.file_name,
        p.url,
        p.location,
        p.location_type,
        p.location_number,
        p.full_location_code,
        p.username,
        p.capture_timestamp,
        p.upload_timestamp
    FROM public.photos p
    WHERE 
        (p_user_id IS NULL OR p.user_id = p_user_id)
        AND (p_location_type IS NULL OR p.location_type = p_location_type)
        AND p.status = 'active'
    ORDER BY 
        CASE WHEN p_sort_by = 'date_desc' THEN p.capture_timestamp END DESC,
        CASE WHEN p_sort_by = 'date_asc' THEN p.capture_timestamp END ASC,
        CASE WHEN p_sort_by = 'location_date' THEN p.location_type END,
        CASE WHEN p_sort_by = 'location_date' THEN p.location_number END,
        CASE WHEN p_sort_by = 'location_date' THEN p.capture_timestamp END DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

SELECT '✅ تم إنشاء الدوال المحسنة' as functions_created;

-- ===== 🔄 الخطوة 6: تحديث البيانات الموجودة =====
SELECT '🔄 الخطوة 6: تحديث البيانات الموجودة...' as step;

-- تحديث full_location_code للصور الموجودة
UPDATE public.photos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL AND full_location_code IS NULL;

-- تحديث full_location_code للفيديوهات الموجودة
UPDATE public.videos 
SET full_location_code = location_type || location_number 
WHERE location_type IS NOT NULL AND location_number IS NOT NULL AND full_location_code IS NULL;

-- تحديث إحصائيات المواقع
SELECT update_all_locations_statistics() as statistics_update;

SELECT '✅ تم تحديث البيانات الموجودة' as data_updated;

-- ===== 🔐 الخطوة 7: إعادة تفعيل RLS الآمن =====
SELECT '🔐 الخطوة 7: إعادة تفعيل Row Level Security الآمن...' as step;

-- إعادة تفعيل RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- إنشاء السياسات الآمنة
CREATE POLICY users_secure_policy ON public.users FOR ALL USING (auth.uid() = id);
CREATE POLICY photos_secure_policy ON public.photos FOR ALL USING (auth.uid() = user_id);
CREATE POLICY videos_secure_policy ON public.videos FOR ALL USING (auth.uid() = user_id);
CREATE POLICY locations_read_policy ON public.locations FOR SELECT USING (true);

SELECT '✅ تم إعادة تفعيل RLS الآمن' as rls_restored;

-- ===== 📊 الخطوة 8: تقرير النتائج النهائي =====
SELECT '📊 تقرير النتائج النهائي:' as final_report;
SELECT COUNT(*) as total_locations FROM public.locations;
SELECT COUNT(*) as total_photos FROM public.photos;
SELECT COUNT(*) as total_videos FROM public.videos;
SELECT COUNT(*) as total_users FROM public.users;
SELECT COUNT(*) as total_indexes FROM pg_indexes WHERE schemaname = 'public' AND indexname LIKE 'idx_%';

-- ===== 🎉 رسالة النجاح =====
SELECT '🎉 تم تطبيق جميع التحسينات بنجاح!' as success_message;
SELECT 'Moon Memory System - Database Optimized ✅' as project_status;
SELECT NOW() as completion_time;
