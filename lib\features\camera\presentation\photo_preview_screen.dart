import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/image_processor.dart';
import '../../../core/services/supabase_service.dart';
import '../../../core/services/auto_upload_service.dart';
import '../../../core/services/session_service.dart';
import '../../../core/services/enhanced_upload_service.dart';
import 'package:logger/logger.dart';

class PhotoPreviewScreen extends ConsumerStatefulWidget {
  final String photoPath;
  final String? location; // الموقع المختار (مثل U101, C145)
  final String? geoLocation; // الموقع الجغرافي الحقيقي
  final String? username; // اسم المستخدم الممرر من الكاميرا

  const PhotoPreviewScreen({
    super.key,
    required this.photoPath,
    this.location,
    this.geoLocation,
    this.username,
  });

  @override
  ConsumerState<PhotoPreviewScreen> createState() => _PhotoPreviewScreenState();
}

class _PhotoPreviewScreenState extends ConsumerState<PhotoPreviewScreen> {
  String? _processedImagePath;
  bool _isUploading = false;
  bool _isUploaded = false;
  bool _isProcessing = true;
  final _logger = Logger();

  @override
  void initState() {
    super.initState();
    // بدء معالجة الصورة في الخلفية
    _processImage();
  }

  Future<void> _processImage() async {
    try {
      String username = '';

      // إذا تم تمرير اسم المستخدم من الكاميرا، استخدمه مباشرة
      if (widget.username != null && widget.username!.isNotEmpty) {
        username = widget.username!;
        _logger.i('Username loaded from camera screen: $username');
      } else {
        // إذا لم يتم تمرير اسم المستخدم، حاول جلبه بالطرق الأخرى
        final user = ref.read(supabaseServiceProvider).client.auth.currentUser;

        if (user != null) {
          // محاولة جلب اسم المستخدم من قاعدة البيانات أولاً
          try {
            final response = await ref.read(supabaseServiceProvider).client
                .from('users')
                .select('full_name')
                .eq('id', user.id)
                .single();
            username = response['full_name'] as String? ?? '';
            _logger.i('Username loaded from database: $username');
          } catch (e) {
            _logger.w('Failed to load username from database: $e');

            // في حالة فشل الاتصال، استخدم البيانات المحفوظة محلياً
            final prefs = await SharedPreferences.getInstance();
            final userDataString = prefs.getString('user_data');

            if (userDataString != null) {
              try {
                final userData = jsonDecode(userDataString) as Map<String, dynamic>;
                username = userData['full_name'] as String? ?? 'مستخدم';
                _logger.i('Username loaded from local storage: $username');
              } catch (e) {
                _logger.e('Error parsing local user data: $e');
                username = 'مستخدم';
              }
            } else {
              username = 'مستخدم';
              _logger.w('No local user data found, using default username');
            }
          }
        } else {
          username = 'مستخدم';
          _logger.w('No authenticated user found');
        }
      }

      _logger.i('Processing image with username: $username');

      final processedPath = await ImageProcessor.processImage(
        imagePath: widget.photoPath,
        username: username,
        location: widget.location ?? 'لم يتم تحديد الموقع',
        geoLocation: widget.geoLocation,
      );

      if (mounted) {
        setState(() {
          _processedImagePath = processedPath;
          _isProcessing = false;
        });
      }

      _logger.i('Image processing completed successfully');
    } catch (e) {
      _logger.e('Error processing image: $e');
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _saveAndShare() async {
    if (_processedImagePath == null) return;

    if (!_isUploaded) {
      setState(() {
        _isUploading = true;
      });

      try {
        // حفظ الصورة في المعرض المحلي دائماً
        await _saveToGallery();

        // 🆕 تسجيل نشاط التقاط الصورة
        await _logPhotoActivity();

        // بدء الرفع التلقائي في الخلفية
        _startBackgroundUpload();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'photo.saved_to_gallery'.tr(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'upload.background_upload'.tr(),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'photo.share_button'.tr(),
              textColor: Colors.white,
              onPressed: () => _sharePhoto(),
            ),
          ),
        );

        setState(() {
          _isUploaded = true;
          _isUploading = false;
        });
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'photo.save_error'.tr(),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );

        setState(() {
          _isUploading = false;
        });
        return;
      }
    } else {
      // مشاركة الصورة
      try {
        await Share.shareXFiles([XFile(_processedImagePath!)]);
      } catch (e) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'camera.photo_share_error'.tr(),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _saveToGallery() async {
    // إنشاء مجلد المعرض المحلي
    final directory = await getApplicationDocumentsDirectory();
    final galleryDir = Directory('${directory.path}/gallery');
    if (!await galleryDir.exists()) {
      await galleryDir.create(recursive: true);
    }

    // نسخ الصورة إلى المعرض
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'photo_$timestamp.jpg';
    final savedPath = '${galleryDir.path}/$fileName';

    await File(_processedImagePath!).copy(savedPath);

    // حفظ معلومات الصورة للرفع التلقائي
    await _saveUploadMetadata(savedPath, fileName);

    _logger.i('Photo saved to gallery: $savedPath');
  }

  Future<void> _saveUploadMetadata(String filePath, String fileName) async {
    // استخدام الخدمة المحسنة بدلاً من SharedPreferences
    final enhancedUploadService = EnhancedUploadService();

    // حساب حجم الملف
    int? fileSize;
    try {
      final file = File(filePath);
      if (await file.exists()) {
        fileSize = await file.length();
      }
    } catch (e) {
      _logger.w('⚠️ فشل في حساب حجم الملف: $e');
    }

    final queueId = await enhancedUploadService.addToUploadQueue(
      filePath: filePath,
      fileName: fileName,
      fileType: 'photo',
      location: widget.location ?? 'unknown',
      username: widget.username,
      fileSizeBytes: fileSize,
      metadata: {
        'geo_location': widget.geoLocation,
        'capture_timestamp': DateTime.now().toIso8601String(),
      },
    );

    if (queueId != null) {
      _logger.i('✅ تم إضافة الصورة إلى قائمة الرفع: $queueId');
    } else {
      _logger.e('❌ فشل في إضافة الصورة إلى قائمة الرفع');
    }
  }

  /// تسجيل نشاط التقاط الصورة
  Future<void> _logPhotoActivity() async {
    try {
      final userId = ref.read(supabaseServiceProvider).client.auth.currentUser?.id;
      if (userId == null) return;

      // حساب حجم الملف
      int? fileSize;
      if (_processedImagePath != null) {
        final file = File(_processedImagePath!);
        if (await file.exists()) {
          fileSize = await file.length();
        }
      }

      await SessionService().logActivity(
        userId: userId,
        activityType: 'photo_taken',
        activityData: {
          'location': widget.location,
          'geoLocation': widget.geoLocation,
          'username': widget.username,
        },
        fileSizeBytes: fileSize,
      );

      _logger.i('📸 تم تسجيل نشاط التقاط الصورة');
    } catch (e) {
      _logger.w('⚠️ فشل في تسجيل نشاط التقاط الصورة: $e');
    }
  }

  void _startBackgroundUpload() {
    // بدء الرفع التلقائي في الخلفية
    AutoUploadService().startAutoUpload();
    _logger.i('Background upload started');
  }

  void _retakePhoto() {
    Navigator.pop(context);
  }

  Future<void> _sharePhoto() async {
    if (_processedImagePath == null) return;

    try {
      await Share.shareXFiles([XFile(_processedImagePath!)]);
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'camera.photo_share_error'.tr(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // عرض الصورة (الأصلية أولاً ثم المعالجة)
          Center(
            child: Image.file(
              File(_processedImagePath ?? widget.photoPath),
              fit: BoxFit.contain,
            ),
          ),

          // مؤشر المعالجة (يظهر فقط أثناء المعالجة)
          if (_isProcessing)
            Positioned(
              top: 50,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'camera.processing'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // شريط الأزرار
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withAlpha((0.8 * 255).round()),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // زر إعادة التصوير
                  IconButton(
                    icon: const Icon(
                      Icons.refresh_rounded,
                      color: Colors.white,
                      size: 32,
                    ),
                    onPressed: _retakePhoto,
                  ),
                  
                  // زر الرفع/المشاركة
                  _isUploading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : IconButton(
                          icon: Icon(
                            _isUploaded ? Icons.share_rounded : Icons.check_rounded,
                            color: Colors.white,
                            size: 32,
                          ),
                          onPressed: _saveAndShare,
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
