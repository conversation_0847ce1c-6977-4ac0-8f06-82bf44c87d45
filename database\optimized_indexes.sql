-- 🚀 فهارس محسنة لتحسين أداء استعلامات الترتيب
-- Optimized Indexes for Sorting Performance
-- Date: 2025-01-16
-- Version: 2.0

-- ===== 📸 فهارس محسنة لجدول الصور =====

-- فهرس مركب للترتيب حسب الموقع والتاريخ (الأكثر استخداماً)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_location_date_optimized 
ON public.photos(location_type, full_location_code, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب التاريخ والموقع
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_date_location_optimized 
ON public.photos(capture_timestamp DESC, location_type, full_location_code)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب المستخدم والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_user_date_optimized 
ON public.photos(username, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب الحجم والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_size_date_optimized 
ON public.photos(file_size_bytes DESC, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب المختلط
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_mixed_sort_optimized 
ON public.photos(location_type, full_location_code, date_trunc('day', capture_timestamp) DESC, username, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس للفلترة حسب نوع الموقع
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_location_type_optimized 
ON public.photos(location_type, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس للفلترة حسب المستخدم
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_user_status_optimized 
ON public.photos(user_id, status, capture_timestamp DESC);

-- فهرس للفلترة حسب التاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_date_range_optimized 
ON public.photos(capture_timestamp, status)
WHERE status = 'active';

-- ===== 🎥 فهارس محسنة لجدول الفيديوهات =====

-- فهرس مركب للترتيب حسب الموقع والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_location_date_optimized 
ON public.videos(location_type, full_location_code, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب التاريخ والموقع
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_date_location_optimized 
ON public.videos(capture_timestamp DESC, location_type, full_location_code)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب المستخدم والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_user_date_optimized 
ON public.videos(username, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب الحجم والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_size_date_optimized 
ON public.videos(file_size_bytes DESC, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب حسب المدة والتاريخ (خاص بالفيديوهات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_duration_date_optimized 
ON public.videos(duration_seconds DESC, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس مركب للترتيب المختلط
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_mixed_sort_optimized 
ON public.videos(location_type, full_location_code, date_trunc('day', capture_timestamp) DESC, username, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس للفلترة حسب نوع الموقع
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_location_type_optimized 
ON public.videos(location_type, capture_timestamp DESC)
WHERE status = 'active';

-- فهرس للفلترة حسب المستخدم
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_user_status_optimized 
ON public.videos(user_id, status, capture_timestamp DESC);

-- ===== 📍 فهارس محسنة لجدول المواقع =====

-- فهرس مركب للربط مع الصور والفيديوهات
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_code_sort_optimized 
ON public.locations(location_code, sort_order)
WHERE is_active = true;

-- فهرس للترتيب حسب النوع والترتيب
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_type_sort_optimized 
ON public.locations(location_type, sort_order)
WHERE is_active = true;

-- فهرس للبحث في الأسماء
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_locations_names_optimized 
ON public.locations USING gin(to_tsvector('arabic', coalesce(location_name_ar, '') || ' ' || coalesce(location_name_en, '')))
WHERE is_active = true;

-- ===== 👥 فهارس محسنة لجدول المستخدمين =====

-- فهرس للبحث في أسماء المستخدمين
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_optimized 
ON public.users(username)
WHERE is_active = true;

-- فهرس للبحث في الأسماء الكاملة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_fullname_optimized 
ON public.users USING gin(to_tsvector('arabic', full_name))
WHERE is_active = true;

-- ===== 📱 فهارس محسنة لجدول الأجهزة =====

-- فهرس للبحث السريع في البصمة الرقمية
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_devices_fingerprint_optimized 
ON public.devices(device_fingerprint)
WHERE is_active = true;

-- فهرس مركب للمستخدم ومستوى الثقة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_devices_user_trust_optimized 
ON public.devices(user_id, trust_level, is_active);

-- ===== 🔍 فهارس للبحث المتقدم =====

-- فهرس للبحث في أسماء الملفات (الصور)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_filename_search_optimized 
ON public.photos USING gin(to_tsvector('simple', file_name))
WHERE status = 'active';

-- فهرس للبحث في أسماء الملفات (الفيديوهات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_filename_search_optimized 
ON public.videos USING gin(to_tsvector('simple', file_name))
WHERE status = 'active';

-- فهرس للبحث في أسماء المستخدمين (الصور)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_username_search_optimized 
ON public.photos USING gin(to_tsvector('simple', username))
WHERE status = 'active';

-- فهرس للبحث في أسماء المستخدمين (الفيديوهات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_username_search_optimized 
ON public.videos USING gin(to_tsvector('simple', username))
WHERE status = 'active';

-- ===== 📊 فهارس للإحصائيات والتقارير =====

-- فهرس لحساب الإحصائيات اليومية
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_daily_stats_optimized 
ON public.photos(date_trunc('day', capture_timestamp), status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_daily_stats_optimized 
ON public.videos(date_trunc('day', capture_timestamp), status);

-- فهرس لحساب الإحصائيات الشهرية
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_photos_monthly_stats_optimized 
ON public.photos(date_trunc('month', capture_timestamp), status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_videos_monthly_stats_optimized 
ON public.videos(date_trunc('month', capture_timestamp), status);

-- ===== 🧹 تنظيف الفهارس القديمة =====

-- حذف الفهارس القديمة غير المحسنة (إذا كانت موجودة)
-- يتم تشغيل هذه الأوامر بحذر بعد التأكد من عمل الفهارس الجديدة

/*
-- أمثلة لحذف الفهارس القديمة (يتم تفعيلها عند الحاجة)
DROP INDEX CONCURRENTLY IF EXISTS idx_photos_old_sort;
DROP INDEX CONCURRENTLY IF EXISTS idx_videos_old_sort;
DROP INDEX CONCURRENTLY IF EXISTS idx_locations_old_sort;
*/

-- ===== 📈 إحصائيات الفهارس =====

-- دالة لعرض إحصائيات استخدام الفهارس الجديدة
CREATE OR REPLACE FUNCTION get_index_usage_stats()
RETURNS TABLE (
    index_name TEXT,
    table_name TEXT,
    index_size TEXT,
    times_used BIGINT,
    tuples_read BIGINT,
    tuples_fetched BIGINT,
    efficiency_ratio DECIMAL(5,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.indexrelname::TEXT as index_name,
        i.relname::TEXT as table_name,
        pg_size_pretty(pg_relation_size(i.indexrelid)) as index_size,
        i.idx_scan as times_used,
        i.idx_tup_read as tuples_read,
        i.idx_tup_fetch as tuples_fetched,
        CASE 
            WHEN i.idx_tup_read > 0 THEN 
                ROUND((i.idx_tup_fetch::DECIMAL / i.idx_tup_read * 100), 2)
            ELSE 0 
        END as efficiency_ratio
    FROM pg_stat_user_indexes i
    WHERE i.indexrelname LIKE '%_optimized'
    ORDER BY i.idx_scan DESC, pg_relation_size(i.indexrelid) DESC;
END;
$$ LANGUAGE plpgsql;

-- ===== 📋 تعليقات على الفهارس =====

COMMENT ON INDEX idx_photos_location_date_optimized IS 'فهرس محسن للترتيب حسب الموقع والتاريخ - الأكثر استخداماً';
COMMENT ON INDEX idx_photos_date_location_optimized IS 'فهرس محسن للترتيب حسب التاريخ والموقع';
COMMENT ON INDEX idx_photos_user_date_optimized IS 'فهرس محسن للترتيب حسب المستخدم والتاريخ';
COMMENT ON INDEX idx_photos_size_date_optimized IS 'فهرس محسن للترتيب حسب الحجم والتاريخ';
COMMENT ON INDEX idx_videos_duration_date_optimized IS 'فهرس محسن للترتيب حسب مدة الفيديو والتاريخ';

-- ===== ✅ تأكيد إنشاء الفهارس =====

-- دالة للتحقق من إنشاء جميع الفهارس المطلوبة
CREATE OR REPLACE FUNCTION verify_optimized_indexes()
RETURNS TABLE (
    index_name TEXT,
    table_name TEXT,
    status TEXT,
    size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        indexname::TEXT,
        tablename::TEXT,
        CASE 
            WHEN indexname IS NOT NULL THEN 'موجود ✅'
            ELSE 'مفقود ❌'
        END as status,
        COALESCE(pg_size_pretty(pg_relation_size(indexname::regclass)), 'غير محدد') as size
    FROM (
        VALUES 
            ('idx_photos_location_date_optimized', 'photos'),
            ('idx_photos_date_location_optimized', 'photos'),
            ('idx_photos_user_date_optimized', 'photos'),
            ('idx_photos_size_date_optimized', 'photos'),
            ('idx_videos_location_date_optimized', 'videos'),
            ('idx_videos_date_location_optimized', 'videos'),
            ('idx_videos_user_date_optimized', 'videos'),
            ('idx_videos_duration_date_optimized', 'videos')
    ) AS expected_indexes(index_name, table_name)
    LEFT JOIN pg_indexes ON pg_indexes.indexname = expected_indexes.index_name
    ORDER BY expected_indexes.table_name, expected_indexes.index_name;
END;
$$ LANGUAGE plpgsql;
